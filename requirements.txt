# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile requirements.in
#
aiohttp[speedups]==3.9.5
alembic
alembic-utils
alembic-postgresql-enum
aiosignal==1.3.1
    # via aiohttp
alabaster==0.7.16
    # via sphinx
amqp==5.2.0
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.4.0
    # via
    #   httpx
    #   openai
asyncpg
beautifulsoup4
Babel
cachetools
celery
circuitbreaker
dash[compress]
dash-ag-grid==31.2.0
    # via -r requirements.in
dash_bootstrap_components
dash-daq==0.5.0
    # via -r requirements.in
dash-extensions==1.0.16
    # via -r requirements.in
dash-iconify
dash-mantine-components
dependency-injector
    # via -r requirements.in
dill==0.3.8
    # via multiprocess
diskcache==5.6.3
editorconfig==0.12.4
    # via jsbeautifier
et-xmlfile==1.1.0
    # via openpyxl
exceptiongroup==1.2.1
    # via
    #   anyio
    #   trio
    #   trio-websocket
flask-caching==2.3.0
    # via dash-extensions
flask-login==0.6.3
    # via -r requirements.in
flask-principal==0.4.0
    # via -r requirements.in
flask-session[redis]
    # via -r requirements.in
livereload==2.7.0
markupsafe==2.1.5
    # via
    #   jinja2
    #   werkzeug
    #   wtforms
more-itertools==10.3.0
    # via dash-extensions
msal==1.29.0
    # via -r requirements.in
msgspec==0.18.6
    # via flask-session
multidict==6.0.5
    # via
    #   aiohttp
    #   yarl
multipledispatch==1.0.0
    # via -r requirements.in
multiprocess==0.70.16
    # via -r requirements.in
odfpy==1.4.1
    # via -r requirements.in
openai==1.35.3
    # via -r requirements.in
openpyxl==3.1.4
    # via -r requirements.in
orjson==3.10.5
    # via -r requirements.in
pandas==2.2.2
    # via
    #   -r requirements.in
    #   statsmodels
patsy==0.5.6
    # via statsmodels
pillow
    # via python-pptx
plotly==5.22.0
    # via
    #   -r requirements.in
    #   dash
prompt-toolkit==3.0.47
    # via click-repl
psycopg2-binary==2.9.9
    # via -r requirements.in
pycparser==2.22
    # via cffi

pykeepass
    # via -r requirements.in
pyodbc==5.1.0
    # via -r requirements.in
aioodbc
pyotp==2.9.0
    # via -r requirements.in
python-dotenv
python-magic
python-pptx
psutil
qrcode==7.4.2
    # via -r requirements.in
retrying==1.3.4
    # via dash
ruff==0.4.10
    # via dash-extensions
scipy==1.13.1
    # via statsmodels
selenium==4.22.0
    # via -r requirements.in
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
    #   openai
    #   trio
sortedcontainers==2.4.0
    # via trio
sqlalchemy[asyncio]
sqlalchemy-citext
sqlalchemy-json
sqlalchemy-utils
statsmodels

urllib3[socks]==2.2.2
    # via
    #   requests
    #   selenium

webdriver-manager==4.0.1
    # via -r requirements.in
websocket-client==1.8.0
    # via selenium
wsproto==1.2.0
    # via trio-websocket
xlrd==2.0.1
    # via -r requirements.in
xlsxwriter==3.2.0
    # via python-pptx
xmltodict==0.13.0
    # via -r requirements.in
zipp==3.19.2
    # via importlib-metadata