import asyncio
import os
import time
from abc import ABC, abstractmethod
from contextlib import contextmanager, AbstractContextManager, asynccontextmanager
from typing import Optional, Callable
import pyodbc
from dependency_injector import containers, providers
from pykeepass import PyKeePass
from pykeepass.exceptions import CredentialsError, HeaderChecksumError, PayloadChecksumError
from sqlalchemy import create_engine, inspect
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import scoped_session, sessionmaker, Session
from sqlalchemy.sql.expression import text

from data.custom_logger import MyLogger

pyodbc.pooling = False


class KpEnvConfigContainer(containers.DeclarativeContainer):
    """
    Container for extracting configuration values from environment variables.

    Uses `Dependency Injector` to define and extract configuration values from environment variables.

    Example usage:

    .. code-block:: python

        from dependency_injector import containers, providers

        class KpEnvConfigContainer(containers.DeclarativeContainer):
            config = providers.Configuration()
            db_path = config.db_path.from_env("DATABASE_PATH")
            master_keyfile = config.master_keyfile.from_env("MASTER_KEYFILE")
            master_password = config.master_password.from_env("MASTER_PASSWORD")

        my_container = KpEnvConfigContainer()

    This container can then be used to extract configuration values from environment variables:

    .. code-block:: python

        # Retrieve the database path configuration value
        db_path = my_container.config.db_path()

        # Retrieve the master keyfile configuration value
        master_keyfile = my_container.config.master_keyfile()

        # Retrieve the master password configuration value
        master_password = my_container.config.master_password()

    """

    config = providers.Configuration()
    db_path = config.db_path.from_env("DATABASE_PATH")
    master_keyfile = config.master_keyfile.from_env("MASTER_KEYFILE")
    master_password = config.master_password.from_env("MASTER_PASSWORD")


class IKeyPassPasswordManager(ABC):
    @abstractmethod
    def get_url(self, entry_name: str) -> str:
        pass

    @abstractmethod
    def get_password(self, entry_name: str) -> str:
        pass

    @abstractmethod
    def get_username(self, entry_name: str) -> str:
        pass

    @abstractmethod
    def get_custom_string(self, entry_name: str, key: str) -> str:
        pass

class KPDatabaseOpener(AbstractContextManager):

    def __init__(self, database_path: str, master_password: Optional[str] = None, master_keyfile: Optional[str] = None):
        if master_password is None and master_keyfile is None:
            raise ValueError("At least one of master_password or keyfile is required.")
        self.database_path = database_path
        self.master_password = master_password
        self.master_keyfile = master_keyfile
        self._instance_counter = 1

    def __enter__(self):
        try:
            self.kp = PyKeePass(self.database_path, password=self.master_password, keyfile=self.master_keyfile)
            return self.kp
        except CredentialsError:
            raise Exception("Invalid master password or keyfile.")
        except (HeaderChecksumError, PayloadChecksumError):
            raise Exception("The KeePass database appears to be corrupt.")

    def __exit__(self, exc_type, exc_value, traceback):
        pass



class KeePassPasswordManager(IKeyPassPasswordManager):
    def __init__(self,
                 # database_opener: providers.Callable[[], ContextManager[PyKeePass]]
                 database_opener: KPDatabaseOpener
                 ):
        self.database_opener = database_opener

    def get_password(self, entry_name: str) -> str:
        with self.database_opener as kp:
            entry = kp.find_entries_by_title(entry_name, first=True)
            if entry:
                return entry.password
            else:
                return ''

    def get_username(self, entry_name: str) -> str:
        with self.database_opener as kp:
            entry = kp.find_entries_by_title(entry_name, first=True)
            if entry:
                return entry.username
        return ''

    def get_url(self, entry_name: str) -> str:
        with self.database_opener as kp:
            entry = kp.find_entries_by_title(entry_name, first=True)
            if entry:
                return entry.url
        return ''

    def get_custom_string(self, entry_name: str, key: str) -> str:
        with self.database_opener as kp:
            entry = kp.find_entries_by_title(entry_name, first=True)
            if entry:
                return entry.get_custom_property(key)
        return ''



class MyKeePassContainer(containers.DeclarativeContainer):
    """
    Container for creating instances of `KPDatabaseOpener` and `KeePassPasswordManager`.

    Uses `KpEnvConfigContainer` to extract configuration values from environment variables.

    Example usage:

    .. code-block:: python

        from dependency_injector import containers, providers
        from my_package import KPDatabaseOpener, KeePassPasswordManager

        class KpEnvConfigContainer(containers.DeclarativeContainer):
            config = providers.Configuration()
            db_path = config.db_path.from_env("DATABASE_PATH")
            master_keyfile = config.master_keyfile.from_env("MASTER_KEYFILE")
            master_password = config.master_password.from_env("MASTER_PASSWORD")

        class MyKeePassContainer(containers.DeclarativeContainer):
            config_container = KpEnvConfigContainer()

            kp_database = providers.Factory(
                KPDatabaseOpener,
                database_path=config_container.config.db_path(),
                master_password=config_container.config.master_password(),
                master_keyfile=config_container.config.master_keyfile()
            )

            password_manager = providers.Factory(
                KeePassPasswordManager,
                database_opener=kp_database
            )

    This container can then be used to create instances of `KPDatabaseOpener` and `KeePassPasswordManager`.

    .. code-block:: python

        my_container = MyKeePassContainer()

        # Create an instance of KPDatabaseOpener
        kp_database = my_container.kp_database()

        # Create an instance of KeePassPasswordManager
        password_manager = my_container.password_manager()

    """
    # wiring_config = containers.WiringConfiguration(modules=["callbacks"])
    config_container = KpEnvConfigContainer()

    kp_database = providers.Factory(
        # open_kp_database,
        KPDatabaseOpener,
        database_path=config_container.config.db_path(),
        master_password=config_container.config.master_password(),
        master_keyfile=config_container.config.master_keyfile()
    )

    password_manager = providers.Factory(
        KeePassPasswordManager,
        database_opener=kp_database
    )
    # wiring_config = containers.WiringConfiguration(modules=["callbacks"])


my_keepass = MyKeePassContainer()


class PostgresConnectionURI:
    """PostgreSQL connection URI builder using KeePass credentials and psycopg3."""

    def __init__(self, password_manager: IKeyPassPasswordManager, read_only: bool = True):
        self.password_manager = password_manager
        self.entry_name = 'JIRA_RO' if read_only else 'JIRA_RW'

    def get_url(self) -> URL:
        """Get PostgreSQL connection URL using SQLAlchemy URL.create with psycopg3."""
        return URL.create(
            drivername="postgresql+psycopg",
            username=self.password_manager.get_username(self.entry_name),
            password=self.password_manager.get_password(self.entry_name),
            host=self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_NAME"),
            port=self._get_port(),
            database=self.password_manager.get_custom_string(self.entry_name, "DB_NAME")
        )

    def get_async_url(self) -> URL:
        """Get async PostgreSQL connection URL (using asyncpg for now)."""
        return URL.create(
            drivername="postgresql+asyncpg",
            username=self.password_manager.get_username(self.entry_name),
            password=self.password_manager.get_password(self.entry_name),
            host=self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_NAME"),
            port=self._get_port(),
            database=self.password_manager.get_custom_string(self.entry_name, "DB_NAME")
        )

    def _get_port(self) -> int:
        """Get database port based on entry type."""
        if self.entry_name == 'JIRA_RO':
            port_str = self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_RO_PORT")
        elif self.entry_name == 'JIRA_RW':
            port_str = self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_RW_PORT")
        else:
            port_str = '5432'
        return int(port_str) if port_str else 5432


class DbConnectionURI:
    """
    DbConnectionURI is a class that provides an easy way to create a connection value for a Postgres database using
    credentials stored in KeePass.

    Parameters:
        read_only (bool):
            - If True, a connection value for a read-only connection will be created.
            - If False, a connection value for a read-write connection will be created.

    Attributes:
        - pg_driver_name (str): The Postgres driver name.
        - username (str): The username for the database connection.
        - password (str): The password for the database connection, with special characters encoded.
        - db_server_name (str): The name of the database server.
        - db_server_port (str): The port number of the database server.
        - db_name (str): The name of the database.

    Methods:
        - set_connection_string(): Sets the connection value using the class attributes.
        - get_conn_str(): Returns the connection value.
                          If the connection value hasn't been set yet, it will be set first using set_connection_string().

    **Example usage**::

        # Create a read-only connection value
        conn = DbConnectionURI(read_only=True)
        conn_str = conn.get_conn_str()

        # Create a read-write connection value
        conn = DbConnectionURI(read_only=False)
        conn_str = conn.get_conn_str()
    """

    password_manager = MyKeePassContainer().password_manager()

    def __init__(self, password_manager: IKeyPassPasswordManager, read_only: bool = True) -> None:
        """
        Initializes a DbConnectionURI object.

        Parameters:
            - password_manager (IKeyPassPasswordManager): An instance of IKeyPassPasswordManager implementing the required methods for retrieving passwords and custom strings.
            - read_only (bool, optional): Specifies whether a read-only connection value should be created. Defaults to True.

        Returns:
            None

        Example:
            >>> password_manager = MyKeePassContainer().password_manager()
            >>> conn = DbConnectionURI(password_manager, read_only=True)


        This example demonstrates how to create a DbConnectionURI object with a specified password manager
        and whether a read-only connection value should be created. The connection value is not set until
        the `set_connection_string` method is called.
        """
        self.password_manager = password_manager
        self.entry_name = 'JIRA_RO' if read_only else 'JIRA_RW'
        self._connection_string = None
        self.set_connection_string()

    def __repr__(self):
        """
        Returns a value representation of the object. The representation includes the class name followed by the masked representation
        of the connection value. The password portion of the connection value is replaced with asterisk for security reasons.

        Returns:
            str: The value representation of the object.

        Example:
            .. code-block:: python

                conn = DbConnectionURI(read_only=True)
                repr(conn)

        This example demonstrates how to create an instance of DbConnectionURI with read-only access and retrieve its value representation.
        """
        conn_str = self._connection_string
        password_start_index = conn_str.find(':', conn_str.find(':') + 1)
        password_end_index = conn_str.find('@')
        masked_representation = conn_str[:password_start_index] + ':****' + conn_str[password_end_index:]
        return f"{masked_representation}"

    @property
    def pg_driver_name(self):
        return self.password_manager.get_custom_string(self.entry_name, "DB_DRIVER")

    @property
    def username(self):
        return self.password_manager.get_username(self.entry_name)

    @property
    def password(self):
        return self.password_manager.get_password(self.entry_name)

    @property
    def db_server_name(self):
        return self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_NAME")

    @property
    def db_server_port(self):
        if self.entry_name == 'JIRA_RO':
            return self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_RO_PORT")
        elif self.entry_name == 'JIRA_RW':
            return self.password_manager.get_custom_string(self.entry_name, "DB_SERVER_RW_PORT")
        else:
            return 5432

    @property
    def db_name(self):
        return self.password_manager.get_custom_string(self.entry_name, "DB_NAME")

    # define a getting method to set the connection value
    @property
    def pg_db_url(self):
        if self._connection_string is None:
            self.set_connection_string()
        return self._connection_string

    # define a setter method to get the connection value
    def set_connection_string(self):
        # self._connection_string = '{driver_name}://{username}:{password}@{host}:{port}/{database}'.format(
        #     driver_name=self.pg_driver_name,
        #     username=self.username,
        #     password=quote(self.password),
        #     host=self.db_server_name,
        #     port=self.db_server_port,
        #     database=self.db_name
        # )
        self._connection_string = URL.create(
            drivername="postgresql+psycopg",
            username=self.username,
            password=self.password,
            host=self.db_server_name,
            port=self.db_server_port,
            database=self.db_name,
        )

    # def get_db_schema(self) -> list[str]:
    #     # logic to retrieve the schema names from the database
    #     schema_names = ['plat', 'cpp', 'acq']
    #     return schema_names


class Database:
    """
    A class for managing a SQLAlchemy database connection and session for postgres DB.
    This class provides methods for
    # initializing a database connection
    # creating the database tables
    # managing database sessions using SQLAlchemy's scoped session and sessionmaker utilities.

    Example usage:
        db = Database('postgresql://user:password@localhost:5432/mydatabase')
        db.create_database()

        with db.session() as session:
            user = User(name='Alice')
            session.add(user)
            session.commit()
    """

    # _instance = None
    # _initialized = False
    #
    # def __new__(cls, *args, **kwargs):
    #     if cls._instance is None:
    #         cls._instance = super().__new__(cls)
    #     return cls._instance

    def __init__(self, db_conn_uri: DbConnectionURI, schema_name: Optional[str] = None) -> None:
        self.server_name = db_conn_uri.db_server_name
        self.server_port = db_conn_uri.db_server_port
        self._schema_name = schema_name
        start_time = time.perf_counter_ns()
        self._engine = create_engine(db_conn_uri.pg_db_url, echo=False, future=True)
        # my_logger.info(f"Engine id = {id(self._engine)}")
        self._engine = self._engine.execution_options(schema_translate_map={None: schema_name})
        self._session_factory = scoped_session(
            sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self._engine, future=True,
            ),
        )

        self._db_connection_time = time.perf_counter_ns() - start_time
        self._session_connection_time = None


    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        session: Session = self._session_factory()
        if exc_val:
            session.rollback()
        else:
            session.commit()
        session.close()

    def get_engine(self):
        return self._engine

    def get_db_schema(self) -> list:
        # inspector = inspect(self._engine, raiseerr=True)
        # return inspector.get_schema_names()
        return os.getenv("PG_SCHEMA_LIST", "plat,public,ccp,cpp").split(",")


    @contextmanager
    # def session(self) -> Callable[..., AbstractContextManager[Session]]:
    def session(self) -> Callable:
        """
        A context manager that provides a SQLAlchemy session.
        This method returns a context manager that can be used with a `with` statement to manage a SQLAlchemy session.
        The session is created using the session factory initialized in the `Database` class constructor.

        Example usage:
            with db.session() as session:
                user = User(name='Alice')
                session.add(user)
                session.commit()

        Note: The session is automatically committed at the end of the `with` block, unless an exception is raised,
        in which case the session is rolled back.

        Returns:
            A context manager that yields a SQLAlchemy session object.

        Raises:
            Any exceptions that are raised within the `with` block are propagated to the calling code,
            after the session is rolled back.
        """
        my_logger = MyLogger().get_logger()
        session: Session = self._session_factory()
        try:
            yield session
        except Exception:
            my_logger.exception("Session rollback because of exception")
            session.rollback()
            raise
        finally:
            session.close()


class AsyncDatabase:
    """Async database manager."""

    def __init__(self, connection_url: URL, schema_name: Optional[str] = None):
        self._schema_name = schema_name
        self._connection_url = connection_url

        self._engine = create_async_engine(
            connection_url,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=3600
        )

        if schema_name:
            self._engine = self._engine.execution_options(
                schema_translate_map={None: schema_name}
            )

        self._session_factory = async_sessionmaker(
            bind=self._engine,
            expire_on_commit=False
        )

    @asynccontextmanager
    async def session(self):
        """Async context manager for database sessions."""
        async with self._session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise

    async def dispose(self):
        """Dispose of the async engine."""
        await self._engine.dispose()


class AsyncPostgresDatabase:
    def __init__(self, user, password, host, port, database):
        self.database_url = f'postgresql+psycopg_async://{user}:{password}@{host}:{port}/{database}'
        self.engine = create_async_engine(self.database_url, echo=True)
        self.async_session = async_sessionmaker(self.engine, expire_on_commit=False)

    @asynccontextmanager
    async def get_session(self):
        async with self.async_session() as session:
            yield session

    async def dispose(self):
        await self.engine.dispose()


class MSSQLDatabase:
    server_name: str = None
    port: int = 1433

    def __init__(
            self, password_manager: IKeyPassPasswordManager,
    ):
        driver_names = [sql_driver for sql_driver in pyodbc.drivers() if sql_driver.endswith(' for SQL Server')]
        self.server_name = password_manager.get_url('ISC PM DB')

        self.connection_url = URL.create(
            "mssql+pyodbc",
            username=password_manager.get_username('ISC PM DB'),
            password=password_manager.get_password('ISC PM DB'),
            host=password_manager.get_url('ISC PM DB'),
            port=self.port,
            database=password_manager.get_custom_string('ISC PM DB', 'DB_NAME_TS'),
            query={
                "driver": driver_names[0],
                "TrustServerCertificate": "yes",
                "authentication": "SqlPassword",
                # "UID": password_manager.get_username('ISC PM DB'),
                # "PWD": password_manager.get_password('ISC PM DB'),
                "ApplicationIntent": "ReadOnly",  # Other option is ReadWrite
                'Remote Query Timeout': '5',
                'Connection Timeout': '5',
            },
        )

    def get_server_name(self):
        return self.server_name

    def get_server_port(self):
        return self.port

    @contextmanager
    def create_engine(self, fast_executemany=True):
        engine = None
        try:
            engine = create_engine(
                self.connection_url, echo=False, fast_executemany=fast_executemany,
                pool_pre_ping=True
            )
            yield engine

        # except sqlalchemy.exc.InterfaceError as e:
        #     print(f"checking interface error. type = {e.orig}. value = {e.orig}")
        #     if isinstance(e.orig, pyodbc.InterfaceError):
        #         error_code = e.orig.args[0]
        #         print(error_code)
        #     raise e
        # except DBAPIError as e:
        #     if isinstance(e.orig, pyodbc.Error):
        #         error_code = e.orig.args[0]
        #         print(f"Error is DBAPIError {error_code}")
        #     raise e
        except Exception as e:
            my_logger = MyLogger().get_logger()
            my_logger.exception(e)
            raise e
        finally:
            # Close the connection after using it
            if engine:
                engine.dispose()


class AsyncMSSQLDatabase:
    server_name: str = None
    port: int = 1433

    def __init__(self, password_manager):
        driver_names = [sql_driver for sql_driver in pyodbc.drivers() if sql_driver.endswith(' for SQL Server')]
        self.server_name = password_manager.get_url('ISC PM DB')

        self.connection_url = URL.create(
            "mssql+aioodbc",
            username=password_manager.get_username('ISC PM DB'),
            password=password_manager.get_password('ISC PM DB'),
            host=password_manager.get_url('ISC PM DB'),
            port=self.port,
            database=password_manager.get_custom_string('ISC PM DB', 'DB_NAME_TS'),
            query={
                "driver": driver_names[0],
                "TrustServerCertificate": "yes",
                "authentication": "SqlPassword",
                "ApplicationIntent": "ReadOnly",
                'Remote Query Timeout': '5',
                'Connection Timeout': '5',
            },
        )

    def get_server_name(self):
        return self.server_name

    def get_server_port(self):
        return self.port

    @asynccontextmanager
    async def create_async_engine(self, fast_executemany=True):
        engine = None
        print(f"async url {self.connection_url}")
        try:
            engine = create_async_engine(
                self.connection_url, echo=False, future=True,
            )
            yield engine
        except Exception as e:
            my_logger = MyLogger().get_logger()
            my_logger.exception(e)
            raise e
        finally:
            if engine:
                await engine.dispose()


def create_schema_dict(read_only: bool, schema_names: list) -> providers.Singleton:
    return providers.Singleton(
        providers.Dict,
        **{
            schema_name: providers.Singleton(
                Database,
                schema_name=schema_name,
                db_conn_uri=providers.Singleton(
                    DbConnectionURI,
                    password_manager=providers.Callable(my_keepass.password_manager),
                    read_only=read_only
                )
            )
            for schema_name in schema_names[0]
        }
    )


class DatabaseContainer(containers.DeclarativeContainer):
    # TODO Use providers.Aggregate instead of providers.Singleton and providers.Dict
    # TODO Use providers.Selector to select between ro and rw connection
    config = providers.Configuration()
    config_schema = providers.Configuration()

    password_manager = providers.Singleton(
        KeePassPasswordManager,
        database_path=providers.Configuration().keepass.db_path.from_env("DATABASE_PATH"),
        master_password=providers.Configuration().keepass.master_password.from_env("MASTER_PASSWORD"),
        master_keyfile=providers.Configuration().keepass.master_keyfile.from_env("MASTER_KEYFILE")
    )

    # New addition NOT WORKING
    # my_keepass_dependency = providers.Dependency(instance_of=KeePassPasswordManager)
    #

    # DB_SCHEMA_NAMES_LIST = ['public', 'plat', 'ccp']
    # tb = traceback.extract_stack()
    # line_number = tb[-2].lineno
    # print(tb)

    db_ms_engine = providers.Singleton(
        MSSQLDatabase,
        password_manager=providers.Callable(my_keepass.password_manager)
    )
    db_ms_asynch_engine = providers.Singleton(
        AsyncMSSQLDatabase,
        password_manager=providers.Callable(my_keepass.password_manager)
    )

    db_conn_uri_ro = providers.Singleton(
        DbConnectionURI,
        password_manager=providers.Callable(my_keepass.password_manager),
        # password_manager=my_keepass_dependency.provided.password_manager,
        read_only=True
    )

    postgres_conn_uri_ro = providers.Singleton(
        PostgresConnectionURI,
        password_manager=providers.Callable(my_keepass.password_manager),
        read_only=True
    )

    db_ro = providers.Singleton(Database, db_conn_uri=db_conn_uri_ro)

    DB_SCHEMA_NAMES_LIST = providers.Singleton(
        providers.List, [
            # schema_name for schema_name in os.getenv("PG_SCHEMA_LIST", "plat,public,ccp,cpp").split(",")
            schema_name for schema_name in db_ro().get_db_schema() if schema_name != 'information_schema'
        ]
    )

    db_ro_schemas = providers.Singleton(create_schema_dict, read_only=True, schema_names=DB_SCHEMA_NAMES_LIST())
    db_rw_schemas = providers.Singleton(create_schema_dict, read_only=False, schema_names=DB_SCHEMA_NAMES_LIST())

    db_connections = providers.Dict({
        'ro': db_ro_schemas,
        'rw': db_rw_schemas,
    })

    schema_list = db_ro().get_db_schema()
    schema_list.extend(['pg_catalog'])
    # Async PostgreSQL Database instances
    async_postgres_ro = providers.Singleton(
        AsyncDatabase,
        connection_url=providers.Callable(postgres_conn_uri_ro.provided.get_async_url),
        schema_name=config.schema_name
    )

    db_ro_schema_selector = providers.Selector(
        config_schema.schema_name,
        **{
            schema_name: providers.Singleton(
                Database,
                schema_name=schema_name,
                db_conn_uri=providers.Singleton(
                    DbConnectionURI,
                    # password_manager=my_keepass_dependency.provided.password_manager,
                    password_manager=providers.Callable(my_keepass.password_manager),
                    read_only=True
                )
            )
            for schema_name in schema_list
        },
        **{
            f"{schema_name}_async": providers.Singleton(
                AsyncDatabase,
                connection_url=providers.Singleton(
        PostgresConnectionURI,
        password_manager=providers.Singleton(
        KeePassPasswordManager,
        database_path=providers.Configuration().keepass.db_path.from_env("DATABASE_PATH"),
        master_password=providers.Configuration().keepass.master_password.from_env("MASTER_PASSWORD"),
        master_keyfile=providers.Configuration().keepass.master_keyfile.from_env("MASTER_KEYFILE")
    ),
        read_only=True
    ),
                schema_name=schema_name,
            )
            for schema_name in schema_list
        },
        **{
            "mssql": providers.Singleton(
                MSSQLDatabase,
                # password_manager=my_keepass_dependency.provided.password_manager,
                password_manager=providers.Callable(my_keepass.password_manager)
            )
        },
        **{
            "mssql_async": providers.Singleton(
                AsyncMSSQLDatabase,
                # password_manager=my_keepass_dependency.provided.password_manager,
                password_manager=providers.Callable(my_keepass.password_manager)
            )
        }
    )

    db_rw_schema_selector = providers.Selector(
        config_schema.schema_name,
        **{
            schema_name: providers.Singleton(
                Database,
                schema_name=schema_name if schema_name not in [""] else 'public',
                db_conn_uri=providers.Singleton(
                    DbConnectionURI,
                    password_manager=providers.Callable(my_keepass.password_manager),
                    # password_manager=my_keepass_dependency.provided.password_manager,
                    read_only=False
                )
            )
            for schema_name in schema_list
        },
        **{
            "mssql": providers.Singleton(
                MSSQLDatabase,
                # password_manager=my_keepass_dependency.provided.password_manager,
                password_manager=providers.Callable(my_keepass.password_manager)
            )
        },
        **{
            "mssql_async": providers.Singleton(
                AsyncMSSQLDatabase,
                # password_manager=my_keepass_dependency.provided.password_manager,
                password_manager=providers.Callable(my_keepass.password_manager)
            )
        }
    )

    db_conn_provider = providers.Selector(
        config.ro_or_rw,
        ro=db_ro_schema_selector,
        rw=db_rw_schema_selector
    )

    db_conn_provider_async = providers.Selector(
        config.ro_or_rw,
        ro=db_ro_schema_selector,
        rw=db_rw_schema_selector,
    )

    # DO NOT DELETE. FOR REFERENCE 5-JUL-2023
    # Additional code
    # db_ro_schemas = providers.Dict(
    #     {
    #         'plat': providers.ThreadSafeSingleton(Database, schema_name='plat', db_conn_uri=db_conn_uri_ro),
    #         'cpp': providers.ThreadSafeSingleton(Database, schema_name='cpp', db_conn_uri=db_conn_uri_ro),
    #         'public': providers.ThreadSafeSingleton(Database, schema_name='public', db_conn_uri=db_conn_uri_ro),
    #     }
    # )

    # db_ro_schemas_dict = providers.Dict(
    #     {
    #         schema_name: providers.Singleton(
    #             Database, schema_name=schema_name,
    #             db_conn_uri=providers.Singleton(
    #                 DbConnectionURI,
    #                 password_manager=providers.Callable(my_keepass.password_manager),
    #                 read_only=True
    #             )
    #         )
    #         # for schema_name in db_ro().get_db_schema() if schema_name not in ['information_schema']
    #         for schema_name in DB_SCHEMA_NAMES_LIST()()
    #     }
    # )
    # Check if callable approach is working
    # db_ro_schemas = providers.Singleton(
    #     providers.Dict,
    #     **{
    #         schema_name: providers.Singleton(
    #             Database, schema_name=schema_name,
    #             db_conn_uri=providers.Singleton(
    #                 DbConnectionURI,
    #                 password_manager=providers.Callable(my_keepass.password_manager),
    #                 read_only=True
    #             )
    #         )
    #         # for schema_name in db_ro().get_db_schema() if schema_name not in ['information_schema']
    #         for schema_name in DB_SCHEMA_NAMES_LIST
    #     }
    # )

    # db_rw_schemas_dict = providers.Dict(
    #     {
    #         schema_name: providers.Singleton(
    #             Database, schema_name=schema_name,
    #             db_conn_uri=providers.Singleton(
    #                 DbConnectionURI,
    #                 password_manager=providers.Callable(my_keepass.password_manager),
    #                 read_only=False
    #             )
    #         )
    #         # for schema_name in db_ro().get_db_schema() if schema_name not in ['information_schema']
    #         for schema_name in DB_SCHEMA_NAMES_LIST()()
    #     }
    # )

    # db_rw_schemas = providers.Singleton(
    #     providers.Dict,
    #     **{
    #         schema_name: providers.Singleton(
    #             Database, schema_name=schema_name,
    #             db_conn_uri=providers.Singleton(
    #                 DbConnectionURI,
    #                 password_manager=providers.Callable(my_keepass.password_manager),
    #                 read_only=False
    #             )
    #         )
    #         # for schema_name in db_ro().get_db_schema() if schema_name not in ['information_schema']
    #         for schema_name in DB_SCHEMA_NAMES_LIST_DUMMY
    #     }
    # )


class AppContainer(containers.DeclarativeContainer):
    # my_keepass = providers.Singleton(MyKeePassContainer)
    session_factory = providers.Singleton(DatabaseContainer)
    wiring_config = containers.WiringConfiguration(
        modules=[
            "callbacks", "callback.wd", "callback.callback_version_async",
            "callback.validate_totp", "callback.callback_weekly_status_report",
        ]
    )


async def main():
    async with async_db.session() as session:
        result = await session.execute(text("SELECT 1"))
        print(result.scalar_one())

if __name__ == "__main__":
    start_time = time.time()

    container = AppContainer()
    container.wire(modules=[__name__])
    print(type(container))
    factory = container.session_factory()
    print(f"custom_container: Time Taken: {time.time() - start_time} seconds")
    maker_time = time.time()
    factory.config.override({'ro_or_rw': 'ro'})
    factory.config_schema.override({'schema_name': 'plat'})
    print(dir(factory.db_conn_provider()))
    print(factory.db_conn_provider().session())
    print(f"time taken {time.time() - maker_time }")
    factory.config_schema.override({'schema_name': 'plat_async'})
    async_db = factory.db_conn_provider_async()
    asyncio.run(main())



