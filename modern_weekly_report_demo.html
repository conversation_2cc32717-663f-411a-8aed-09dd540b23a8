<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Weekly Report Design Demo</title>
    <link rel="stylesheet" href="assets/css/modern_weekly_report.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@iconify/iconify@3.1.1/dist/iconify.min.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        /* Demo controls */
        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            z-index: 1000;
        }
        
        .demo-controls h3 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #262626;
        }
        
        .demo-controls button {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .demo-controls button:hover {
            background: #f5f5f5;
            border-color: #1890ff;
        }
        
        /* Mock form elements */
        .mock-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .mock-dropdown {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            margin-bottom: 8px;
        }
        
        .config-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 4px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .risk-row {
            display: grid;
            grid-template-columns: 120px 2fr 100px 100px 2fr 2fr 80px;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 4px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .mock-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 60px;
        }
        
        .mock-checkbox {
            width: 20px;
            height: 20px;
            margin: auto;
        }
        
        @media (max-width: 768px) {
            .config-row {
                grid-template-columns: 1fr;
            }
            
            .risk-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h3>Demo Controls</h3>
        <button onclick="switchTab('cookie')">Cookie Tab</button>
        <button onclick="switchTab('jazz')">Jazz Tab</button>
        <button onclick="switchTab('risk')">Risk Tab</button>
        <button onclick="addConfigRow()">Add Config Row</button>
        <button onclick="addRiskRow()">Add Risk Row</button>
        <button onclick="simulateGeneration()">Simulate Generation</button>
        <button onclick="simulateMobile()">Mobile View</button>
    </div>

    <div class="modern-weekly-report-layout">
        <!-- Header Section -->
        <div class="report-header">
            <div class="header-content">
                <div class="title-section">
                    <iconify-icon icon="mdi:file-document-multiple" class="header-icon"></iconify-icon>
                    <div>
                        <h1 class="header-title">Weekly Project Reports</h1>
                        <p class="header-subtitle">Automated generation of comprehensive project status reports</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="modern-button secondary">
                        <iconify-icon icon="mdi:help-circle" class="button-icon"></iconify-icon>
                        Help
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="report-content">
            <!-- Modern Tab System -->
            <div class="modern-tab-container">
                <div class="tab-header">
                    <button class="tab-button active" data-tab="display-cookie" id="cookie-tab-btn">
                        <iconify-icon icon="mdi:cookie" class="tab-icon"></iconify-icon>
                        Cookie Project
                    </button>
                    <button class="tab-button" data-tab="display-jazz" id="jazz-tab-btn">
                        <iconify-icon icon="mdi:music-note" class="tab-icon"></iconify-icon>
                        Jazz Project
                    </button>
                    <button class="tab-button" data-tab="display-risk" id="risk-tab-btn">
                        <iconify-icon icon="mdi:shield-alert" class="tab-icon"></iconify-icon>
                        Risk Register
                    </button>
                </div>
                
                <!-- Tab Content -->
                <div class="tab-content-container">
                    <!-- Cookie Tab Content -->
                    <div class="tab-panel active" id="cookie-tab-panel">
                        <div class="panel-header">
                            <h3 class="panel-title">Cookie Project Configuration</h3>
                            <p class="panel-description">Configure release versions and report parameters for Cookie project</p>
                        </div>
                        
                        <!-- Version Management Section -->
                        <div class="config-section">
                            <div class="section-header">
                                <div>
                                    <iconify-icon icon="mdi:source-branch" class="section-icon"></iconify-icon>
                                    <h4 class="section-title">Version Management</h4>
                                </div>
                                <div class="version-controls">
                                    <button class="modern-button primary small" onclick="addConfigRow()">
                                        <iconify-icon icon="mdi:plus" class="button-icon"></iconify-icon>
                                        Add Version
                                    </button>
                                    <button class="modern-button secondary small" onclick="removeConfigRow()">
                                        <iconify-icon icon="mdi:minus" class="button-icon"></iconify-icon>
                                        Remove Version
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Configuration Grid Header -->
                            <div class="config-grid-header">
                                <div class="grid-header-cell">Release Pattern</div>
                                <div class="grid-header-cell">Release Dropdown</div>
                                <div class="grid-header-cell">Branch Dropdown</div>
                                <div class="grid-header-cell">Report Header</div>
                            </div>
                            
                            <!-- Dynamic Configuration Rows -->
                            <div class="config-grid-content" id="cookie-config-content">
                                <div class="config-row">
                                    <input type="text" class="mock-input" placeholder="v2.1.*" value="v2.1.*">
                                    <select class="mock-dropdown">
                                        <option>v2.1.0</option>
                                        <option>v2.1.1</option>
                                        <option>v2.1.2</option>
                                    </select>
                                    <select class="mock-dropdown">
                                        <option>main</option>
                                        <option>develop</option>
                                        <option>release/2.1</option>
                                    </select>
                                    <input type="text" class="mock-input" placeholder="Cookie Release 2.1" value="Cookie Release 2.1">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Jazz Tab Content -->
                    <div class="tab-panel" id="jazz-tab-panel">
                        <div class="panel-header">
                            <h3 class="panel-title">Jazz Project Configuration</h3>
                            <p class="panel-description">Configure release versions and report parameters for Jazz project</p>
                        </div>
                        
                        <!-- Version Management Section -->
                        <div class="config-section">
                            <div class="section-header">
                                <div>
                                    <iconify-icon icon="mdi:source-branch" class="section-icon"></iconify-icon>
                                    <h4 class="section-title">Version Management</h4>
                                </div>
                                <div class="version-controls">
                                    <button class="modern-button primary small">
                                        <iconify-icon icon="mdi:plus" class="button-icon"></iconify-icon>
                                        Add Version
                                    </button>
                                    <button class="modern-button secondary small">
                                        <iconify-icon icon="mdi:minus" class="button-icon"></iconify-icon>
                                        Remove Version
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Configuration Grid Header -->
                            <div class="config-grid-header">
                                <div class="grid-header-cell">Release Pattern</div>
                                <div class="grid-header-cell">Release Dropdown</div>
                                <div class="grid-header-cell">Branch Dropdown</div>
                                <div class="grid-header-cell">Report Header</div>
                            </div>
                            
                            <!-- Dynamic Configuration Rows -->
                            <div class="config-grid-content" id="jazz-config-content">
                                <div class="config-row">
                                    <input type="text" class="mock-input" placeholder="jazz-v3.*" value="jazz-v3.*">
                                    <select class="mock-dropdown">
                                        <option>jazz-v3.0.0</option>
                                        <option>jazz-v3.0.1</option>
                                        <option>jazz-v3.1.0</option>
                                    </select>
                                    <select class="mock-dropdown">
                                        <option>main</option>
                                        <option>develop</option>
                                        <option>feature/jazz-3.0</option>
                                    </select>
                                    <input type="text" class="mock-input" placeholder="Jazz Release 3.0" value="Jazz Release 3.0">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Risk Register Tab Content -->
                    <div class="tab-panel" id="risk-tab-panel">
                        <div class="panel-header">
                            <h3 class="panel-title">Risk Register Management</h3>
                            <p class="panel-description">Manage project risks, mitigation plans, and contingency strategies</p>
                        </div>
                        
                        <!-- Risk Management Section -->
                        <div class="config-section">
                            <div class="section-header">
                                <div>
                                    <iconify-icon icon="mdi:shield-alert" class="section-icon"></iconify-icon>
                                    <h4 class="section-title">Risk Management</h4>
                                </div>
                                <div class="version-controls">
                                    <button class="modern-button primary small" onclick="addRiskRow()">
                                        <iconify-icon icon="mdi:plus" class="button-icon"></iconify-icon>
                                        Add Risk
                                    </button>
                                    <button class="modern-button secondary small" onclick="removeRiskRow()">
                                        <iconify-icon icon="mdi:minus" class="button-icon"></iconify-icon>
                                        Remove Risk
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Risk Grid Header -->
                            <div class="risk-grid-header">
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:calendar" class="header-icon"></iconify-icon>
                                    Date Raised
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:alert-circle" class="header-icon"></iconify-icon>
                                    Risk Description
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:percent" class="header-icon"></iconify-icon>
                                    Probability
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:impact" class="header-icon"></iconify-icon>
                                    Impact
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:shield-check" class="header-icon"></iconify-icon>
                                    Mitigation Plan
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:backup-restore" class="header-icon"></iconify-icon>
                                    Contingency Plan
                                </div>
                                <div class="risk-header-cell">
                                    <iconify-icon icon="mdi:checkbox-marked" class="header-icon"></iconify-icon>
                                    Select
                                </div>
                            </div>
                            
                            <!-- Dynamic Risk Rows -->
                            <div class="risk-grid-content" id="risk-content">
                                <div class="risk-row">
                                    <input type="date" class="mock-input" value="2024-01-15">
                                    <textarea class="mock-textarea" placeholder="Describe the risk...">Database performance degradation during peak hours</textarea>
                                    <select class="mock-dropdown">
                                        <option>High</option>
                                        <option>Medium</option>
                                        <option>Low</option>
                                    </select>
                                    <select class="mock-dropdown">
                                        <option>High</option>
                                        <option>Medium</option>
                                        <option>Low</option>
                                    </select>
                                    <textarea class="mock-textarea" placeholder="Mitigation plan...">Implement database optimization and monitoring</textarea>
                                    <textarea class="mock-textarea" placeholder="Contingency plan...">Scale database resources and implement caching</textarea>
                                    <input type="checkbox" class="mock-checkbox" checked>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Progress and Actions Section -->
            <div class="progress-section">
                <div class="progress-header">
                    <iconify-icon icon="mdi:progress-check" class="progress-icon"></iconify-icon>
                    <h3 class="progress-title">Report Generation Progress</h3>
                </div>
                
                <!-- Progress Display -->
                <div class="progress-content">
                    <div class="status-display">
                        <p id="paragraph_id" class="status-text">Ready to generate report</p>
                        <div class="progress-chart" style="height: 100px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #8c8c8c;">
                            📊 Progress Chart Would Appear Here
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons-section">
                        <button class="modern-button primary large" id="button_id" onclick="simulateGeneration()">
                            <iconify-icon icon="mdi:play-circle" class="button-icon"></iconify-icon>
                            Generate Report
                        </button>
                        <button class="modern-button secondary large" id="cancel_button_id" disabled>
                            <iconify-icon icon="mdi:stop-circle" class="button-icon"></iconify-icon>
                            Cancel Job
                        </button>
                        <button class="modern-button success large" id="id-download-report">
                            <iconify-icon icon="mdi:download" class="button-icon"></iconify-icon>
                            Download Report
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Log Section -->
            <div class="log-section">
                <div class="log-header">
                    <iconify-icon icon="mdi:text-box-multiple" class="log-icon"></iconify-icon>
                    <h3 class="log-title">Generation Log</h3>
                </div>
                <div id='log' class="log-content">
                    <p class="log-message">Waiting for report generation to start...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
    <script src="assets/js/modern_weekly_report.js"></script>
    <script>
        function switchTab(tabName) {
            const tabMap = {
                'cookie': 'display-cookie',
                'jazz': 'display-jazz',
                'risk': 'display-risk'
            };
            
            if (window.modernWeeklyReport && tabMap[tabName]) {
                window.modernWeeklyReport.switchTab(tabMap[tabName]);
            }
        }

        function addConfigRow() {
            const content = document.getElementById('cookie-config-content');
            const newRow = document.createElement('div');
            newRow.className = 'config-row';
            newRow.innerHTML = `
                <input type="text" class="mock-input" placeholder="v2.2.*">
                <select class="mock-dropdown">
                    <option>Select version...</option>
                </select>
                <select class="mock-dropdown">
                    <option>Select branch...</option>
                </select>
                <input type="text" class="mock-input" placeholder="Report header...">
            `;
            content.appendChild(newRow);
        }

        function addRiskRow() {
            const content = document.getElementById('risk-content');
            const newRow = document.createElement('div');
            newRow.className = 'risk-row';
            newRow.innerHTML = `
                <input type="date" class="mock-input">
                <textarea class="mock-textarea" placeholder="Describe the risk..."></textarea>
                <select class="mock-dropdown">
                    <option>High</option>
                    <option>Medium</option>
                    <option>Low</option>
                </select>
                <select class="mock-dropdown">
                    <option>High</option>
                    <option>Medium</option>
                    <option>Low</option>
                </select>
                <textarea class="mock-textarea" placeholder="Mitigation plan..."></textarea>
                <textarea class="mock-textarea" placeholder="Contingency plan..."></textarea>
                <input type="checkbox" class="mock-checkbox">
            `;
            content.appendChild(newRow);
        }

        function removeConfigRow() {
            const content = document.getElementById('cookie-config-content');
            const rows = content.querySelectorAll('.config-row');
            if (rows.length > 1) {
                rows[rows.length - 1].remove();
            }
        }

        function removeRiskRow() {
            const content = document.getElementById('risk-content');
            const rows = content.querySelectorAll('.risk-row');
            if (rows.length > 1) {
                rows[rows.length - 1].remove();
            }
        }

        function simulateGeneration() {
            if (window.modernWeeklyReport) {
                window.modernWeeklyReport.handleGenerateReport();
                
                // Simulate progress steps
                const steps = [
                    'Initializing report generation...',
                    'Fetching Cookie project data...',
                    'Fetching Jazz project data...',
                    'Processing risk register...',
                    'Generating PowerPoint slides...',
                    'Finalizing report...',
                    'Report generation completed!'
                ];
                
                steps.forEach((step, index) => {
                    setTimeout(() => {
                        window.modernWeeklyReport.addLogMessage(step, 'info');
                        if (index === steps.length - 1) {
                            window.modernWeeklyReport.state.isGenerating = false;
                            window.modernWeeklyReport.updateButtonStates();
                            document.getElementById('paragraph_id').textContent = 'Report generated successfully!';
                        }
                    }, (index + 1) * 1500);
                });
            }
        }

        function simulateMobile() {
            const isMobile = document.body.style.maxWidth === '768px';
            document.body.style.maxWidth = isMobile ? 'none' : '768px';
            if (window.modernWeeklyReport) {
                window.modernWeeklyReport.handleResize();
            }
        }
    </script>
</body>
</html>
