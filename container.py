"""
High-performance database connection factory for multi-module Plotly Dash applications.

This module provides:
- Dependency injection with @inject decorators
- Redis integration for Dash caching
- Proper singleton management across modules
- Intelligent use of providers.Selector for common configurations
- Thread-safe initialization for multi-module applications
"""

import asyncio
import os
import time
import threading
import redis
import pickle
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from functools import lru_cache, wraps
from typing import Dict, Literal, Optional, Union, Any, Callable
import logging
from contextlib import contextmanager

import pyodbc
from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject
from dependency_injector.providers import Error
from pykeepass import PyKeePass
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine, URL
from sqlalchemy.ext.asyncio import AsyncEngine, async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import text

# Configure logging
logger = logging.getLogger(__name__)

DatabaseMode = Literal["ro", "rw"]
DatabaseType = Literal["postgres", "mssql"]


@dataclass(frozen=True)
class ConnectionCredentials:
    """Immutable container for database connection credentials."""

    username: str
    password: str
    host: str
    port: int
    database: str
    driver: Optional[str] = None


@dataclass(frozen=True)
class CacheKey:
    """Cache key for database connections."""

    db_type: DatabaseType
    schema_name: str
    mode: DatabaseMode
    is_async: bool

    def __str__(self) -> str:
        return f"{self.db_type}_{self.schema_name}_{self.mode}_{'async' if self.is_async else 'sync'}"

def format_duration_ns(start_ns: int, end_ns: int) -> str:
    """Format duration in ns with adaptive units."""
    duration = end_ns - start_ns
    if duration >= 1_000_000_000:  # ≥ 1s
        return f"{duration / 1_000_000_000:.3f} s"
    elif duration >= 1_000_000:    # ≥ 1 ms
        return f"{duration / 1_000_000:.3f} ms"
    elif duration >= 1_000:        # ≥ 1 µs
        return f"{duration / 1_000:.3f} µs"
    else:
        return f"{duration} ns"

class PerformantKeePassManager:
    """
    High-performance KeePass credential manager with intelligent caching.

    Thread-safe singleton implementation for multi-module applications.
    """

    _instance: Optional['PerformantKeePassManager'] = None
    _lock = threading.Lock()

    def __init__(self) -> None:
        """Initialize KeePass manager with environment credentials."""
        self._db_path = os.environ.get("DATABASE_PATH")
        self._keyfile = os.environ.get("MASTER_KEYFILE")
        self._master_password = os.environ.get("MASTER_PASSWORD")

        if not self._db_path or not self._keyfile:
            raise ValueError(
                "DATABASE_PATH and MASTER_KEYFILE must be set in environment variables"
            )

        self._keepass_db: Optional[PyKeePass] = None
        self._credential_cache: Dict[str, ConnectionCredentials] = {}
        self._thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="keepass_worker")
        self._initialized = False
        self._init_lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> 'PerformantKeePassManager':
        """Get thread-safe singleton instance."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def _ensure_initialized(self) -> None:
        """Thread-safe initialization of KeePass database."""
        if not self._initialized:
            with self._init_lock:
                if not self._initialized:
                    start_time = time.perf_counter()
                    try:
                        self._keepass_db = PyKeePass(
                            self._db_path,
                            keyfile=self._keyfile,
                            password=self._master_password
                        )
                        self._initialized = True
                        elapsed = (time.perf_counter() - start_time) * 1000
                        logger.info(f"KeePass initialized in {elapsed:.2f} ms")
                    except Exception as e:
                        logger.error(f"Failed to initialize KeePass: {e}")
                        raise

    @lru_cache(maxsize=32)
    def _get_entry_cached(self, entry_name: str) -> Any:
        """Get KeePass entry with LRU caching."""
        self._ensure_initialized()

        if self._keepass_db is None:
            raise RuntimeError("KeePass not initialized")

        entry = self._keepass_db.find_entries(title=entry_name, first=True)
        if not entry:
            raise KeyError(f"Entry '{entry_name}' not found in KeePass")
        return entry

    def get_credentials(self, entry_name: str, schema_name: str, mode: DatabaseMode) -> ConnectionCredentials:
        """
        Retrieve database credentials with caching.

        Args:
            entry_name: Name of the KeePass entry
            schema_name: Database schema name
            mode: Database access mode ('ro' or 'rw')

        Returns:
            ConnectionCredentials object with database connection info
        """
        cache_key = f"{entry_name}_{schema_name}_{mode}"

        # Return cached credentials if available
        if cache_key in self._credential_cache:
            logger.debug(f"Returning cached credentials for {cache_key}")
            return self._credential_cache[cache_key]

        start_time = time.perf_counter()

        try:
            credentials = self._fetch_credentials_sync(entry_name, schema_name, mode)

            # Cache the results
            self._credential_cache[cache_key] = credentials

            elapsed = (time.perf_counter() - start_time) * 1000
            logger.info(f"Retrieved credentials for '{cache_key}' in {elapsed:.2f} ms")

            return credentials

        except Exception as e:
            logger.error(f"Failed to retrieve credentials for {cache_key}: {e}")
            raise

    def _fetch_credentials_sync(self, entry_name: str, schema_name: str, mode: DatabaseMode) -> ConnectionCredentials:
        """Synchronously fetch credentials from KeePass."""
        entry = self._get_entry_cached(entry_name)

        # Determine port field based on mode
        port_field = f"DB_SERVER_{mode.upper()}_PORT"

        return ConnectionCredentials(
            username=entry.username,
            password=entry.password,
            host=entry.get_custom_property("DB_SERVER_NAME"),
            port=int(entry.get_custom_property(port_field)),
            database=entry.get_custom_property("DB_NAME")
        )

    def get_mssql_credentials(self) -> ConnectionCredentials:
        """Get MS SQL credentials."""
        cache_key = "mssql_credentials"

        if cache_key in self._credential_cache:
            return self._credential_cache[cache_key]

        entry = self._get_entry_cached("ISC PM DB")

        # Auto-detect SQL Server ODBC driver
        available_drivers = [d for d in pyodbc.drivers() if d.endswith(" for SQL Server")]
        if not available_drivers:
            raise RuntimeError(
                "No SQL Server ODBC driver found! Please install ODBC Driver 17 or 18 for SQL Server."
            )

        chosen_driver = available_drivers[0]
        logger.info(f"Using ODBC driver: {chosen_driver}")

        credentials = ConnectionCredentials(
            username=entry.username,
            password=entry.password,
            host=entry.get_custom_property("DB_SERVER_NAME"),
            port=int(entry.get_custom_property("DB_SERVER_RW_PORT")),
            database=entry.get_custom_property("DB_NAME_TS"),
            driver=chosen_driver
        )

        self._credential_cache[cache_key] = credentials
        return credentials


class RedisManager:
    """
    Redis manager for Dash application caching with database integration.

    Provides caching for database query results and connection metadata.
    """

    def __init__(
            self,
            redis_host: str = "localhost",
            redis_port: int = 6379,
            redis_db: int = 0,
            redis_password: Optional[str] = None,
            default_ttl: int = 3600
    ):
        """
        Initialize Redis manager.

        Args:
            redis_host: Redis server host
            redis_port: Redis server port
            redis_db: Redis database number
            redis_password: Redis password (if required)
            default_ttl: Default time-to-live for cache entries (seconds)
        """
        self.redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=False  # Keep as bytes for pickle
        )
        self.default_ttl = default_ttl

        # Test connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

    def get_cached_query_result(self, cache_key: str) -> Optional[Any]:
        """Get cached query result."""
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return pickle.loads(cached_data)
        except Exception as e:
            logger.warning(f"Failed to retrieve cached data for {cache_key}: {e}")
        return None

    def cache_query_result(self, cache_key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """Cache query result."""
        try:
            ttl = ttl or self.default_ttl
            pickled_data = pickle.dumps(data)
            self.redis_client.setex(cache_key, ttl, pickled_data)
            return True
        except Exception as e:
            logger.warning(f"Failed to cache data for {cache_key}: {e}")
            return False

    def invalidate_cache_pattern(self, pattern: str) -> int:
        """Invalidate cache entries matching a pattern."""
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Failed to invalidate cache pattern {pattern}: {e}")
            return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics."""
        try:
            info = self.redis_client.info()
            return {
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
                "hit_rate": info.get("keyspace_hits", 0) / max(
                    info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0), 1
                )
            }
        except Exception as e:
            logger.warning(f"Failed to get cache stats: {e}")
            return {}


def cached_query(cache_key_func: Callable = None, ttl: int = 3600):
    """
    Decorator for caching database query results in Redis.

    Args:
        cache_key_func: Function to generate cache key from function args
        ttl: Time-to-live for cached results
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get Redis manager from dependency injection
            redis_manager = kwargs.pop('redis_manager', None)
            if not redis_manager:
                # Fallback to direct execution if no Redis
                return func(*args, **kwargs)

            # Generate cache key
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                # Default cache key generation
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"

            # Try to get from cache
            cached_result = redis_manager.get_cached_query_result(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result

            # Execute function and cache result
            result = func(*args, **kwargs)
            redis_manager.cache_query_result(cache_key, result, ttl)
            logger.debug(f"Cached result for {cache_key}")

            return result

        return wrapper

    return decorator


class OptimizedDatabaseUrlFactory:
    """High-performance database URL factory with caching."""

    def __init__(self):
        """Initialize the URL factory."""
        self._keepass_manager = PerformantKeePassManager.get_instance()
        self._url_cache: Dict[CacheKey, URL] = {}

    def create_postgres_url(
            self,
            schema_name: str,
            mode: DatabaseMode,
            is_async: bool = False
    ) -> URL:
        """Create PostgreSQL connection URL with caching."""
        cache_key = CacheKey(
            db_type="postgres",
            schema_name=schema_name,
            mode=mode,
            is_async=is_async
        )

        # Return cached URL if available
        if cache_key in self._url_cache:
            logger.debug(f"Returning cached URL for {cache_key}")
            return self._url_cache[cache_key]

        start_time = time.perf_counter()

        # Get credentials
        entry_name = f"{schema_name}_{mode}"
        credentials = self._keepass_manager.get_credentials(entry_name, schema_name, mode)

        # Create URL with appropriate driver
        driver_name = "postgresql+psycopg" if not is_async else "postgresql+psycopg_async"

        url = URL.create(
            drivername=driver_name,
            username=credentials.username,
            password=credentials.password,
            host=credentials.host,
            port=credentials.port,
            database=credentials.database,
        )

        # Cache the URL
        self._url_cache[cache_key] = url

        elapsed = (time.perf_counter() - start_time) * 1000
        logger.info(f"Created PostgreSQL URL for {cache_key} in {elapsed:.2f} ms")

        return url

    def create_postgres_url_with_schema(
            self,
            schema_name: str,
            mode: DatabaseMode,
            is_async: bool = False
    ) -> tuple[URL, str]:
        """Create PostgreSQL connection URL with schema info for translate_map."""
        cache_key = CacheKey(
            db_type="postgres",
            schema_name=schema_name,
            mode=mode,
            is_async=is_async
        )

        # Return cached URL if available
        if cache_key in self._url_cache:
            logger.debug(f"Returning cached URL for {cache_key}")
            return self._url_cache[cache_key], schema_name

        start_time = time.perf_counter()

        # Get credentials
        entry_name = f"{schema_name}_{mode}"
        credentials = self._keepass_manager.get_credentials(entry_name, schema_name, mode)

        # Create URL with appropriate driver
        driver_name = "postgresql+psycopg" if not is_async else "postgresql+psycopg_async"

        url = URL.create(
            drivername=driver_name,
            username=credentials.username,
            password=credentials.password,
            host=credentials.host,
            port=credentials.port,
            database=credentials.database,
        )

        # Cache the URL
        self._url_cache[cache_key] = url

        elapsed = (time.perf_counter() - start_time) * 1000
        logger.info(f"Created PostgreSQL URL for {cache_key} in {elapsed:.2f} ms")

        return url, schema_name

    def create_mssql_url(self, is_async: bool = False) -> URL:
        """Create MS SQL Server connection URL."""
        cache_key = CacheKey(
            db_type="mssql",
            schema_name="ISC_PM_DB",
            mode="rw",
            is_async=is_async
        )

        if cache_key in self._url_cache:
            logger.debug(f"Returning cached URL for {cache_key}")
            return self._url_cache[cache_key]

        start_time = time.perf_counter()
        credentials = self._keepass_manager.get_mssql_credentials()

        driver_name = "mssql+pyodbc" if not is_async else "mssql+aioodbc"

        url = URL.create(
            drivername=driver_name,
            username=credentials.username,
            password=credentials.password,
            host=credentials.host,
            port=credentials.port,
            database=credentials.database,
            query={"driver": credentials.driver} if credentials.driver else {}
        )

        self._url_cache[cache_key] = url

        elapsed = (time.perf_counter() - start_time) * 1000
        logger.info(f"Created MSSQL URL for {cache_key} in {elapsed:.2f} ms")

        return url


class OptimizedEngineFactory:
    """Factory for creating optimized SQLAlchemy engines."""

    @staticmethod
    def create_sync_engine(
            url: URL,
            echo: bool = False,
            pool_size: int = 10,
            max_overflow: int = 20,
            pool_timeout: int = 30,
            pool_recycle: int = 3600,
            **kwargs
    ) -> Engine:
        """Create optimized synchronous SQLAlchemy engine."""
        engine_config = {
            "echo": echo,
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            "pool_pre_ping": True,
            "future": True,
            **kwargs
        }

        logger.info(f"Creating sync engine with pool_size={pool_size}, max_overflow={max_overflow}")
        return create_engine(url, **engine_config)

    @staticmethod
    def create_sync_engine_with_schema(
            url: URL,
            schema_name: str = None,
            echo: bool = False,
            pool_size: int = 10,
            max_overflow: int = 20,
            pool_timeout: int = 30,
            pool_recycle: int = 3600,
            **kwargs
    ) -> Engine:
        """Create optimized synchronous SQLAlchemy engine with schema translation."""
        engine_config = {
            "echo": echo,
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            "pool_pre_ping": True,
            "future": True,
            **kwargs
        }

        logger.info(f"Creating sync engine with pool_size={pool_size}, max_overflow={max_overflow}")
        engine = create_engine(url, **engine_config)

        # Apply schema translation map if schema_name is provided
        if schema_name:
            engine = engine.execution_options(
                schema_translate_map={None: schema_name}
            )
            logger.info(f"Applied schema_translate_map: {{None: '{schema_name}'}}")

        return engine

    @staticmethod
    def create_async_engine(
            url: URL,
            echo: bool = False,
            pool_size: int = 10,
            max_overflow: int = 20,
            pool_timeout: int = 30,
            pool_recycle: int = 3600,
            **kwargs
    ) -> AsyncEngine:
        """Create optimized asynchronous SQLAlchemy engine."""
        engine_config = {
            "echo": echo,
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            "pool_pre_ping": True,
            "future": True,
            **kwargs
        }

        logger.info(f"Creating async engine with pool_size={pool_size}, max_overflow={max_overflow}")
        return create_async_engine(url, **engine_config)

    @staticmethod
    def create_async_engine_with_schema(
            url: URL,
            schema_name: str = None,
            echo: bool = False,
            pool_size: int = 10,
            max_overflow: int = 20,
            pool_timeout: int = 30,
            pool_recycle: int = 3600,
            **kwargs
    ) -> AsyncEngine:
        """Create optimized asynchronous SQLAlchemy engine with schema translation."""
        engine_config = {
            "echo": echo,
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            "pool_pre_ping": True,
            "future": True,
            **kwargs
        }

        logger.info(f"Creating async engine with pool_size={pool_size}, max_overflow={max_overflow}")
        engine = create_async_engine(url, **engine_config)

        # Apply schema translation map if schema_name is provided
        if schema_name:
            engine = engine.execution_options(
                schema_translate_map={None: schema_name}
            )
            logger.info(f"Applied schema_translate_map: {{None: '{schema_name}'}}")

        return engine


# Helper functions for providers
def create_postgres_sync_engine_with_schema_factory(factory, schema_name: str, mode: str):
    """Helper function to create sync engine with schema translation."""
    url, schema = factory.create_postgres_url_with_schema(schema_name, mode, False)
    return OptimizedEngineFactory.create_sync_engine_with_schema(url, schema)

def create_postgres_async_engine_with_schema_factory(factory, schema_name: str, mode: str):
    """Helper function to create async engine with schema translation."""
    url, schema = factory.create_postgres_url_with_schema(schema_name, mode, True)
    return OptimizedEngineFactory.create_async_engine_with_schema(url, schema)


class DatabaseContainer(containers.DeclarativeContainer):
    """
    Optimized dependency injection container for database connections.

    Provides proper dependency injection with @inject decorators while
    maintaining performance optimizations.
    """

    # Configuration
    config = providers.Configuration()
    wiring_config = containers.WiringConfiguration(
        modules=[
            "callbacks", "callback.wd", "callback.callback_version_async",
            "callback.validate_totp", "callback.callback_weekly_status_report",
            "callback.callback_sprint",
        ]
    )

    # Core services
    url_factory = providers.Singleton(OptimizedDatabaseUrlFactory)

    redis_manager = providers.Singleton(
        RedisManager,
        redis_host=config.redis_host.as_(str),
        redis_port=config.redis_port.as_(int),
        redis_db=config.redis_db.as_(int),
        redis_password=config.redis_password.as_(str),
        default_ttl=config.redis_default_ttl.as_(int),
    )

    # ========================================
    # COMMON POSTGRESQL CONFIGURATIONS (Selector)
    # ========================================
    postgres_sync_engines = providers.Selector(
        config.postgres_connection_key.as_(str),
        plat_ro=providers.Singleton(
            create_postgres_sync_engine_with_schema_factory,
            factory=url_factory,
            schema_name="plat",
            mode="ro"
        ),

        plat_rw=providers.Singleton(
            create_postgres_sync_engine_with_schema_factory,
            factory=url_factory,
            schema_name="plat",
            mode="rw"
        ),
        cpp_ro=providers.Singleton(
            create_postgres_sync_engine_with_schema_factory,
            factory=url_factory,
            schema_name="cpp",
            mode="ro"
        ),

        public_ro=providers.Singleton(
            OptimizedEngineFactory.create_sync_engine,
            url=providers.Callable(
                lambda factory: factory.create_postgres_url("public", "ro", False),
                url_factory
            ),
            pool_size=config.postgres_pool_size.as_(int),
            max_overflow=config.postgres_max_overflow.as_(int)
        ),

    )

    postgres_async_engines = providers.Selector(
        config.postgres_connection_key.as_(str),
        public_ro=providers.Singleton(
            create_postgres_async_engine_with_schema_factory,
            factory=url_factory,
            schema_name="public",
            mode="ro"
        ),
        plat_ro=providers.Singleton(
            create_postgres_async_engine_with_schema_factory,
            factory=url_factory,
            schema_name="plat",
            mode="ro"
        ),

        plat_rw=providers.Singleton(
            create_postgres_async_engine_with_schema_factory,
            factory=url_factory,
            schema_name="plat",
            mode="rw"
        ),
        cpp_ro=providers.Singleton(
            create_postgres_async_engine_with_schema_factory,
            factory=url_factory,
            schema_name="cpp",
            mode="ro"
        ),
    )

    # Session factories using Selector
    postgres_sync_sessions = providers.Selector(
        config.postgres_connection_key.as_(str),

        plat_ro=providers.Factory(
            sessionmaker,
            bind=postgres_sync_engines,
            expire_on_commit=False,
            future=True
        ),

        plat_rw=providers.Factory(
            sessionmaker,
            bind=postgres_sync_engines,
            expire_on_commit=False,
            future=True
        ),

        public_ro=providers.Factory(
            sessionmaker,
            bind=postgres_sync_engines,
            expire_on_commit=False,
            future=True
        ),

        cpp_ro=providers.Factory(
            sessionmaker,
            bind=postgres_sync_engines,
            expire_on_commit=False,
            future=True
        )
    )

    postgres_async_sessions = providers.Selector(
        config.postgres_connection_key.as_(str),
        public_ro=providers.Factory(
            async_sessionmaker,
            bind=postgres_async_engines,
            expire_on_commit=False,
            future=True
        ),

        plat_ro=providers.Factory(
            async_sessionmaker,
            bind=postgres_async_engines,
            expire_on_commit=False,
            future=True
        ),

        plat_rw=providers.Factory(
            async_sessionmaker,
            bind=postgres_async_engines,
            expire_on_commit=False,
            future=True
        ),
    cpp_ro = providers.Factory(
        async_sessionmaker,
        bind=postgres_async_engines,
        expire_on_commit=False,
        future=True
    ),
    )

    # ========================================
    # MSSQL (Single instance)
    # ========================================
    mssql_sync_engine = providers.Singleton(
        OptimizedEngineFactory.create_sync_engine,
        url=providers.Callable(
            lambda factory: factory.create_mssql_url(False),
            url_factory
        ),
        pool_size=config.mssql_pool_size.as_(int),
        max_overflow=config.mssql_max_overflow.as_(int)
    )

    mssql_async_engine = providers.Singleton(
        OptimizedEngineFactory.create_async_engine,
        url=providers.Callable(
            lambda factory: factory.create_mssql_url(True),
            url_factory
        ),
        pool_size=config.mssql_pool_size.as_(int),
        max_overflow=config.mssql_max_overflow.as_(int)
    )

    mssql_sync_session_factory = providers.Factory(
        sessionmaker,
        bind=mssql_sync_engine,
        expire_on_commit=False,
        future=True
    )

    mssql_async_session_factory = providers.Factory(
        async_sessionmaker,
        bind=mssql_async_engine,
        expire_on_commit=False,
        future=True
    )

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s'
    )

    # Set Windows event loop policy if needed
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    container = DatabaseContainer()

    # Configure defaults
    container.config.postgres_pool_size.from_env("POSTGRES_POOL_SIZE", default=15)
    container.config.postgres_max_overflow.from_env("POSTGRES_MAX_OVERFLOW", default=30)
    container.config.mssql_pool_size.from_env("MSSQL_POOL_SIZE", default=8)
    container.config.mssql_max_overflow.from_env("MSSQL_MAX_OVERFLOW", default=15)
    container.config.redis_host.from_env("REDIS_HOST", default="localhost")
    container.config.redis_port.from_env("REDIS_PORT", default=6379)
    container.config.redis_db.from_env("REDIS_DB", default=0)
    container.config.redis_password.from_env("REDIS_PASSWORD", default=None)
    container.config.redis_default_ttl.from_env("REDIS_DEFAULT_TTL", default=3600)

    selector_keys_sync = ["plat_ro", "plat_rw", "public_ro", "cpp_ro"]
    selector_keys_async = ["plat_ro", "plat_rw"]

    print("\n=== Provider smoke-test with DB queries ===")

    # --- Test synchronous sessions ---
    for key in selector_keys_sync:
        try:
            container.config.postgres_connection_key.from_value(key)
            SessionLocal = container.postgres_sync_sessions()
            with SessionLocal() as session:
                result = session.execute(text("SELECT now()"))
                value = result.scalar()
            print(f"  ✔ Sync session [{key}] executed SELECT now(): {value}")
        except Exception as e:
            print(f"  ✘ Sync session [{key}] failed: {e}")

    # --- Test asynchronous sessions ---
    async def test_async_sessions():
        for key in selector_keys_async:
            try:
                container.config.postgres_connection_key.from_value(key)
                AsyncSessionLocal = container.postgres_async_sessions()
                async with AsyncSessionLocal() as session:
                    result = await session.execute(text("SELECT now()"))
                    value = result.scalar()
                print(f"  ✔ Async session [{key}] executed SELECT now(): {value}")
            except Exception as e:
                print(f"  ✘ Async session [{key}] failed: {e}")

    asyncio.run(test_async_sessions())

    print("\n=== Smoke-test complete ===")
    ms_db = container.mssql_sync_engine()
    print(f"ms_db: {type(ms_db)}")
    ms_db_async = container.mssql_async_engine()
    print(f"ms_db_async: {type(ms_db_async)}")


