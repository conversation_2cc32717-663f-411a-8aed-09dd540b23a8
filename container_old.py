import asyncio
import os
import time
import pyodbc
from concurrent.futures import Thread<PERSON>oolExecutor

from dependency_injector.providers import Error
from sqlalchemy.engine import URL
from typing import Literal

from pykeepass import PyKeePass
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from dependency_injector import containers, providers
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.sql.expression import text


def _timed_url_create(**kwargs) -> URL:
    start = time.perf_counter()
    url = URL.create(**kwargs)
    elapsed = (time.perf_counter() - start) * 1000
    print(f"[DBUrlFactory] URL.create() took {elapsed:.2f} ms")
    return url


class DBUrlFactory:
    def __init__(self):
        start = time.perf_counter()

        db_path = os.environ.get("DATABASE_PATH")
        keyfile = os.environ.get("MASTER_KEYFILE")
        master_pw = os.environ.get("MASTER_PASSWORD")

        if not db_path or not keyfile:
            raise ValueError("DATABASE_PATH and MASTER_KEYFILE must be set in environment")

        # Open KeePass once
        self.kp = PyKeePass(db_path, keyfile=keyfile, password=master_pw)
        self._cache = {}  # (dbtype, schema, mode, async) -> URL

        elapsed = (time.perf_counter() - start) * 1000
        print(f"[DBUrlFactory] KeePass opened in {elapsed:.2f} ms")

    # ---------------------------
    # Internal helpers
    # ---------------------------
    def _get_entry(self, entry_name: str):
        start = time.perf_counter()
        entry = self.kp.find_entries(title=entry_name, first=True)
        elapsed = (time.perf_counter() - start) * 1000
        print(f"[DBUrlFactory] Retrieved entry '{entry_name}' in {elapsed:.2f} ms")

        if not entry:
            raise KeyError(f"Entry '{entry_name}' not found in KeePass")
        return entry

    def _get_fields_parallel(self, entry_name: str, fields: list[str]) -> dict:
        entry = self._get_entry(entry_name)

        def fetch_field(field: str):
            if field == "username":
                return field, entry.username
            elif field == "password":
                return field, entry.password
            elif field == "url":
                return field, entry.url
            else:
                return field, entry.get_custom_property(field)

        start = time.perf_counter()
        with ThreadPoolExecutor() as ex:
            results = dict(ex.map(fetch_field, fields))
        elapsed = (time.perf_counter() - start) * 1000
        print(f"[DBUrlFactory] Retrieved {len(fields)} fields for '{entry_name}' in {elapsed:.2f} ms")

        return results

    # ---------------------------
    # Public API
    # ---------------------------
    def make_postgres_url(
        self,
        schema_name: str,
        mode: Literal["ro", "rw"],
        async_: bool = False,
    ) -> URL:
        cache_key = ("postgres", schema_name, mode, async_)
        if cache_key in self._cache:
            print(f"[DBUrlFactory] Returning cached URL for {cache_key}")
            return self._cache[cache_key]

        entry_name = f"{schema_name}_{mode}"

        fields = [
            "username",
            "password",
            "DB_NAME",
            "DB_SERVER_NAME",
            f"DB_SERVER_{mode.upper()}_PORT",
        ]
        data = self._get_fields_parallel(entry_name, fields)

        driver_name = "postgresql+psycopg_async" if async_ else "postgresql+psycopg"

        url = _timed_url_create(
            drivername=driver_name,
            username=data["username"],
            password=data["password"],
            host=data["DB_SERVER_NAME"],
            port=int(data[f"DB_SERVER_{mode.upper()}_PORT"]),
            database=data["DB_NAME"],
        )
        self._cache[cache_key] = url
        return url

    def make_mssql_url(
        self,
        async_: bool = False,
    ) -> URL:
        cache_key = ("mssql", "ISC PM DB", "rw", async_)
        if cache_key in self._cache:
            print(f"[DBUrlFactory] Returning cached URL for {cache_key}")
            return self._cache[cache_key]

        entry_name = "ISC PM DB"

        fields = [
            "username",
            "password",
            "DB_SERVER_NAME",
            "DB_NAME_TS",
            "DB_SERVER_RW_PORT",
        ]
        data = self._get_fields_parallel(entry_name, fields)
        # 🔎 Auto-detect installed SQL Server ODBC driver
        driver_names = [d for d in pyodbc.drivers() if d.endswith(" for SQL Server")]
        if not driver_names:
            raise RuntimeError(
                "No SQL Server ODBC driver found! Please install ODBC Driver 17 or 18 for SQL Server."
            )
        chosen_driver = driver_names[0]
        print(f"[DBUrlFactory] Using ODBC driver: {chosen_driver}")

        driver_name = "mssql+aioodbc" if async_ else "mssql+pyodbc"


        url = _timed_url_create(
            drivername=driver_name,
            username=data["username"],
            password=data["password"],
            host=data["DB_SERVER_NAME"],
            port=int(data["DB_SERVER_RW_PORT"]),
            database=data["DB_NAME_TS"],
            query={"driver": chosen_driver},
        )
        self._cache[cache_key] = url
        return url

class SyncEngineFactory:
    """Factory to create SQLAlchemy synchronous engines."""

    def __init__(self, url: URL, echo: bool = False, pool_size: int = 5, max_overflow: int = 10):
        self.url = url
        self.echo = echo
        self.pool_size = pool_size
        self.max_overflow = max_overflow

    def create(self) -> Engine:
        engine = create_engine(
            self.url,
            echo=self.echo,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            future=True,  # SQLAlchemy 2.0 style
        )
        return engine


class AsyncEngineFactory:
    """Factory to create SQLAlchemy asynchronous engines."""

    def __init__(self, url: URL, echo: bool = False, pool_size: int = 5, max_overflow: int = 10):
        self.url = url
        self.echo = echo
        self.pool_size = pool_size
        self.max_overflow = max_overflow

    def create(self) -> AsyncEngine:
        engine = create_async_engine(
            self.url,
            echo=self.echo,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            future=True,  # SQLAlchemy 2.0 style
        )
        return engine

# factory_instance = DBUrlFactory()

class DatabaseContainer(containers.DeclarativeContainer):
    config = providers.Configuration()

    # Shared KeePass-based URL factory
    db_url_factory = providers.Singleton(DBUrlFactory)

    # postgres_schemas = ["cpp", "plp"]
    # postgres_engines = providers.Aggregate(
    #     plat_ro_sync= providers.Singleton(
    #         SyncEngineFactory,
    #         db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
    #         # factory_instance.make_postgres_url("plat", "ro", async_=False),
    #     ),
    #     cpp_ro_sync=providers.Singleton(
    #         SyncEngineFactory,
    #         db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
    #         # factory_instance.make_postgres_url("plat", "ro", async_=False),
    #     ),
    # )

    postgres_engines_selector = providers.Selector(
        config.schema_name,
        # **{
        #     f"{schema_name}_ro_sync": providers.Singleton(
        #         SyncEngineFactory,
        #         factory_instance.make_postgres_url(schema_name, "ro", False),
        #
        #     )
        #     for schema_name in postgres_schemas
        # },
        public_ro_sync=providers.Singleton(
            SyncEngineFactory,
            db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
        ),
        plat_ro_sync=providers.Singleton(
            SyncEngineFactory,
            db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
        ),
        plat_rw_sync = providers.Singleton(
            SyncEngineFactory,
            db_url_factory.provided.make_postgres_url.call("plat", "rw", False),
        )
    )

    postgres_session_selector = providers.Selector(
        config.schema_name,
        public_ro_sync=providers.Factory(
            sessionmaker,
            bind=providers.Singleton(
                lambda url: SyncEngineFactory(url).create(),
                db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
            ),
            expire_on_commit=False,
            future=True,
        ),
        plat_ro_sync=providers.Factory(
            sessionmaker,
            bind=providers.Singleton(
                lambda url: SyncEngineFactory(url).create(),
                db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
        ),
            expire_on_commit=False,
            future=True,
        ),
        cpp_ro_sync=providers.Factory(
            sessionmaker,
            bind=providers.Singleton(
                lambda url: SyncEngineFactory(url).create(),
                db_url_factory.provided.make_postgres_url.call("plat", "ro", False),
        ),
            expire_on_commit=False,
            future=True,
        ),
    )


    # -------------------------------
    # MS SQL (always single instance)
    # -------------------------------
    mssql_sync_engine = providers.Singleton(
        lambda factory: SyncEngineFactory(
            factory.make_mssql_url(async_=False), echo=False
        ).create(),
        db_url_factory,
    )

    mssql_async_engine = providers.Singleton(
        lambda factory: AsyncEngineFactory(
            factory.make_mssql_url(async_=True), echo=False
        ).create(),
        db_url_factory,
    )

    mssql_sync_session = providers.Singleton(
        lambda eng: sessionmaker(bind=eng, expire_on_commit=False, future=True),
        mssql_sync_engine,
    )

    mssql_async_session = providers.Singleton(
        lambda eng: async_sessionmaker(bind=eng, expire_on_commit=False, future=True),
        mssql_async_engine,
    )

async def test_postgres_connections():
    factory = DBUrlFactory()

    # ----------------------------
    # Sync engine
    # ----------------------------
    start = time.perf_counter()
    pg_url_sync = factory.make_postgres_url("plat", "ro", async_=False)
    sync_engine = SyncEngineFactory(pg_url_sync, echo=False).create()
    elapsed = (time.perf_counter() - start) * 1000
    print(f"[Postgres Sync] Engine created in {elapsed:.2f} ms")

    # Run a quick test query
    start = time.perf_counter()
    with sync_engine.connect() as conn:
        result = conn.execute(text("SELECT 1")).scalar()
    elapsed = (time.perf_counter() - start) * 1000
    print(f"[Postgres Sync] Query executed in {elapsed:.2f} ms (result={result})")

    # ----------------------------
    # Async engine
    # ----------------------------
    start = time.perf_counter()
    pg_url_async = factory.make_postgres_url("plat", "ro", async_=True)
    async_engine = AsyncEngineFactory(pg_url_async, echo=False).create()
    elapsed = (time.perf_counter() - start) * 1000
    print(f"[Postgres Async] Engine created in {elapsed:.2f} ms")

    # Run a quick async test query
    start = time.perf_counter()
    async with async_engine.connect() as conn:
        result = await conn.execute(text("SELECT 1"))
        value = result.scalar()
    elapsed = (time.perf_counter() - start) * 1000
    print(f"[Postgres Async] Query executed in {elapsed:.2f} ms (result={value})")

    # Cleanup
    await async_engine.dispose()
    sync_engine.dispose()

# Postgres async session (plat_ro)
async def test_async():
    async_session_local = container.postgres_async_session()
    async with async_session_local() as session:
        result = await session.execute(text("SELECT 1"))
        print(result.scalar())


if __name__ == "__main__":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    factory_main = DBUrlFactory()

    # First call — fetches from KeePass
    url1 = factory_main.make_postgres_url("plat", "ro", async_=True)
    print(url1)

    # Second call — served from cache (no KeePass hit)
    url2 = factory_main.make_postgres_url("plat", "ro", async_=True)
    print(url2)

    # MS SQL example
    url3 = factory_main.make_mssql_url(async_=False)
    print(url3)

    # Postgres sync
    pg_url = factory_main.make_postgres_url("plat", "ro", async_=False)
    sync_engine_main = SyncEngineFactory(pg_url, echo=True).create()
    print(sync_engine_main)

    # Postgres async
    pg_async_url = factory_main.make_postgres_url("plat", "ro", async_=True)
    async_engine_main = AsyncEngineFactory(pg_async_url, echo=True).create()
    print(async_engine_main)

    # MS SQL sync
    mssql_url = factory_main.make_mssql_url(async_=False)
    mssql_engine = SyncEngineFactory(mssql_url).create()

    # MS SQL async
    mssql_async_url = factory_main.make_mssql_url(async_=True)
    mssql_async_engine = AsyncEngineFactory(mssql_async_url).create()
    asyncio.run(test_postgres_connections())

    container = DatabaseContainer()

    # Switch globally: schema + ro/rw
    container.config.ro_or_rw.from_value("ro")
    container.config.schema_name.from_value("plat")

    # engine_main = container.postgres_engines("plat_ro_sync").create()
    # SessionLocal = sessionmaker(bind=engine_main, expire_on_commit=False, future=True)
    #
    # with SessionLocal() as session_main:
    #     result_main = session_main.execute(text("SELECT 1"))
    #     print(result_main.scalar())

    container.config.schema_name.override("plat_rw_sync")
    engine_selector = container.postgres_engines_selector().create()
    SessionLocal = sessionmaker(bind=engine_selector, expire_on_commit=False, future=True)
    print(engine_selector.url)

    with SessionLocal() as session_main:
        result_main = session_main.execute(text("SELECT count(1) from plat.issue"))
        print(result_main.scalar())

    container.config.schema_name.override("cpp_ro_sync")
    try:
        engine_selector = container.postgres_engines_selector().create()
    except Error as e:
        print(f"given provider not found")
        container.config.schema_name.override("public_ro_sync")
        engine_selector = container.postgres_engines_selector().create()
    SessionLocal = sessionmaker(bind=engine_selector, expire_on_commit=False, future=True)

    with SessionLocal() as session_main:
        result_main = session_main.execute(text("SELECT count(1) from issue"))
        print(result_main.scalar())

    session_selector = container.postgres_session_selector()

    with session_selector() as session_main:
        result_main = session_main.execute(text("SELECT now()"))
        print(result_main.scalar())

    # with x.create() as engine:
    #     with engine.connect() as conn:
    #         result = conn.execute(text("SELECT 1")).scalar()
    #         print(result)

    # Postgres sync session (plat_ro)
    # SessionLocal = container.postgres_sync_session()
    # with SessionLocal() as session:
    #     rows = session.execute(text("SELECT 1")).all()
    #     print(rows)
    #
    # asyncio.run(test_async())


    # MS SQL sync
    SessionMSSQL = container.mssql_sync_session()
    # with SessionMSSQL() as session:
    #     rows = session.execute(text("SELECT @@VERSION")).all()
    #     print(rows)

