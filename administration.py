import json

# import dash_app
from flask_admin import Admin, BaseView, expose, AdminIndexView
from flask_admin.contrib.sqla import ModelView
from flask_admin._compat import urljoin
from app import server
from flask_admin.menu import MenuLink

from dbmodels.user import User, Role, Permission
from dbmodels.user_addons import UserRole, RolePermissions
from dbmodels.teams import Teams
from dbmodels.issue import Issue
from data.get_from_db import start_session
from redis import Redis
from flask_admin.contrib import rediscli
from flask import redirect, request, url_for, render_template, session, flash
from flask_login import current_user, login_required, login_user, logout_user, UserMixin
from flask_adminlte3 import AdminLTE3
import pyotp
from msal import ConfidentialClientApplication, SerializableTokenCache
import os
from pykeepass import PyKeePass as pkp
from data.role_manager import user_has_role, user_has_permission, get_user_roles, get_user_permissions, \
    assign_role_to_user, assign_permission_to_role

# set optional bootswatch theme
# server.config['FLASK_ADMIN_SWATCH'] = 'cerulean'
# server.config['FLASK_ADMIN_TEMPLATE_MODE'] = 'bootstrap3'


class LogoutView(BaseView):
    @expose("/")
    def index(self):
        logout_user()
        # Send the identity_changed signal with AnonymousIdentity
        identity_changed.send(current_app._get_current_object(), identity=AnonymousIdentity())
        return redirect(url_for('admin.index'))


class LoginMenuLink(MenuLink):
    def is_accessible(self):
        return not current_user.is_authenticated


class LogoutMenuLink(MenuLink):
    def is_accessible(self):
        return current_user.is_authenticated


class UserAuth(UserMixin, User):
    def __init__(self, username, displayName: str = None):
        super().__init__()
        self.id = username
        self.email_address = username
        self.displayName = displayName
        self._roles = None
        self._permissions = None

    @property
    def roles(self):
        """Get all roles assigned to this user"""
        if self._roles is None:
            self._roles = get_user_roles(self.email_address)
        return self._roles

    @property
    def permissions(self):
        """Get all permissions assigned to this user"""
        if self._permissions is None:
            self._permissions = get_user_permissions(self.email_address)
        return self._permissions

    def has_role(self, role_name):
        """Check if the user has a specific role"""
        return user_has_role(self.email_address, role_name)

    def has_permission(self, permission_name):
        """Check if the user has a specific permission"""
        return user_has_permission(self.email_address, permission_name)

    def is_superuser(self):
        """Check if the user is a superuser"""
        return self.has_role('superuser')

    def is_admin(self):
        """Check if the user is an admin"""
        return self.has_role('admin')

    def is_pmo(self):
        """Check if the user is a PMO"""
        return self.has_role('pmo')

    def get_secret_key(self):
        secret_key = pyotp.random_base32()
        totp_auth = pyotp.totp.TOTP(secret_key)

    def get_totp_uri(self):
        totp_auth_uri = pyotp.totp_auth.provisioning_uri(name=self.emailAddress, issuer_name='CoreCARD')
        return 'otpauth://totp/2FA-Demo:{0}?secret={1}&issuer=2FA-Demo' \
            .format(self.username, self.otp_secret)

    def verify_totp(self, token) -> bool:
        totp_auth = pyotp.totp.TOTP(self.otp_secret)
        return totp_auth.verify(token)
        # return onetimepass.valid_totp(token, self.otp_secret)


AdminLTE3(server)

# MSAL helper_function
home_path = os.getenv('HOME', 'c:/vishal/KeyPass')
ref = pkp(
    filename=os.path.join(home_path, 'Database.kdbx'),
    keyfile=os.path.join(home_path, 'Database.key')
)

msal_map = {}
for title in ['CLIENT_ID', 'TENANT_ID', 'CLIENT_SECRET']:
    entry = ref.find_entries(title=title, first=True)
    # exec(f'{title} = {entry.password}')
    msal_map[title] = entry.password
AUTHORITY = f"https://login.microsoftonline.com/{msal_map['TENANT_ID']}"
SCOPES = ["User.Read"]
SESSION_TYPE = "filesystem"

cache = SerializableTokenCache()
msal_app = ConfidentialClientApplication(
    client_id=msal_map['CLIENT_ID'], authority=AUTHORITY, client_credential=msal_map['CLIENT_SECRET']
)


@server.route("/admin/login")
def login():
    print(f'redirect url - {url_for("admin.index")}')
    if current_user.is_authenticated:
        return redirect(url_for("admin.index"))

    # Get the redirect URI
    redirect_uri = url_for('authorized', _external=True)

    auth_code_flow_dict = msal_app.initiate_auth_code_flow(
        scopes=SCOPES,
        redirect_uri=redirect_uri
    )
    auth_url = auth_code_flow_dict['auth_uri']
    session['auth_code_flow_dict'] = auth_code_flow_dict
    print(auth_code_flow_dict)
    # Exchange authorization code for access token

    return redirect(auth_url)


@server.route("/admin/authorized")
def authorized():
    # Get the authorization response
    code = request.args.get('code')

    code_flow = session['auth_code_flow_dict']

    redirect_uri = url_for('authorized', _external=True)
    # redirect_uri = urljoin(request.host_url, "/admin/auth/callback")

    # Exchange authorization code for access token
    result = msal_app.acquire_token_by_auth_code_flow(
        auth_code_flow=code_flow,
        # redirect_uri=redirect_uri,
        auth_response=request.args.to_dict()
    )

    if "access_token" in result:
        print("Success!!!!!")
        # Get the user's email address from the token
        id_token_claims = result.get("id_token_claims")
        print(json.dumps(id_token_claims))
        email = id_token_claims.get("preferred_username")
        display_name = id_token_claims.get("name")

        # Login the user
        user = UserAuth(email, display_name)
        login_user(user)

        return redirect(url_for('admin.index'))

    return "Authentication failed"


@server.route("/admin/logout")
def logout_current_admin_user():
    if current_user.is_authenticated:
        logout_user()
        # Send the identity_changed signal with AnonymousIdentity
        identity_changed.send(current_app._get_current_object(), identity=AnonymousIdentity())
    return redirect(url_for('admin.index'))

# @server.route("/admin/logout")
# def logout():
#     logout_user()
#     return redirect(url_for("index"))


class RedisCli(BaseView):
    def __init__(self, redis_instance, **kwargs):
        super().__init__(**kwargs)
        self.redis_instance = redis_instance

    @expose("/")
    def index(self):
        redis_data = self.redis_instance.getall('vishal')
        return self.render('redis.html', redis=redis_data)


class MyModelView(ModelView):
    def is_accessible(self):
        return current_user.is_authenticated

    def inaccessible_callback(self, name, **kwargs):
        return redirect(url_for("login", next=request.url))


# class MyAdminIndexView(AdminIndexView):
#     @expose("/")
#     @login_required
#     def index(self):
#         return self.render("admin/index.html")


class MyAdminIndexView(AdminIndexView):
    @expose("/", methods=['GET', 'POST'])
    @login_required
    def index(self):
        return self.render('myadmin3/my_index.html')


class AdminLTEModelView(ModelView):
    list_template = 'admin/model/list.html'
    create_template = 'admin/model/create.html'
    edit_template = 'admin/model/edit.html'
    details_template = 'admin/model/details.html'

    create_modal_template = 'admin/model/modals/create.html'
    edit_modal_template = 'admin/model/modals/edit.html'
    details_modal_template = 'admin/model/modals/details.html'


# admin = Admin(dash_app.server, name='dashboard', template_mode='bootstrap3', index_view=MyAdminIndexView())
# admin = Admin(server, name='dashboard', template_mode='bootstrap4')
admin = Admin(base_template='myadmin3/my_master.html',
              template_mode='bootstrap4',
              index_view=MyAdminIndexView(url="/admin")
              )

admin.init_app(server)

db_session = start_session()


class TeamCustomView(ModelView):
    column_searchable_list = ['assignee_name', 'team_name', 'startDate', 'endDate']
    form_columns = ['assignee_name', 'team_name', 'startDate', 'endDate', 'active', 'accountId']
    can_delete = False
    create_modal = True


class UserCustomerView(ModelView):
    column_display_pk = True
    column_searchable_list = ['displayName', 'emailAddress', 'accountId']
    form_columns = ('accountId', 'displayName', 'emailAddress')
    column_exclude_list = ['accountType', 'active', 'timeZone', 'locale', 'otp_secret', 'otp_secret_added_on']
    can_export = True
    can_view_details = False
    can_create = False
    can_edit = False
    can_delete = False


class CustomIssueView(ModelView):
    column_labels = {
        "assignee_user": "Assignee",
        "reporter_user": "Reporter",
    }

    def __init__(self, session, **kwargs):
        super().__init__(Issue, session, **kwargs)

    def _list_columns(self):
        return ["key", "summary", "assignee", "reporter"]

    def scaffold_list_columns(self):
        columns = super().scaffold_list_columns()

        # Replace the relationship columns with the desired columns from the related model
        # columns[columns.index("assignee_user")] = "assignee.displayName"
        # columns[columns.index("reporter_user")] = "reporter.displayName"

        return columns


admin.add_view(CustomIssueView(db_session, name="view_2", endpoint="issue_"))
# admin.add_view(ModelView(Issue, pg_session, name="issue_view_1", url="/admin/issue_view_1"))

# User Management
admin.add_view(UserCustomerView(User, db_session, name="Users", category="User Management"))
admin.add_view(TeamCustomView(Teams, db_session, name="Teams", category="User Management"))

# Role Management
class RoleModelView(MyModelView):
    column_list = ('id', 'name', 'description')
    column_searchable_list = ('name', 'description')
    form_columns = ('name', 'description')
    can_create = True
    can_edit = True
    can_delete = True

class PermissionModelView(MyModelView):
    column_list = ('id', 'name', 'description')
    column_searchable_list = ('name', 'description')
    form_columns = ('name', 'description')
    can_create = True
    can_edit = True
    can_delete = True

class UserRoleModelView(MyModelView):
    column_list = ('emailAddress', 'role_id')
    form_columns = ('emailAddress', 'role_id')
    can_create = True
    can_edit = True
    can_delete = True

    def on_model_change(self, form, model, is_created):
        # Ensure the user exists
        user = db_session.query(User).filter(User.emailAddress == model.emailAddress).first()
        if not user:
            flash(f'User with email {model.emailAddress} does not exist', 'error')
            return False

        # Ensure the role exists
        role = db_session.query(Role).filter(Role.id == model.role_id).first()
        if not role:
            flash(f'Role with ID {model.role_id} does not exist', 'error')
            return False

        return True

class RolePermissionModelView(MyModelView):
    column_list = ('role_id', 'permission_id')
    form_columns = ('role_id', 'permission_id')
    can_create = True
    can_edit = True
    can_delete = True

    def on_model_change(self, form, model, is_created):
        # Ensure the role exists
        role = db_session.query(Role).filter(Role.id == model.role_id).first()
        if not role:
            flash(f'Role with ID {model.role_id} does not exist', 'error')
            return False

        # Ensure the permission exists
        permission = db_session.query(Permission).filter(Permission.id == model.permission_id).first()
        if not permission:
            flash(f'Permission with ID {model.permission_id} does not exist', 'error')
            return False

        return True

admin.add_view(RoleModelView(Role, db_session, name="Roles", category="Role Management"))
admin.add_view(PermissionModelView(Permission, db_session, name="Permissions", category="Role Management"))
admin.add_view(UserRoleModelView(UserRole, db_session, name="User Roles", category="Role Management"))
admin.add_view(RolePermissionModelView(RolePermissions, db_session, name="Role Permissions", category="Role Management"))

# System
admin.add_view(rediscli.RedisCli(Redis(), name="Redis CLI", category="System"))

# Links
admin.add_link(LogoutMenuLink(name='Logout', category='', url="/admin/logout"))
admin.add_link(LoginMenuLink(name='Login', category='', url="/admin/login"))

server.run(host="localhost", port="5000", debug=True)