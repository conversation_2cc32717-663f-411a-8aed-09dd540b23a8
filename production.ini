[app:main]
use = call:index:make_app

[server:main]
use = egg:waitress#main
listen = localhost:1951
threads = 8

[loggers]
keys = root

[handlers]
keys = file

[formatters]
keys = generic

[logger_root]
level = INFO
handlers = file

[handler_file]
class = FileHandler
level = INFO
formatter = generic
args = ('dash_app.log', 'a')

[formatter_generic]
format = %(asctime)s %(levelname)-8s [%(name)s] %(message)s
