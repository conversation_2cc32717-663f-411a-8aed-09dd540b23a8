<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Login Page Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/modern_login.css">
</head>
<body class="login-page">
    <div class="modern-login-page">
        <!-- Notifications Container -->
        <div id="notifications-container"></div>
        
        <!-- Modern Login Container -->
        <div class="modern-login-container">
            <!-- Login Header -->
            <div class="modern-login-header">
                <h1 class="modern-login-title">Welcome Back</h1>
                <p class="modern-login-subtitle">Sign in to your account</p>
            </div>
            
            <!-- Login Form -->
            <form class="modern-login-form" onsubmit="handleLogin(event)">
                <!-- Email Input Group -->
                <div class="modern-input-group">
                    <label class="modern-input-label" for="email">Email Address</label>
                    <div class="modern-input-field">
                        <input
                            id="email"
                            type="email"
                            placeholder="Enter your email address"
                            class="modern-input"
                            required
                            autocomplete="email"
                        />
                        <i class="fa fa-envelope modern-input-icon"></i>
                    </div>
                </div>
                
                <!-- Password Input Group -->
                <div class="modern-input-group">
                    <label class="modern-input-label" for="password">Password</label>
                    <div class="modern-input-field">
                        <input
                            id="password"
                            type="password"
                            placeholder="Enter your password"
                            class="modern-input"
                            required
                            autocomplete="current-password"
                        />
                        <i class="fa fa-lock modern-input-icon"></i>
                        <button
                            type="button"
                            class="password-toggle"
                            onclick="togglePassword()"
                            title="Toggle password visibility"
                            aria-label="Show password"
                        >
                            <i class="fa fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Login Button -->
                <button
                    type="submit"
                    class="modern-login-button"
                    id="loginButton"
                >
                    Sign In
                </button>
            </form>
        </div>
    </div>

    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            const icon = toggleButton.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fa fa-eye-slash';
                toggleButton.setAttribute('aria-label', 'Hide password');
                toggleButton.title = 'Hide password';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fa fa-eye';
                toggleButton.setAttribute('aria-label', 'Show password');
                toggleButton.title = 'Show password';
            }
        }

        // Form validation and submission
        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const button = document.getElementById('loginButton');
            
            // Clear previous errors
            clearErrors();
            
            let isValid = true;
            
            // Validate email
            if (!email) {
                showError('email', 'Email is required');
                isValid = false;
            } else if (!isValidEmail(email)) {
                showError('email', 'Please enter a valid email address');
                isValid = false;
            }
            
            // Validate password
            if (!password) {
                showError('password', 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showError('password', 'Password must be at least 6 characters');
                isValid = false;
            }
            
            if (!isValid) {
                return;
            }
            
            // Show loading state
            button.classList.add('loading');
            button.disabled = true;
            
            // Simulate login process
            setTimeout(() => {
                button.classList.remove('loading');
                button.disabled = false;
                
                // Show success message
                showNotification('Login successful! (This is a demo)', 'success');
                
                // Store email for next time
                localStorage.setItem('demo_email', email);
            }, 2000);
        }
        
        // Email validation
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Show field error
        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const inputGroup = field.closest('.modern-input-group');
            
            field.style.borderColor = 'var(--error-color)';
            
            let errorElement = inputGroup.querySelector('.field-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                errorElement.style.cssText = `
                    color: var(--error-color);
                    font-size: 0.75rem;
                    margin-top: 0.25rem;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                `;
                inputGroup.appendChild(errorElement);
            }
            
            errorElement.innerHTML = `<i class="fa fa-exclamation-circle"></i> ${message}`;
        }
        
        // Clear all errors
        function clearErrors() {
            const errorElements = document.querySelectorAll('.field-error');
            errorElements.forEach(el => el.remove());
            
            const inputs = document.querySelectorAll('.modern-input');
            inputs.forEach(input => input.style.borderColor = '');
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notifications-container');
            
            const notification = document.createElement('div');
            notification.className = `modern-${type}-message`;
            notification.innerHTML = `
                <i class="fa fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${message}
            `;
            
            container.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 5000);
        }
        
        // Load saved email on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedEmail = localStorage.getItem('demo_email');
            if (savedEmail) {
                document.getElementById('email').value = savedEmail;
            }
            
            // Add entrance animations
            const formElements = document.querySelectorAll('.modern-input-group, .modern-login-button');
            formElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.4s ease-out';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 200 + (index * 100));
            });
        });
        
        // Add real-time validation
        document.getElementById('email').addEventListener('input', function() {
            const email = this.value;
            if (email && !isValidEmail(email)) {
                this.style.borderColor = 'var(--error-color)';
            } else if (email) {
                this.style.borderColor = 'var(--success-color)';
            } else {
                this.style.borderColor = '';
            }
        });
        
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            if (password && password.length < 6) {
                this.style.borderColor = 'var(--error-color)';
            } else if (password) {
                this.style.borderColor = 'var(--success-color)';
            } else {
                this.style.borderColor = '';
            }
        });
    </script>
</body>
</html>
