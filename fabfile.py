import getpass
# import fabric
from fabric import task
from fabric import Connection, Config


@task
def welcome(ctx):
    print("Welcome to getting started with Fabric!")

@task
def deploy(ctx):
    sudo_pass = getpass.getpass("Enter sudo password:")
    config = Config(overrides={'sudo': {'password': sudo_pass}})

    c = Connection(host='***********', user='dash', port=22, config=config)
    result = c.run('uname -s', pty=True)
    print(result)
    c.sudo('whoami', hide='stderr')
    c.run('cd /opt/jiradashboard && git pull')
    c.sudo('systemctl restart index')
    c.sudo('systemctl restart celery')

