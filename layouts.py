"""
all the layouts
"""
import psutil
import datetime
import os
import pandas as pd
import pytz
from dash import html, dash_table
from dash import dcc
import dash_bootstrap_components as dbc
from sqlalchemy import inspect

import app
import callbacks
import custom_container
import data.get_from_db as db
import time
import dash_extensions as de

import data.helper
from data.helper import TimeTaken, get_cpu_usage, get_memory_usage
from custom_container import KPDatabaseOpener, KeePassPasswordManager
from data.custom_logger import MyLogger
from urllib.parse import quote
import flask
from flask_login import current_user
from app import roles_and_permissions, user_role, session
from dash_extensions import Mermaid
from dependency_injector.wiring import inject, Provide
from dependency_injector import containers
from dependency_injector.containers import DynamicContainer


def generate_clock_layout(row: int, col: int):
    dial_tags = [html.Div(className="diallines", style={'transform': f"rotate({6 * count}deg)"}, ) for count in
                 range(1, 60)]

    return html.Div(
        children=[
            dcc.Interval(id={'type': 'interval', 'index': f'{row}_{col}'}, interval=1000 * 60),
            html.Div(
                children=[
                    html.Div(className="info date", id={'type': 'date-details', 'index': f'{row}_{col}'}),
                    html.Div(className="info day", id={'type': 'day-details', 'index': f'{row}_{col}'}),
                    html.Div(className="info country", id={'type': 'country', 'index': f'{row}_{col}'})
                ]
            ),
            html.Div(className="dot"),
            html.Div(
                children=[
                    html.Div(className="hour-hand", id={'type': 'hour', 'index': f'{row}_{col}'}),
                    html.Div(className="minute-hand", id={'type': 'minute', 'index': f'{row}_{col}'}),
                    html.Div(
                        children=[html.Div(className="second-hand", id={'type': 'second', 'index': f'{row}_{col}'}), ],
                        className="seconds-container"
                    ),
                ]
            ),
            html.Div(
                children=[
                    html.Span("3", className="format-span h3"),
                    html.Span("6", className="format-span h6"),
                    html.Span("9", className="format-span h9"),
                    html.Span("12", className="format-span h12"),
                ]
            ),
            html.Div(className="diallines"),
            *dial_tags,
            html.Div(className="time", id={'type': 'time', 'index': f'{row}_{col}'})
        ],
        className="clock"
    )


def generate_clock_row_col(rows: int = 2, cols: int = 4):
    row_array = []
    for row in range(rows):
        col_array = []
        for col in range(cols):
            col_array.append(html.Div(generate_clock_layout(row, col), className="col"))
        row_array.append(html.Div(col_array, className="row"))
    return row_array


keepass = KPDatabaseOpener(
        os.getenv("DATABASE_PATH"),
        master_keyfile=os.getenv("MASTER_KEYFILE")
    )
kpk_inst = KeePassPasswordManager(keepass)


def check_session_factory(session_factory):
    my_logger = MyLogger().get_logger()
    if isinstance(session_factory, containers.DynamicContainer):
        my_logger.debug("session_factory is dynamic container")
        pg_session_generator = session_factory.db_ro().session()
    else:
        my_logger.debug("Session not inserted dynamically. Constructing it manually")
        pg_session_generator = custom_container.Database(custom_container.DbConnectionURI(kpk_inst, read_only=True)).session()
    return pg_session_generator


@app.server.route('/api/db/schema', methods=['GET'])
def display_schema():
    from flask import jsonify
    schema = custom_container.Database(custom_container.DbConnectionURI(kpk_inst, read_only=True)).get_db_schema()
    return jsonify(list(set(schema) - {'information_schema', 'public'}))


@inject
def page_content(
        url, project: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
):
    my_logger = MyLogger().get_logger()
    options = dict(loop=True, autoplay=True, rendererSettings=dict(preserveAspectRatio='xMidYMid slice'))
    my_logger.debug(f"{current_user.id} User has access to projects: {project}")
    # project is having null value when pointing to home
    # for now hard coded the projects to plat
    # with db.start_session('plat') as pg_session:
    #     branch_list = db.get_branch_details(pg_session)
    #     package_list = db.get_package_details(pg_session)

    # if isinstance(session_factory, containers.DynamicContainer):
    #     my_logger.debug("session_factory is dynamic container")
    #     pg_session_generator = session_factory.db_ro().session()
    # else:
    #     my_logger.debug("Session not inserted dynamically. Constructing it manually")
    #     pg_session_generator = db.Database(db.DbConnectionURI(kpk_inst, read_only=True)).session()
    schema_list = None
    # with check_session_factory(session_factory) as pg_session:
    schema_name = project if project != '' else 'public'
    # with session_factory.db_ro().session() as pg_session:
    #
    #     db_engine = pg_session.get_bind()
    #     inspector = inspect(db_engine)
    #     schema_list = inspector.get_schema_names()
    #     schema_list = list(set(schema_list) - {'information_schema', 'public'})
    #     print(schema_list)
    #     if project in schema_list:
    #         updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
    #         pg_session.bind = updated_engine

    with session_factory.db_connections()['ro'][schema_name].session() as pg_session:
        branch_list = db.get_branch_details(pg_session)
        package_list = db.get_package_details(pg_session)

        if project == 'plat':
            branch_options = [dict(label=item, value=item) for item in branch_list]
            package_options = [dict(label=item, value=item) for item in package_list]
        else:
            branch_options = package_options= []

        if url == '/':
            # lotourl = "https://assets6.lottiefiles.com/packages/lf20_gwjpwf21.json"
            # return html.Div(de.Lottie(options=options, url=lotourl, width="50%", height="50%"))
            mylogger = MyLogger().get_logger()
            mylogger.debug(f"Home Page. Project = {project}. Schema Name = {schema_name}")
            df = db.get_user_detail(pg_session)
            df.drop(columns=['displayName', 'emailAddress'], inplace=True)
            df['Status'] = df['Status'].map({True: 'Active', False: 'Inactive'})
            df = df.groupby(['Account Type', 'Status']).count().reset_index()
            df.sort_values(['Account Type', 'Status'], inplace=True, ascending=[False, True])

            user_array = df.to_numpy()

            for col in df.columns:
                df[col] = df[col].apply(data.helper.apply_td)

            run_details = []
            local_engine = pg_session.get_bind()
            inspector = inspect(local_engine)
            schema_list = inspector.get_schema_names()
            schema_list = list(set(schema_list) - {'information_schema', 'public'})

            for proj in schema_list:
                _db_session = pg_session
                local_engine = local_engine.execution_options(schema_translate_map={None: proj})
                print(local_engine)
                _db_session.bind = local_engine
                df_proj = db.get_run_details(_db_session)
                df_proj.insert(0, 'Project', proj)
                run_details.append(df_proj)
            df_consolidated = pd.concat(run_details)
            df_consolidated['LAST_RUN'] = df_consolidated['LAST_RUN'].dt.strftime('%m/%d %H:%M')
            run_details_array = df_consolidated.to_numpy()

            for col in df_consolidated.columns:
                df_consolidated[col] = df_consolidated[col].apply(data.helper.apply_td)
            # Logic to get keys from redis cache
            # k_prefix = dash_app.cache.cache.key_prefix
            # keys = dash_app.cache.cache._write_client.keys(k_prefix + '*')
            # keys = [k.decode('utf8') for k in keys]
            # keys = [k.replace(k_prefix, '') for k in keys]
            # for key in keys:
            #     values = dash_app.cache.get_many(key)
            # values = dash_app.cache.get_many(*keys)

            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.P(
                                                f"{session['displayName']}", id="id-welcome-user",
                                                className="card-text"
                                            )
                                        ],
                                        className="card"
                                    ),
                                ],
                                className="mb-30 col-md-6 col-lg-4"
                            ),
                        ],
                        className="row"
                    ),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.H3("Infra Parameters", className="card-title"),
                                            html.P(
                                                f"Memory: {get_memory_usage():.2f} MB", className="card-text"
                                            ),
                                            html.P(
                                                f"CPU:\n{get_cpu_usage():.2f}%", className="card-text"
                                            ),
                                        ],
                                        className="card"
                                    ),
                                ],
                                className="mb-30 col-md-6 col-lg-4"
                            ),
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.H3("Account Summary", className="card-title"),
                                            html.P(f"{user_array[0][1]} {user_array[0][0]} = {user_array[0][2]}",
                                                   className="card-text"),
                                            html.P(f"{user_array[1][1]} {user_array[1][0]} = {user_array[1][2]}",
                                                   className="card-text"),
                                            html.P(f"{user_array[2][1]} {user_array[2][0]} = {user_array[2][2]}",
                                                   className="card-text"),
                                        ],
                                        className="card"
                                    ),
                                ],
                                className="mb-30 col-md-6 col-lg-4"
                            ),
                            html.Div(
                                children=[
                                    html.Div(
                                        children=[
                                            html.H3("Data Refresh", className="card-title"),
                                            html.Div(
                                                children=[
                                                    html.H4(
                                                        f"Schema {run_details_array[0][0]}",
                                                        className="card-text"
                                                    ),
                                                    html.Label(
                                                        f"{run_details_array[0][1]} = {run_details_array[0][2]}",
                                                        className="card-text"
                                                    ),
                                                    html.Label(
                                                        f"{run_details_array[1][1]} = {run_details_array[1][2]}",
                                                        className="card-text"
                                                    ),
                                                ]
                                            ),
                                            html.Div(
                                                children=[
                                                    html.H4(
                                                        f"Schema {run_details_array[2][0]}",
                                                        className="card-text"
                                                    ),
                                                    html.Label(
                                                        f"{run_details_array[2][1]} = {run_details_array[2][2]}",
                                                        className="card-text"
                                                    ),
                                                    html.Label(
                                                        f"{run_details_array[3][1]} = {run_details_array[3][2]}",
                                                        className="card-text"
                                                    ),
                                                ]
                                            ),

                                        ],
                                        className="card"
                                    ),
                                ],
                                className="mb-30 col-md-6 col-lg-4"
                            ),
                        ],
                        className="row"
                    ),

                    # html.Div(children=[
                    #     html.Table(
                    #         children=[
                    #             html.Caption("Account Status"),
                    #             html.Thead(children=[
                    #                 html.Tr([html.Th(col) for col in df.columns]),
                    #             ]),
                    #             html.Tbody(children=[
                    #                 *[html.Tr(i) for i in df.values]
                    #             ])
                    #
                    #         ], className='format-table'
                    #     ),
                    # ]),
                    # html.Div(children=[
                    #     html.Table(
                    #         children=[
                    #             html.Caption("Data As of"),
                    #             html.Thead(children=[
                    #                 html.Tr([html.Th(col) for col in df_consolidated.columns]),
                    #             ]),
                    #             html.Tbody(children=[
                    #                 *[html.Tr(i) for i in df_consolidated.values]
                    #             ])
                    #         ], className='format-table'
                    #     )
                    # ]),
                ]
                , className='container_custom'
            )
        elif url in [f'/{project}/overview']:

            options = db.get_board_id(pg_session, project)
            options_team = [{'label': i[0], 'value': i[0]} for i in db.get_team_names(pg_session)]

            value = None
            if project == "plat":
                value = 39
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Label("Board"),
                            dcc.Dropdown(
                                options=options,
                                className="dcc-dropdown",
                                clearable=False,
                                searchable=False,
                                id="id-sprint-board-values",
                                value=value
                            ),
                            html.Label("Sprints"),
                            html.I(className="line-divider"),
                            html.Label("Active"),
                            dcc.Loading(dcc.Dropdown(id="id-active-sprint", className="dcc-dropdown")),
                            html.Label("Future"),
                            dcc.Loading(dcc.Dropdown(id="id-future-sprint", className="dcc-dropdown")),
                            html.Label("Closed"),
                            dcc.Loading(dcc.Dropdown(id="id-closed-sprint", className="dcc-dropdown")),
                            html.Label(children=["Team"], className="hide", id="id-label-team"),

                            dcc.Dropdown(
                                id="id-team-sprint", className="dcc-dropdown hide",
                                options=options_team,
                                value='PLAT_REWARDS'
                            ),

                            html.Label(children=["Type"], className="hide", id="id-label-view"),
                            dcc.Dropdown(
                                id="id-status-sprint",
                                options=[
                                    dict(label='started', value='started'),
                                    dict(label='created', value='created'),
                                    dict(label='updated', value='updated'),
                                ],
                                searchable=False, value='started', className="dcc-dropdown hide"),
                            html.I(className="line-divider"),
                            html.Label("Time Taken"),
                            dcc.Loading(id="id-fetch-time-sprints"),
                        ],
                        # className="side-menu hidden",
                        className="side-menu",
                        # id="id-side-menu-panel"
                        id=dict(type='side-panel', index='sprint')
                    ),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Button(
                                        # html.Img(src=dash_app.get_asset_url("img/menu_open_black_24dp.svg"), ),
                                        className="format-button",
                                        id={'type': 'toggle-panel', 'index': "sprint"}
                                        # id="id-toggle-button"

                                    ),
                                ],
                            ),
                            html.Div("...", className="header-menu", id="id-overview-main-header"),
                            html.Div("", className="header-right-side"),
                            html.Div("", className="middle-vacant"),
                            html.Article(
                                children=[
                                    html.Div(
                                        children=[""],
                                        className="layer one show",
                                        id="id-overview-layer-1"
                                    ),
                                    html.Div(
                                        children=[""],
                                        className="layer two",
                                        id="id-overview-layer-2"
                                    ),
                                    html.Div(
                                        children=[""],
                                        className="layer three",
                                        id="id-overview-layer-3"
                                    ),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer four days-grid",
                                        id="id-overview-layer-4"
                                    )),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer five",
                                        id="id-overview-layer-5"
                                    ))

                                ],
                                # id="overview-layer",
                                id="id-sprint-details",
                                className="main-area",
                            ),
                            html.Div("--", className="side-vacant"),
                            html.Div("", className="bottom-left"),
                            html.Div(children=[
                                html.Ul(
                                    children=[
                                        html.Li(className="active", id="id-overview-bullet-1"),
                                        html.Li(id="id-overview-bullet-2"),
                                        html.Li(id="id-overview-bullet-3"),
                                        html.Li(id="id-overview-bullet-4"),
                                        html.Li(id="id-overview-bullet-5"),
                                    ],
                                    className="bullets"
                                )],
                                className="bottom-middle"
                            ),
                            html.Div("", className="bottom-right"),
                        ],
                        # className="display-area expand-grid",
                        className="display-area",
                        # id="overview-display-area"
                        # id="id-main-display-panel"
                        id=dict(type='main-panel', index='sprint')
                    ),
                ],
                className="page-grid"
            )
        elif url in [f'/{project}/version']:
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Label("Selection Panels"),
                            html.I(className="line-divider"),
                            html.Details(
                                children=[
                                    html.Summary("Version State", className="format_summary"),
                                    html.Label("Released", className="format_summary"),
                                    dcc.Dropdown(
                                        options=[
                                            dict(label='Yes', value=1),
                                            dict(label='No', value=0),
                                        ],
                                        className="dcc-dropdown",
                                        clearable=False,
                                        searchable=False,
                                        id="id-version-released-value",
                                        value=0
                                    ),
                                    html.Label("Archived", className="format_summary"),
                                    dcc.Dropdown(
                                        options=[
                                            dict(label='Yes', value=1),
                                            dict(label='No', value=0),
                                        ],
                                        className="dcc-dropdown",
                                        clearable=False,
                                        searchable=False,
                                        id="id-version-archive-value",
                                        value=0
                                    ),
                                ],
                                id='details',
                                open=False,
                                className="hide"
                            ),
                            html.I(className="line-divider hide"),
                            html.Label("Releases"),
                            dcc.Loading(
                                dcc.Dropdown(id="id-versions", className="dcc-dropdown", multi=True, searchable=True)),
                            html.Label("Releases: Pattern Search"),
                            html.Div(children=[
                                dcc.Input(id="id-version-search", type="text", debounce=True),
                                # de.Keyboard(id="keyboard"),
                                html.Button(id="id-version-serach-button", className="format-serach-button")
                            ]),

                            html.Label("Projects", className="hide", id="id-release-project"),
                            dcc.Dropdown(
                                id="id-release-project-dropdown",
                                className="dcc-dropdown hide",
                                disabled=True,
                                multi=True
                            ),
                            html.Label("Custom Status", className="hide", id="id-release-status-label"),
                            dcc.Dropdown(
                                id="id-release-status-value",
                                className="dcc-dropdown hide",
                                disabled=True,
                                clearable=False
                            ),
                            html.Label("Team", className="hide", id="id-release-team-label"),
                            dcc.Dropdown(
                                id="id-release-team-value",
                                className="dcc-dropdown hide",
                                disabled=True,
                                clearable=False
                            ),
                            html.Label("Type", className="hide", id="id-release-type"),
                            dcc.Dropdown(
                                id="id-release-selector",
                                className="dcc-dropdown hide",
                                options={0: 'AffectsVersion', 1: 'FixVersion'},
                                value=0
                            ),
                            html.Label("Charts", className="hide", id="id-release-chart-label"),
                            dcc.Dropdown(
                                id="id-release-project-charts",
                                className="dcc-dropdown hide",
                                options=dict(
                                    opendefects='Open Defects',
                                    bypriority='By Priority',
                                    details='Details'
                                ),
                                disabled=True,
                                value='details',
                                clearable=False
                            ),

                            # html.Label("Hide Done Issues", className="hide"),
                            # dcc.Dropdown(
                            #     id="id-filter-done", className="dcc-dropdown hide", searchable=False, clearable=False,
                            #     options=[dict(label='No', value='No'), dict(label='Yes', value='Yes')],
                            #     value='No'
                            # ),
                            html.I(className="line-divider"),
                            html.Label("Counts: Priority & Severity", className="hide", id="id-release-counts"),
                            dbc.Alert(id="id-release-alert", className="hide", color='info'),
                            dbc.Alert(id="id-release-urgency", className="hide", color='info')
                            # html.Label("Time Taken"),
                            # dcc.Loading(id="id-fetch-time-versions"),
                        ],
                        className="side-menu",
                        # id="id-side-menu-panel-version"
                        id=dict(type='side-panel', index='version')
                    ),
                    html.Div(
                        children=[
                            html.Div(
                                children=[
                                    html.Button(
                                        className="format-button",
                                        # id="id-toggle-button-version"
                                        # id={'type': 'toggle-panel', 'index': "version"}
                                        id=dict(type='toggle-panel', index="version")
                                    ),
                                ],
                            ),
                            html.Div(children=[
                                html.Label("Release Roadmap"),
                            ], className="header-menu",
                                id="id-version-main-header"),
                            # html.Div(children=[html.A(id="id-excel-download")], className="header-right-side"),
                            html.Div(children=[
                                html.Button(
                                    id="id-excel-download",
                                    className="format-export-button"
                                    # children=[html.I(className="fas fa-file-export")],
                                ),
                                de.Download(id="id-download")
                            ], className="header-right-side"),
                            html.Div("", className="middle-vacant"),
                            html.Article(
                                children=[
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer one",
                                        id="id-version-layer-1"
                                    ), type='graph'),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer two",
                                        id="id-version-layer-2"
                                    ), type='circle'),
                                    html.Div(
                                        children=[""],
                                        className="layer three show",
                                        id="id-version-layer-3"
                                    ),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer five",
                                        id="id-version-layer-5"
                                    )),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer six",
                                        id="id-version-layer-6"
                                    )),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer seven",
                                        id="id-version-layer-7"
                                    )),
                                    dcc.Loading(html.Div(
                                        children=[""],
                                        className="layer eight",
                                        id="id-version-layer-8"
                                    )),
                                ],
                                id="id-version-details",
                                className="main-area",
                            ),
                            html.Div("", className="side-vacant", id=""),
                            html.Div("", className="bottom-left"),
                            html.Div(children=[
                                html.Ul(
                                    children=[
                                        html.Li(className="active", id="id-version-bullet-3"),
                                        html.Li(id="id-version-bullet-8"),
                                        html.Li(id="id-version-bullet-7"),
                                        html.Li(id="id-version-bullet-5"),
                                        html.Li(id="id-version-bullet-6"),
                                        html.Li(id="id-version-bullet-2"),
                                        html.Li(id="id-version-bullet-1"),

                                    ],
                                    className="bullets"
                                )],
                                className="bottom-middle"
                            ),
                            html.Div("", className="bottom-right"),
                        ],
                        className="display-area",
                        # className="display-area-3level",
                        # id="id-main-display-panel-version"
                        id=dict(type='main-panel', index='version')
                    ),
                ],
                className="page-grid"
            )

        elif url in [f'/{project}/svn']:
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Label("Branch"),
                            dcc.Dropdown(
                                options=branch_options,
                                className="dcc-dropdown",
                                clearable=False,
                                searchable=True,
                                id="id-svn-branch-dash",
                                multi=False,
                            ),
                            html.Label("Package"),
                            dcc.Loading(
                                dcc.Dropdown(
                                    id="id-svn-package-dash",
                                    className="dcc-dropdown",
                                    options=package_options,
                                    clearable=False,
                                    searchable=True,
                                    multi=False,
                                )
                            ),
                            html.Label("Filter By: Issuetype"),
                            dcc.Dropdown(
                                id="id-svn-issuetype",
                                className="dcc-dropdown",
                                options=dict(
                                    ALL='ALL', epic='Epic', standard='Standard',
                                    bugs='Bugs', subtask='Sub-task', invalid='Invalid'
                                ),
                                value='ALL',
                                multi=False, clearable=False, searchable=False
                            ),
                            html.Label("Show: FixVersions"),
                            dcc.Dropdown(
                                id="id-svn-fixversion",
                                className="dcc-dropdown",
                                options=dict(ALL='ALL', missing='Missing'),
                                value='ALL',
                                multi=False, clearable=False, searchable=False
                            ),
                            html.Label("No of Records"),
                            dbc.Alert(id="id-svn-show-counts")
                        ],
                        className="side-menu",
                        id=dict(type='side-panel', index='svn')
                    ),
                    html.Div(
                        children=[
                            html.Button(
                                className="format-button",
                                id=dict(type='toggle-panel', index="svn")
                            ),
                            html.Div(
                                children=[
                                    html.Label("SVN JIRA Mapping"),
                                ], className="header-menu",
                                id="id-svn-main-header"
                            ),
                            html.Div(
                                children=[
                                    html.Button(
                                        id="id-svn-excel-download",
                                        className="format-export-button"
                                    ),
                                    de.Download(id="id-svn-download")
                                ], className="header-right-side"
                            ),
                            html.Div("", className="middle-vacant"),
                            html.Article(
                                children=[
                                    dcc.Loading(html.Div(
                                        children=[html.Div("")],
                                        className="layer two show",
                                        id="id-svn-layer-2"
                                    ), ),
                                ],
                                id="id-svn-details",
                                className="main-area",
                            ),
                            html.Div("", className="side-vacant"),
                            html.Div("", className="bottom-left"),
                            html.Div(children=[
                                html.Ul(
                                    children=[
                                        html.Li(className="active", id="id-svn-bullet-2"),
                                        html.Li(id="id-svn-bullet-3"),

                                    ],
                                    className="bullets"
                                )],
                                className="bottom-middle"
                            ),
                            html.Div("", className="bottom-right"),
                        ],
                        className="display-area",
                        id=dict(type='main-panel', index='svn')
                    )
                ],
                className="page-grid"
            )
        elif url in [f'/{project}/description']:
            options_team = [{'label': team_name[0], 'value': team_name[0]} for team_name in db.get_team_names(pg_session)]
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Label(children=["Team"], className="", id="id-label-desc-team"),
                            dcc.Dropdown(
                                id="id-team-desc", className="dcc-dropdown",
                                options=options_team,
                                value='PLAT_REWARDS',
                                clearable=False,
                                searchable=False
                            ),
                            html.Label(children=["Team Members"]),
                            dcc.Dropdown(id="id-team-member-desc", className="dcc-dropdown", clearable=False,
                                         searchable=False, ),
                            html.Label(children=["Date Range"]),
                            dcc.DatePickerRange(
                                id="id-desc-date-picker-range",
                                initial_visible_month=datetime.date.today(),
                                # min_date_allowed=datetime.date(2022, 5, 30),
                                max_date_allowed=datetime.date.today() - datetime.timedelta(days=1),
                                start_date=datetime.date(2022, 5, 30),
                                end_date=datetime.date.today() - datetime.timedelta(days=1),
                                className="date-picker"
                            ),
                            html.I(className="line-divider"),
                        ],
                        className="side-menu",
                        id=dict(type='side-panel', index='description')
                    ),
                    html.Div(children=[
                        html.Div(
                            children=[
                                html.Button(
                                    className="format-button",
                                    id=dict(type='toggle-panel', index="description")
                                ),
                            ],
                        ),
                        html.Div(
                            "Description validation: Analysis, Design, Coding and Unit Test",
                            className="header-menu-aging",
                            id="id-compliance-desc-header"
                        ),
                        html.Div("", className="header-right-side"),
                        html.Div("", className="middle-vacant"),
                        html.Article(
                            children=[
                                dcc.Loading(html.Div(className="layer three show", id="id-comp-description-3")),
                                dcc.Loading(html.Div(className="layer five", id="id-comp-description-5")),
                            ],
                            id="id-compliance-description",
                            className="main-area",
                        ),
                        html.Div("", className="side-vacant", id=""),
                        html.Div("", className="bottom-left"),
                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(id="id-desc-bullet-3", className="active"),
                                    html.Li(id="id-desc-bullet-5"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-aging"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                        className="display-area",
                        id=dict(type='main-panel', index='description')
                    )
                ],
                className="page-grid"
            )
        elif url in [f'/{project}/issueaging']:
            df = db.get_open_issues(pg_session)
            df_standardissue = df[(df["issuetype"] != 'Epic') & (~df['isSubTask'])]['issuetype'].copy(deep=True)
            df_subtaskissue = df[(df["issuetype"] != 'Epic') & (df['isSubTask'])]['issuetype'].copy(deep=True)
            standard_series = df_standardissue.value_counts()
            subtask_series = df_subtaskissue.value_counts()
            option_standard = [{'label': f'{key}, {standard_series.get(key)}', 'value': key} for key in
                               standard_series.keys()]
            option_subtask = [{'label': f'{key}, {subtask_series.get(key)}', 'value': key} for key in subtask_series.keys()]
            return html.Div(
                children=[
                    html.Div(children=[
                        html.Label("Epic"),
                        dcc.Dropdown(
                            id='id-epic-issue',
                            options=[{'label': 'Epic', 'value': 'Epic'}],
                            className="dcc-dropdown",
                        ),
                        html.Label("Standard Issue Type"),
                        dcc.Dropdown(id='id-standard-issue', options=option_standard, className="dcc-dropdown", ),
                        html.Label("SubTask Issue Type"),
                        dcc.Dropdown(id='id-subtask-issue', options=option_subtask, className="dcc-dropdown", ),
                        html.Label("Created or Updated"),
                        dcc.Dropdown(
                            id='id-issuetype-when',
                            options=[{'label': 'created', 'value': 'created'}, {'label': 'updated', 'value': 'updated'}],
                            value='created',
                            clearable=False,
                            searchable=False
                        ),
                        html.Label('Time Taken'),
                        dcc.Loading(id="id-fetch-time-issuetype"),
                    ],
                        className="side-menu"),
                    html.Div(children=[
                        # html.Div(children=[html.Button(className="format-button", id="id-toggle-button-aging"), ], ),
                        html.Div(" ", className="header-menu-aging", id="id-version-main-header"),
                        # html.Div("", className="header-right-side"),
                        # html.Div("", className="middle-vacant"),
                        # html.Div(id='id-issuetype-graph'),
                        html.Div(
                            children=[
                                dcc.Loading(dcc.Graph(className="layer one show", id="id-issuetype-graph-1")),
                                dcc.Loading(dcc.Graph(className="layer two", id="id-issuetype-graph-2")),
                                dcc.Loading(dcc.Graph(className="layer three", id="id-issuetype-graph-3")),
                            ],
                            id="id-issuetype-graph",
                            className="main-area-aging",
                        ),

                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(className="active", id="id-aging-bullet-1"),
                                    html.Li(id="id-aging-bullet-2"),
                                    html.Li(id="id-aging-bullet-3"),
                                    html.Li(id="id-aging-bullet-4"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-aging"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                        className="display-area-3level")
                ],
                className="page-grid"
            )
        # elif url in [f'/{project}/updateteams']:
        #     # Update Teams table
        #     options_team = [{'label': i[0], 'value': i[0]} for i in db.get_team_names(project)]
        #     df = db.get_user_detail()
        #     df.dropna(inplace=True)
        #     options_individual = [{'value': getattr(row, "accountId"), 'label': getattr(row, "displayName")} for row in
        #                           df.itertuples()]
        #     return html.Div(
        #         children=[
        #             html.Div(children=[
        #                 html.Label(children=["Team"], id="id-label-team"),
        #                 dcc.Dropdown(
        #                     id="id-team-admin", className="dcc-dropdown",
        #                     options=options_team,
        #                     value='PLAT_REWARDS'
        #                 ),
        #                 html.Label(children=["Individual"]),
        #                 dcc.Dropdown(
        #                     id="id-individual-admin", className="dcc-dropdown",
        #                     options=options_individual,
        #                 ),
        #                 html.Button("Add Row", className="format_button", id="id-add-teams", n_clicks=0),
        #                 html.Button("Update", className="format_button", id="id-update-teams", n_clicks=0),
        #                 dbc.Alert(id="id-update-team-alert")
        #             ], className="side-menu"),
        #             html.Div(children=[
        #                 dash_table.DataTable(
        #                     id="id-teams-table",
        #                     style_header={
        #                         'backgroundColor': 'rgb(30, 30, 30)',
        #                         'color': 'white'
        #                     },
        #                     style_data={
        #                         'backgroundColor': 'rgb(50, 50, 50)',
        #                         'color': 'white'
        #                     },
        #                     # hidden_columns=['id', 'accountId', 'Account Type', 'Status'],
        #                     style_data_conditional=
        #                     [
        #                         {
        #                             'if': {'column_editable': False},
        #                             'backgroundColor': 'rgb(30, 30, 30)',
        #                             'color': 'white'
        #                         },
        #                         {'if': {'column_id': 'accountId'}, 'display': 'None'},
        #                         {'if': {'column_id': 'active'}, 'backgroundColor': 'white'}
        #                     ] +
        #                     [
        #                         {'if': {'column_id': c}, 'display': 'None'} for c in
        #                         ['accountId', 'Account Type', 'Status', 'id']
        #                     ],
        #                     style_header_conditional=[
        #                         {'if': {'column_id': col, },
        #                          'display': 'None', } for col in ['id', 'accountId', 'Account Type', 'Status']
        #                     ],
        #                     dropdown={
        #                         'active': {
        #                             'options': [
        #                                 {'label': 'Active', 'value': 'Active'},
        #                                 {'label': 'Inactive', 'value': 'Inactive'}
        #                             ]
        #                         }
        #                     }
        #                 )
        #             ],
        #                 className="display-area-3level"
        #             )
        #         ],
        #         className="page-grid"
        #     )
        elif url in [f'/{project}/compliance']:
            options_team = [{'label': i[0], 'value': i[0]} for i in db.get_team_names(pg_session)]

            return html.Div(
                children=[
                    html.Div(children=[
                        html.Label(children=["Team"], className="", id="id-label-comp-team"),
                        dcc.Dropdown(
                            id="id-team-comp", className="dcc-dropdown",
                            options=options_team,
                            value='PLAT_REWARDS'
                        ),
                        html.I(className="line-divider"),
                        html.Label('Time Taken'),
                        dcc.Loading(id="id-fetch-time-comp"),
                    ],
                        className="side-menu"),
                    html.Div(children=[
                        # html.Div(children=[html.Button(className="format-button", id="id-toggle-button-aging"), ], ),
                        html.Div(" ", className="header-menu-aging", id="id-compliance-header"),
                        # html.Div("", className="header-right-side"),
                        # html.Div("", className="middle-vacant"),
                        # html.Div(id='id-issuetype-graph'),
                        html.Div(
                            children=[
                                dcc.Loading(dcc.Graph(className="layer two show", id="id-comp-graph-2")),
                                dcc.Loading(dcc.Graph(className="layer three", id="id-comp-graph-3")),
                                dcc.Loading(dcc.Graph(className="layer five", id="id-comp-graph-5")),
                                dcc.Loading(dcc.Graph(className="layer six", id="id-comp-graph-6")),
                                dcc.Loading(dcc.Graph(className="layer seven", id="id-comp-data-7")),
                            ],
                            id="id-compliance-graph",
                            className="main-area-aging",
                        ),

                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(id="id-comp-bullet-2", className="active"),
                                    html.Li(id="id-comp-bullet-3"),
                                    html.Li(id="id-comp-bullet-5"),
                                    html.Li(id="id-comp-bullet-6"),
                                    html.Li(id="id-comp-bullet-7"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-aging"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                        className="display-area-3level")
                ],
                className="page-grid"
            )
        # elif url in [f'/{project}/initattr']:
        #     # colorscale = [[0, '#4d004c'], [.5, '#f2e5ff'], [1, '#ffffff']]
        #     rowEvenColor = 'lightgrey'
        #     rowOddColor = 'white'
        #     df = db.get_initiative_attribute(project)
        #     _row, _ = df.shape
        #     _row = int(_row / 2) + 1
        #     fig = go.Figure(
        #         data=[go.Table(
        #             header=dict(values=list(df.columns), fill_color='royalblue',
        #                         align='center', font=dict(color='white', size=12)),
        #             cells=dict(
        #                 values=[df.key, df.project, df.summary, df.status],
        #                 fill_color=[[rowOddColor, rowEvenColor] * _row],
        #                 align='left', font=dict(color='black', size=12))
        #         )]
        #     )
        #
        #     df = db.get_missing_initiative_attribute(project)
        #     fig2 = go.Figure(
        #         data=[go.Table(
        #             header=dict(values=list(df.columns), fill_color='royalblue', align='center'),
        #             cells=dict(values=[df.key, df.summary, df.status], fill_color='lavender', align='left')
        #         )]
        #     )
        #     # fig = ff.create_table(df,  height_constant=20, colorscale=colorscale)
        #     # fig.update_layout(title_text='Initiative to Project Mapping')
        #     return html.Div(
        #         children=[
        #             html.Label("Initiative Logical Grouping"),
        #             dcc.Graph(figure=fig),
        #             dcc.Graph(figure=fig2)
        #         ]
        #
        #     )
        # elif url in [f'/{project}/updateinitattribs']:
        #     if current_user.id in roles_and_permissions and user_role in roles_and_permissions[current_user.id]:
        #         df = db.get_initiative_attribs(project)
        #
        #         return html.Div(
        #             children=[
        #                 dash_table.DataTable(
        #                     df.to_dict('records'),
        #                     id="id-data-table-init-attrib",
        #                     columns=[
        #                         {"name": col, "id": col, 'editable': j} for col, j in zip(
        #                             df.columns.to_numpy(),
        #                             [False, False, True, True, True, False, False]
        #                         )
        #                     ],
        #                     page_size=10,
        #                     # page_action='none',
        #                     fixed_rows={'headers': True},
        #                     style_table={'height': '500px', 'overflowY': 'auto'},
        #                     style_data={
        #                         'whiteSpace': 'normal',
        #                         'height': 'auto',
        #                         'lineHeight': '15px'
        #                     },
        #                     style_cell={
        #                         'overflow': 'hidden',
        #                         'textOverflow': 'ellipsis',
        #                         'maxWidth': 0,
        #                         'textAlign': 'left'
        #                     },
        #                     style_cell_conditional=[
        #                         {
        #                             'if': {'column_id': c},
        #                             'textAlign': 'right'
        #                         } for c in ['initiative_id']
        #                     ],
        #                     tooltip_data=[
        #                         {
        #                             column: {'value': str(value), 'type': 'markdown'}
        #                             for column, value in row.items()
        #                         } for row in df.to_dict('records')
        #                     ],
        #                     tooltip_duration=None,
        #                 ),
        #
        #                 html.Button("Update", className="format_button", id="id-update-init-attribs"),
        #                 html.Br(),
        #                 dbc.Alert(id="id-update-details")
        #             ],
        #             className="container_custom"
        #         )
        #     else:
        #         return html.Div(f"You don't have permission to view this page: {user_role}")

        elif url in [f'{quote("/plat/SQL Code Review")}']:
            df = db.get_open_code_reviews(pg_session)

            return html.Div(
                children=[
                    html.Div(children=[
                        html.Label(children=["Versions"], className="", id="id-label-code-version"),
                        dcc.Dropdown(
                            id="id-code-version", className="dcc-dropdown",
                            options=df['affected'].unique(),
                        ),
                        html.I(className="line-divider"),
                        html.Label('Time Taken'),
                        dcc.Loading(id="id-fetch-time-comp"),
                    ],
                        className="side-menu"),
                    html.Div(children=[
                        html.Div("SQL Code Review", className="header-menu-aging", id="id-sql-code-header"),
                        html.Div(
                            children=[
                                (
                                    html.Div(className="layer ten show", id="id-review-graph-10",
                                             children=[
                                                 html.Div(id="id-review-graph-child-1"),
                                                 html.Div(id="id-review-graph-child-2"),
                                                 html.Div(id="id-review-graph-child-3"),
                                                 html.Div(id="id-review-graph-child-4"),
                                             ]
                                             )
                                ),
                                dcc.Loading(html.Div(className="layer three", id="id-review-graph-3")),
                            ],
                            id="id-compliance-graph",
                            className="main-area-aging",
                        ),

                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(id="id-review-bullet-10", className="active"),
                                    html.Li(id="id-review-bullet-3"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-aging"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                        className="display-area-3level")
                ], className="page-grid"
            )
            # rowEvenColor = 'lightgrey'
            # rowOddColor = 'white'
            # df = db.get_open_code_reviews('plat')
            # _row, _ = df.shape
            #
            # col_list = ['#', 'key', 'summary', 'status', 'assignee']
            # fig = go.Figure(
            #     data=[go.Table(
            #         header=dict(values=col_list, fill_color='royalblue',
            #                     align='center', font=dict(color='white', size=12)),
            #         cells=dict(
            #             values=[(df.index + 1), df.key, df.summary, df.status, df.assignee],
            #             fill_color=[[rowOddColor, rowEvenColor] * int(_row/2 + 1)],
            #             align='left', font=dict(color='black', size=12))
            #     )]
            # )
            # return html.Div(
            #     children=[
            #         dcc.Graph(figure=fig),
            #     ]
            # )
        elif url in [f'/plat/releasenotes']:
            project = 'plat'
            dff = db.get_versions(pg_session)[['id',
                                            'name', 'archived', 'released', 'issuesCount', 'issuesUnresolvedCount',
                                            'issuesFixedCount',
                                            'issuesAffectedCount']].copy(deep=True)

            dff = dff.sort_values(by=['name'], ascending=False)
            options = [dict(label=name, value=name) for name, id_ in dff[['name', 'id']].values]
            platform_version = db.get_platform_version(pg_session)
            platform_options = [dict(label=item, value=item) for item in platform_version]

            # branch_list = db.get_branch_details(project)
            # branch_options = [dict(label=item, value=item) for item in branch_list]
            # package_list = db.get_package_details('plat')
            # package_options = [dict(label=item, value=item) for item in package_list]
            all_cookies = dict(flask.request.cookies)
            if 'pf_cookie' in all_cookies:
                pf_version = all_cookies['pf_cookie']
                print(f"cookie value = {pf_version}")
            else:
                pf_version = ""

            return html.Div(
                children=[
                    dcc.Checklist(
                        options=[
                            {'label': 'Cookie', 'value': 'Cookie'},
                            {'label': 'Jazz', 'value': 'Jazz'}
                        ],
                        value=['Cookie', 'Jazz'],
                        inline=True,
                        className="container-check-box",
                        persistence=True,
                        persistence_type='local',
                        id='id-rn-project'
                    ),
                    html.Div(
                        children=[
                            html.Label(
                                "App From Version:",
                                style={
                                    'margin-left': '50px', 'margin-right': '10px'
                                }
                            ),
                            dcc.Dropdown(
                                id="id-from-dash_app-version",
                                options=options,
                                searchable=True,
                                clearable=True,
                                className="dcc-dropdown",
                                persistence=True,
                            )
                        ],
                        className="container-text-dropdown"
                    ),
                    html.Div(
                        children=[
                            html.Label(
                                "App  To  Version:",
                                style={
                                    'margin-left': '50px', 'margin-right': '10px'
                                }
                            ),
                            dcc.Dropdown(
                                id="id-to-dash_app-version",
                                className="dcc-dropdown",
                            )
                        ],
                        className="container-text-dropdown"
                    ),
                    dcc.Textarea(id="id-label-textarea",
                                 style={'margin-top': '10px', 'width': '100%', 'border-radius': '10px'}),
                    html.Div(
                        children=[
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Branch"), ]),
                                    dcc.Dropdown(
                                        options=branch_options,
                                        className="dcc-dropdown",
                                        multi=True,
                                        searchable=True,
                                        id="id-svn-branch",
                                        persistence=True
                                    ),
                                ], className="cls-format-rn-ul"
                            ),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Package"), ]),
                                    dcc.Dropdown(
                                        options=package_options,
                                        className="dcc-dropdown",
                                        multi=True,
                                        searchable=True,
                                        id="id-svn-package",
                                        persistence=True
                                    ),
                                ], className="cls-format-rn-ul"
                            ),
                        ]
                    ),

                    html.Ul(
                        children=[
                            html.Li(children=[html.Label("Platform Version"), ]),
                            html.Li(children=[
                                # dcc.Input(
                                #     placeholder="Platform Version",
                                #     id="id-rn-pf-version",
                                #     debounce=True,
                                #     persistence=True
                                # ),
                                dcc.Dropdown(
                                    options=platform_options,
                                    # value=platform_version[0],
                                    className="dcc-dropdown",
                                    multi=True,
                                    searchable=True,
                                    id="id-rn-pf-version",
                                    persistence=True
                                ),
                                dcc.Checklist(['Yes'], ['Yes'], persistence=True, id="id-gen-pf-rn")
                            ]),
                            # html.Li(children=[html.Label("Get Platform RN")]),
                            # html.Li(dcc.Checklist(['Yes'], ['Yes'], persistence=True))
                        ], className="cls-format-rn-ul"
                    ),
                    html.Ul(
                        children=[
                            html.Li(children=[html.Label("KMS Version"), ]),
                            html.Li(children=[
                                dcc.Input(
                                    placeholder="KMS Version",
                                    id="id-rn-kms-version",
                                    debounce=True,
                                    persistence=True
                                ),
                                dcc.Checklist(['Yes'], ['Yes'], persistence=True, id="id-gen-kms-rn")
                            ])
                        ], className="cls-format-rn-ul"
                    ),

                    html.Details(
                        children=[
                            html.Summary("Cookie"),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Reporting Version"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="Reporting Version",
                                            id="id-rn-cookie-rpt-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])
                                ]
                            ),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("PDF Kafka"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="PDF Kafka Version",
                                            id="id-rn-cookie-kafka-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])

                                ]
                            ),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Report Delivery"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="Report Delivery Version",
                                            id="id-rn-cookie-rpt-delivery-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])

                                ]
                            ),
                        ],
                        open=True,
                        className="rn-details details-hide",
                        id="id-rn-cookie-details"
                    ),
                    html.Details(
                        children=[
                            html.Summary("Jazz"),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Reporting Version"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="Reporting Version",
                                            id="id-rn-jazz-rpt-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])
                                ]
                            ),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("PDF Kafka"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="PDF Kafka Version",
                                            id="id-rn-jazz-kafka-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])

                                ]
                            ),
                            html.Ul(
                                children=[
                                    html.Li(children=[html.Label("Report Delivery"), ]),
                                    html.Li(children=[
                                        dcc.Input(
                                            placeholder="Report Delivery Version",
                                            id="id-rn-jazz-rpt-delivery-version",
                                            debounce=True,
                                            persistence=True
                                        ),
                                    ])

                                ]
                            ),
                        ],
                        open=True,
                        className="rn-details details-hide",
                        id="id-rn-jazz-details"
                    ),

                    html.Div(id="id-pf-version-output"),
                    html.Div(
                        children=[
                            html.Button(id="id-download-rn", children="Download RN", className="format_button",
                                        disabled=True),
                        ],
                        className="container-login-button"
                    ),
                    dcc.Download(id="id-download-rn-xlsx"),
                    html.Div(id="id-test-rn-area")
                ],
                className="container_custom"
            )
        elif url in ['/plat/estimates']:
            if os.name == "nt":
                default_value = '<EMAIL>'
            else:
                default_value = ""

            df = db.get_user_detail()
            df.drop(df[df['emailAddress'] == 'NaN'].index, inplace=True)

            suggestions = df['emailAddress'].values.tolist()

            return [html.Div(children=[
                html.Ul(id='id-progressbar-epic', children=[
                    html.Li(children=["O365 Login id"], id="id-1st-level-epic"),
                    html.Li(children=["Verify Login"], id="id-2nd-level-epic"),
                    html.Li(children=["Upload File"], id="id-3rd-level-epic"),
                    html.Li(children=["Done"], id="id-4th-level-epic"),
                ], className="progressbar"),
            ], className="container_custom")] + [html.Div(children=[
                html.Ul(children=[
                    html.Li(children=[html.Div(children=[
                        html.Datalist(id="id-suggested-emails-epic",
                                      children=[html.Option(value=word) for word in suggestions]),
                        html.Div(
                            children=[
                                dcc.Input(
                                    type='email', id="id-login-user-epic", list="id-suggested-emails-epic",
                                    placeholder="O365 email id",
                                    value=default_value,
                                    className="input-creds",
                                    debounce=True
                                ),
                                html.Span(className='focus-input-creds'),
                                html.Span(children=[html.I(className="fa fa-envelope")], className="symbol-input-creds"),
                            ],
                            className='wrap-input-creds',
                        ),
                    ]), ]),
                    html.Li("", className="check-container", id="id-valid-login-name-epic")
                ], className="login-container-ul"),
                html.Div(
                    html.Ul(
                        children=[
                            html.Li(children=[
                                dcc.Input(type='password', id="id-login-passwd-epic", placeholder="Password",
                                          className="input-creds", debounce=True),
                                html.Span(className='focus-input-creds'),
                                html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds")
                            ], className='wrap-input-creds'),
                            html.Li(children=[dcc.Loading(html.Div(""))], className="check-container",
                                    id="id-valid-jira-cred-epic")
                        ], className="login-container-ul"
                    ),
                ),
                dcc.Upload(
                    [
                        'Drag and Drop or ',
                        html.A('Select a File'),
                        html.P("Only File with extension txt, csv allowed", id="id-namefile-epic")
                    ],
                    multiple=False,
                    disabled=True,
                    style={
                        'width': '95%',
                        'height': '120px',
                        'lineHeight': '60px',
                        'borderWidth': '1px',
                        'borderStyle': 'dashed',
                        'borderRadius': '5px',
                        'textAlign': 'center',
                        'border-style': 'dashed',
                        'background-color': 'gray',
                        'margin-bottom': '10px'
                    },
                    id="id-file-upload-epic",
                ),
                html.Div(children=[
                    html.Button(id="id-run-button-id", children="Run Job!", className="format_button", disabled=False),
                    html.Button(id="id_cancel_button_id", children="Cancel Running Job!", className="format_button",
                                disabled=True),
                ], className="container-login-button"),
                html.Hr(className='white'),
                # html.Div(id="id-progress-status", children=[html.P("Pls upload file!!")], className="details"),
                dbc.Alert(id="id-progress-status", ),
                html.Div(id="id-file-output"),
                dcc.Store(id='id-store-epic-list'),
                dcc.Graph(id="id-progress-bar-graph-epic", figure=data.helper.make_progress_graph(0, 10)),
            ], className="container_custom")]
        elif url in ['/plat/transition']:
            if os.name == "nt":
                # default_value = '<EMAIL>'
                default_value = ""
            else:
                default_value = ""
            df = db.get_user_detail()
            df.drop(df[df['emailAddress'] == 'NaN'].index, inplace=True)

            suggestions = df['emailAddress'].values.tolist()

            return [html.Div(children=[
                html.Ul(id='id-progressbar', children=[
                    html.Li(children=["O365 Login id"], id="id-1st-level"),
                    html.Li(children=["Verify Login"], id="id-2nd-level"),
                    html.Li(children=["Upload File"], id="id-3rd-level"),
                    html.Li(children=["Done"], id="id-4th-level"),
                ], className="progressbar"),
            ], className="container_custom")] + [html.Div(children=[
                html.Ul(children=[
                    html.Li(children=[html.Div(children=[
                        html.Datalist(id="id-suggested-emails", children=[html.Option(value=word) for word in suggestions]),
                        html.Div(
                            children=[
                                dcc.Input(
                                    type='email', id="id-login-user", list="id-suggested-emails",
                                    placeholder="O365 email id",
                                    value=default_value,
                                    className="input-creds",
                                    debounce=True
                                ),
                                html.Span(className='focus-input-creds'),
                                html.Span(children=[html.I(className="fa fa-envelope")], className="symbol-input-creds"),
                            ],
                            className='wrap-input-creds', tabIndex="1"
                        ),
                    ]), ]),
                    html.Li("", className="check-container", id="id-valid-login-name")
                ], className="login-container-ul"),
                html.Div(
                    html.Ul(
                        children=[
                            html.Li(children=[
                                dcc.Input(type='password', id="id-login-passwd", placeholder="Password",
                                          className="input-creds", debounce=True),
                                html.Span(className='focus-input-creds'),
                                html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds")
                            ], className='wrap-input-creds'),
                            html.Li(children=[dcc.Loading(html.Div(""))], className="check-container",
                                    id="id-valid-jira-cred")
                        ], className="login-container-ul"
                    ),
                ),
                dcc.Upload(
                    [
                        'Drag and Drop or ',
                        html.A('Select a File'),
                        html.P("Only txt, csv, xls, xlsx, ods file types allowed", id="namefile")
                    ],
                    multiple=False,
                    disabled=True,
                    style={
                        'width': '500px',
                        'height': '120px',
                        'lineHeight': '60px',
                        'borderWidth': '1px',
                        'borderStyle': 'dashed',
                        'borderRadius': '5px',
                        'textAlign': 'center',
                        'border-style': 'dashed',
                        'background-color': 'gray',
                        'margin-bottom': '10px'
                    },
                    id="id-file-upload-transition",
                ),
                html.Div(
                    children=[
                        html.Button(id="id-run-button-transition", children="Run Job!", className="format_button",
                                    disabled=True),
                        html.Button(id="id_cancel_button-transition", children="Cancel Running Job!",
                                    className="format_button", disabled=True),
                    ],
                    className="container-login-button"
                ),
                html.Hr(className='white'),
                # html.Div(id="id-progress-status-transition", children=[html.P("Pls upload file!!")], className="details"),
                dbc.Alert(id="id-progress-status-transition"),
                html.Hr(className='white'),
                # html.Div(id='output-data-upload'),
                html.Hr(className='white'),
                html.Div(id="id-file-output-transition"),
                dcc.Graph(id="progress_bar_graph", figure=data.helper.make_progress_graph(0, 10)),
                dcc.Store(id='id-store-issue-list')
            ], className="container_custom")]
        elif url in [f'/{project}/coretrack']:
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            dcc.Input(
                                id="id-ct-user", type='text',
                                placeholder="CoreTrack Login id",
                                className="input-creds",
                                debounce=True, persistence=True
                            ),
                            html.Span(className='focus-input-creds'),
                            html.Span(children=[html.I(className="fa fa-envelope")], className="symbol-input-creds"),
                        ],
                        className='wrap-input-creds', tabIndex="1"
                    ),
                    html.Div(children=[
                        dcc.Input(type='password', id="id-ct-passwd", placeholder="password",
                                  className="input-creds", debounce=True, persistence=True),
                        html.Span(className='focus-input-creds'),
                        html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds")
                    ], className='wrap-input-creds'),
                    dcc.Upload(
                        [
                            'Drag and Drop or ',
                            html.A('Select a File'),
                            html.P("Only xls, xlsx, ods file types allowed", id="namefile")
                        ],
                        multiple=False,
                        disabled=False,

                        style={
                            'width': '500px',
                            'height': '120px',
                            'lineHeight': '60px',
                            'borderWidth': '1px',
                            'borderStyle': 'dashed',
                            'borderRadius': '5px',
                            'textAlign': 'center',
                            'border-style': 'dashed',
                            'background-color': 'gray',
                            'margin-bottom': '10px'
                        },
                        id="id-ct-file-upload",
                    ),
                    # dcc.Dropdown(
                    #     id="id-ct-env",
                    #     options=dict(test="Test", prod="Prod"),
                    #     value="test",
                    #     className="dcc-dropdown",
                    #     clearable=False, searchable=False
                    # ),
                    html.Div(
                        children=
                        [
                            html.Label("CoreTrack URL"),
                            dcc.RadioItems(
                                options=[
                                    {'label': 'Test', 'value': 'test'},
                                    {'label': 'Live', 'value': 'prod'},
                                ],
                                value='test',
                                inline=True,
                                id="id-ct-env", className="dcc-radio-button"
                            ),
                        ],
                        className="container-radio-button"
                    ),
                    html.Div(
                        children=
                        [
                            html.Label("Operation"),
                            dcc.RadioItems(
                                ['Search', 'Create'], 'Search',
                                inline=True,
                                id="id-ct-options", className="dcc-radio-button"
                            ),
                        ],
                        className="container-radio-button"
                    ),

                    html.Div(
                        children=[
                            html.Button(id="id-run-button-ct", children="Run Job!", className="format_button"),
                            html.Button(id="id-cancel-button-ct", children="Cancel Running Job!",
                                        className="format_button"),
                            html.Button(id="id-download-ct", children="Download File", className="format_button"),
                        ],
                        className="container-login-button"
                    ),
                    html.Hr(className='white'),
                    dbc.Alert(id="id-coretrack-alert"),
                    dcc.Graph(id="id-progress-bar-graph-ct", figure=data.helper.make_progress_graph(0, 10)),

                    # html.Button("Download File", id="id-download-ct", className="format_button"),
                    dcc.Download(id="id-download-ct-df-xlsx"),
                    html.Div(id="id-message-board-ct", className="details"),
                    html.Div(id="id-file-ct-uploaded"),
                    dcc.Store(id="id-ct-details"),
                    dcc.Store("id-download-ct", storage_type='session'),
                ],
                className="container_custom"
            )
        elif url in [f'/{project}/svndetails']:
            return html.Div(
                children=[
                    html.Div(
                        children=[
                            html.Button(id="id-refresh-svn", children="Update SVN Repo!", className="format_button"),
                            html.Button(id="id-cancel-refresh-svn", children="Cancel Running Job!",
                                        className="format_button"),
                        ],
                        className="container-login-button"
                    ),
                    html.Hr(className='white'),
                    dbc.Alert(id="id-svn-alert"),
                    html.Hr(className='white'),
                    dcc.Upload(
                        [
                            'Drag and Drop or ',
                            html.A('Select a File'),
                            html.P("Only xls, xlsx, ods file types allowed", id="namefile")
                        ],
                        multiple=False,
                        disabled=False,

                        style={
                            'width': '500px',
                            'height': '120px',
                            'lineHeight': '60px',
                            'borderWidth': '1px',
                            'borderStyle': 'dashed',
                            'borderRadius': '5px',
                            'textAlign': 'center',
                            'border-style': 'dashed',
                            'background-color': 'gray',
                            'margin-bottom': '10px'
                        },
                        id="id-svn-file-upload",
                    ),
                    html.Div(
                        children=[
                            html.Button(id="id-get-matching-ids", children="Run Job!", className="format_button"),
                            html.Button(id="id-cancel-matching-ids", children="Cancel Running Job!",
                                        className="format_button"),
                            html.Button(id="id-download-svn", children="Download File", className="format_button"),
                        ],
                        className="container-login-button"
                    ),
                    html.Div(id="id-svn-df"),
                    html.Div(id="id-svn-dummy"),
                    dcc.Download(id="id-download-svn-xlsx"),
                    dcc.Store(id="id-download-svn-data", storage_type='session')
                ],
                className="container_custom"
            )
        elif url in ['/plat/weekly_report']:

            return html.Div(
                children=[
                    html.Details(
                        children=[
                            html.Summary("Cookie"),
                            html.Div(
                                children=[
                                    html.Label("Add & Remove Versions", className="col"),
                                    html.Div(
                                        children=[
                                            html.Button("+", id="add-filter", n_clicks=0,
                                                        className="release-format-button"),
                                        ], className="col"
                                    ),
                                    html.Div(
                                        children=[
                                            html.Button("-", id="remove-filter", n_clicks=0,
                                                        className="release-format-button"),
                                        ], className="col"
                                    ),
                                ],
                                className="row"
                            ),
                            html.Hr(),

                            html.Div(
                                children=[
                                    html.Label("Release Pattern", className="col"),
                                    html.Label("Release Dropdown", className="col"),
                                    html.Label("Branch Dropdown", className="col"),
                                    html.Label("Report Header", className="col")
                                ],
                                className="row"
                            ),
                            html.Div(children=[], id="id-status-report-div"),

                        ],
                        open=True,
                        className="mb-30 status-report",
                        id="id-status-rpt"
                    ),
                    html.Details(
                        children=[
                            html.Summary("Jazz"),
                            html.Div(
                                children=[
                                    html.Label("Add & Remove Versions", className="col"),
                                    html.Div(
                                        children=[
                                            html.Button("+", id="jazz-add-filter", n_clicks=0,
                                                        className="release-format-button"),
                                        ], className="col"
                                    ),
                                    html.Div(
                                        children=[
                                            html.Button("-", id="jazz-remove-filter", n_clicks=0,
                                                        className="release-format-button"),
                                        ], className="col"
                                    ),
                                ],
                                className="row"
                            ),
                            html.Hr(),

                            html.Div(
                                children=[
                                    html.Label("Release Pattern", className="col"),
                                    html.Label("Release Dropdown", className="col"),
                                    html.Label("Branch Dropdown", className="col"),
                                    html.Label("Report Header", className="col")
                                ],
                                className="row"
                            ),
                            html.Div(children=[], id="id-jazz-status-report-div"),

                        ],
                        open=True,
                        className="status-report",
                        id="id-jazz-status-rpt"
                    ),
                    html.Div(
                        children=[
                            html.P(id="paragraph_id", children=["Button not clicked"]),
                            dcc.Graph(id="progress_bar_graph", figure=data.helper.make_progress_graph(0, 10)),
                        ]
                    ),

                    html.Button(id="button_id", children="Run Job!", n_clicks=0),
                    html.Button(id="cancel_button_id", children="Cancel Running Job!"),
                    html.Div(children=[html.H2("Test Value")], id='log', className="status-report"),
                    dcc.Interval(
                        id='log-update',
                        interval=1 * 1000  # in milliseconds
                    ),
                ],
                className="container"
            )
        elif url in ['/clocks_old']:
            return html.Div(
                children=[
                    html.Div(children=[
                        #     html.Article(
                        #         children=[
                        #             html.Div(className='hours-container',
                        #                      children=[html.Div(className='hours', id="id-hours", )]),
                        #             html.Div(className='minutes-container',
                        #                      children=[html.Div(className='minutes', id="id-minutes")]),
                        #             html.Div(className='seconds-container',
                        #                      children=[html.Div(className='seconds', id="id-seconds")]),
                        #             dcc.Interval(id="interval-component", interval=1 * 1000, max_intervals=0)
                        #         ],
                        #         className='clock simple show'
                        #     )], className="demo-container clocks single"
                        # ),
                        html.Div(
                            children=[
                                html.Div(id="id-dummy-clock", hidden=True, children="some data"),
                                html.Div(
                                    children=[
                                        html.Div(className="info date"),
                                        html.Div(className="info day")
                                    ]
                                ),
                                html.Div(className="dot"),
                                html.Div(
                                    children=[
                                        html.Div(className="hour-hand", id="id-hour-hand"),
                                        html.Div(className="minute-hand"),
                                        html.Div(className="second-hand"),
                                    ]
                                ),
                                html.Div(
                                    children=[
                                        html.Span("3", className="format-span h3"),
                                        html.Span("6", className="format-span h6"),
                                        html.Span("9", className="format-span h9"),
                                        html.Span("12", className="format-span h12"),
                                    ]
                                ),
                                html.Div(className="diallines")
                            ],
                            className="clock"
                        )
                    ])]
            )
        elif url in ['/r&d/creditcard']:
            from data import mermaid_constants as mc
            return html.Div(children=[
                html.Div(
                    children=[
                        html.Div(id="id-capture-mermaid-click", children=[html.Div("")]),
                        html.H4("Context Diagram of services in the credit card industry"),
                        Mermaid(
                            id="id-cc-system-context",
                            chart=mc.credit_card_system_context,
                            config={"securityLevel": "loose", "startOnLoad": True},
                            name="credit_card_system_context",
                        ),
                        html.H4("credit card system containers"),
                        Mermaid(chart=mc.credit_card_system_containers),
                        html.H5("Credit Card Industry"),
                        html.H6("Context Diagram: Services in the credit card industry"),
                        Mermaid(chart=mc.credit_card_services_systems),
                        html.Table(
                            children=[
                                html.Tbody(children=[
                                    html.Tr(children=[
                                        html.Td("Cardholder"),
                                        html.Td("A person who owns a credit card and uses it to make purchases.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Merchant"),
                                        html.Td("A business or entity that accepts credit cards as a form of payment.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Acquirer"),
                                        html.Td(
                                            "A financial institution that processes credit card transactions on behalf of merchants.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Issuer"),
                                        html.Td(
                                            "A financial institution that issues credit cards to cardholders.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Card Network"),
                                        html.Td(
                                            "A third-party network that facilitates the transfer of funds and information between merchants, acquirers, and issuers.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Payment Gateway"),
                                        html.Td(
                                            "A system that enables online transactions by securely transmitting credit card data between a merchant's website and an acquirer.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Processor"),
                                        html.Td(
                                            "A system that processes credit card transactions on behalf of acquirers and issuers.")
                                    ], ),
                                    html.Tr(children=[
                                        html.Td("Supporting Services"),
                                        html.Td(
                                            "A group of services that support the credit card industry, including customer service, fraud detection, and credit scoring.")
                                    ], ),
                                ])
                            ]
                        ),
                        html.P("""
                        This diagram shows a more detailed view of a credit card transaction, with the cardholder making a purchase from a merchant, who then sends the transaction to a payment gateway. The payment gateway securely transmits the transaction to an acquirer, who forwards it to a processor for processing. The processor sends the transaction to the card network, which routes it to the appropriate issuer for approval or denial.
                        In addition to the core transactional systems, the diagram includes several supporting services that are essential to the credit card industry. Customer service helps cardholders and merchants resolve issues related to transactions, while fraud detection systems help identify and prevent fraudulent activity. Credit scoring services help issuers assess the creditworthiness of cardholders and determine their credit limits.
                        """),
                        Mermaid(chart=mc.container_issuer_sub_functions),
                        Mermaid(chart=mc.container_issuer_authorization),
                        Mermaid(chart=mc.container_issuer_card_management),
                        Mermaid(chart=mc.container_issuer_transaction_processing),
                        Mermaid(chart=mc.container_issuer_customer_service),
                        Mermaid(chart=mc.container_card_network),
                        Mermaid(chart=mc.container_card_network_transaction_processing),
                        Mermaid(chart=mc.container_card_network_authorization),
                        Mermaid(chart=mc.container_card_network_clearing_and_settlement),
                        Mermaid(chart=mc.container_processor),
                        html.Ul(children=[
                            html.Li(children=[
                                html.B("Transaction Processing:"),
                                html.P(
                                    "responsible for handling and processing incoming transaction requests from different sources, including card networks, merchants, and other processors. It involves a set of sub-functionalities to validate, route, and settle transactions.")
                            ]),
                            html.Li(children=[
                                html.B("Authorization:"),
                                html.P(
                                    "responsible for authorizing transactions based on different criteria, such as cardholder authentication, fraud detection, and credit limit management. It involves a set of sub-functionalities to check and approve or decline transaction requests")
                            ]),
                            html.Li(children=[
                                html.B("Clearing and Settlement:"),
                                html.P(
                                    "responsible for clearing and settling transactions between different parties involved in the transaction process, such as card issuers, acquirers, and processors. It involves a set of sub-functionalities to aggregate, reconcile, and process payments and settlements.")
                            ]),
                        ]),
                        Mermaid(chart=mc.container_acquirer),
                        Mermaid(chart=mc.container_gateway),
                        html.Ul(
                            children=[
                                html.Li(
                                    children=[
                                        html.B("Web Server"),
                                        html.P(
                                            "This container hosts the payment gateway's web application, which allows merchants to initiate and manage transactions.")
                                    ]
                                ),
                                html.Li(children=[
                                    html.B("Load Balancer"),
                                    html.P(
                                        "This container balances incoming requests across multiple instances of the web server container, to ensure that the system can handle high levels of traffic.")
                                ]),
                                html.Li(children=[
                                    html.B("API Gateway"),
                                    html.P(
                                        "This container acts as a single entry point for all incoming requests and forwards them to the appropriate container for processing.")
                                ]),
                                html.Li(children=[
                                    html.B("Transaction Management"),
                                    html.P(
                                        "This container manages the transaction lifecycle, from processing incoming requests to transmitting responses to the acquirer and card network.")
                                ]),
                                html.Li(children=[
                                    html.B("Security"),
                                    html.P(
                                        "This container manages all aspects of security for the payment gateway, including user authentication and authorization, data encryption, and vulnerability scanning.")
                                ]),
                                html.Li(children=[
                                    html.B("Reporting"),
                                    html.P(
                                        "This container generates reports and analytics for the payment gateway, providing insights into transaction volumes, success rates, and other metrics.")
                                ]),
                                html.Li(children=[
                                    html.B("Acquirer"),
                                    html.P(
                                        "The acquirer is responsible for forwarding transactions to the appropriate processor and ultimately the issuer for approval or denial.")
                                ]),
                                html.Li(children=[
                                    html.B("Card Network"),
                                    html.P(
                                        "The card network routes transactions between the payment gateway and the issuer, ensuring that each transaction is processed correctly and securely.")
                                ]),
                            ]
                        ),
                        Mermaid(chart=mc.container_merchant),
                        Mermaid(chart=mc.container_cardholder),
                        Mermaid(chart=mc.container_cardholder_digital),
                        Mermaid(chart=mc.container_credit_scoring),
                        Mermaid(chart=mc.components_credit_scoring),
                        Mermaid(chart=mc.container_credit_scoring_data_sources),
                        Mermaid(chart=mc.container_credit_scoring_decision_engine),
                    ],
                    id="mermaind-credit-card-context"),
            ]
            )
        elif url in ['/r&d/mortgages']:
            return html.Div("Under Construction")
        elif url in ['/clocks']:
            return generate_clock_row_col()
        else:
            # lotourl = 'https://assets5.lottiefiles.com/packages/lf20_6nmazhqu.json'
            # return html.Div(de.Lottie(options=options, url=lotourl, width="50%", height="50%"))
            return html.Div(
                children=[
                    html.H1("404"),
                    html.P("Page Under Construction")
                ], className="error"
            )


if __name__ == '__main__':
    for i in pytz.all_timezones:
        print(i)
    print(pytz.timezone('UTC-5'))
