<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Version Page Design Demo</title>
    <link rel="stylesheet" href="assets/css/modern_version.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@iconify/iconify@3.1.1/dist/iconify.min.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        /* Demo controls */
        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            z-index: 1000;
            max-width: 200px;
        }
        
        .demo-controls h3 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #262626;
        }
        
        .demo-controls button {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .demo-controls button:hover {
            background: #f5f5f5;
            border-color: #1890ff;
        }
        
        /* Mock data styles */
        .mock-progress {
            display: inline-block;
            background: #f0f0f0;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 12px;
            color: #666;
        }
        
        .mock-link {
            color: #1890ff;
            text-decoration: none;
        }
        
        .mock-link:hover {
            text-decoration: underline;
        }
        
        .priority-high {
            color: #ff4d4f;
            font-weight: bold;
        }
        
        .priority-medium {
            color: #faad14;
            font-weight: bold;
        }
        
        .priority-low {
            color: #52c41a;
            font-weight: bold;
        }
        
        .status-open {
            background: #fff2e8;
            color: #fa8c16;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-progress {
            background: #e6f7ff;
            color: #1890ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-done {
            background: #f6ffed;
            color: #52c41a;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <h3>Demo Controls</h3>
        <button onclick="switchLayer('three')">Release Status</button>
        <button onclick="switchLayer('eight')">Defect Details</button>
        <button onclick="switchLayer('five')">Charts</button>
        <button onclick="switchLayer('seven')">Roadmap</button>
        <button onclick="toggleSidebar()">Toggle Sidebar</button>
        <button onclick="simulateMobile()">Mobile View</button>
        <button onclick="addMockData()">Add Mock Data</button>
    </div>

    <div class="modern-version-layout">
        <!-- Header Section -->
        <div class="version-header">
            <div class="header-content">
                <div class="title-section">
                    <iconify-icon icon="mdi:source-branch" class="header-icon"></iconify-icon>
                    <div>
                        <h1 class="header-title">Release Roadmap</h1>
                        <p class="header-subtitle">Manage project versions, releases, and defect tracking</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="modern-button secondary" id="id-copy-epic-btn">
                        <iconify-icon icon="mdi:content-copy" class="button-icon"></iconify-icon>
                        Copy Epic List
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="version-content">
            <!-- Modern Sidebar -->
            <div class="modern-sidebar">
                <div class="sidebar-header">
                    <button class="sidebar-toggle">
                        <iconify-icon icon="mdi:menu" class="toggle-icon"></iconify-icon>
                    </button>
                    <h3 class="sidebar-title">Filters & Configuration</h3>
                </div>
                
                <!-- Version Selection Section -->
                <div class="filter-section">
                    <div class="section-header">
                        <iconify-icon icon="mdi:filter-variant" class="section-icon"></iconify-icon>
                        <h4 class="section-title">Version Selection</h4>
                    </div>
                    
                    <!-- Version State Controls -->
                    <details class="version-state-details">
                        <summary class="modern-summary">
                            <iconify-icon icon="mdi:cog" class="summary-icon"></iconify-icon>
                            Advanced Version State
                        </summary>
                        <div class="state-controls">
                            <div class="control-group">
                                <label class="control-label">Released Status</label>
                                <select class="modern-dropdown">
                                    <option>No (Active)</option>
                                    <option>Yes (Released)</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label class="control-label">Archive Status</label>
                                <select class="modern-dropdown">
                                    <option>No (Active)</option>
                                    <option>Yes (Archived)</option>
                                </select>
                            </div>
                        </div>
                    </details>
                    
                    <!-- Version Selection -->
                    <div class="control-group">
                        <label class="control-label">Select Releases</label>
                        <select class="modern-dropdown" multiple>
                            <option>v2.1.0</option>
                            <option>v2.1.1</option>
                            <option>v2.2.0</option>
                            <option>v3.0.0-beta</option>
                        </select>
                    </div>
                    
                    <!-- Pattern Search -->
                    <div class="control-group">
                        <label class="control-label">Pattern Search</label>
                        <p class="help-text">Use comma-separated patterns. Prefix with ~ to exclude.</p>
                        <div class="search-input-group">
                            <input type="text" class="modern-input" placeholder="e.g., v2.1.*, ~v2.1.0" value="v2.1.*">
                            <button class="modern-button primary small">
                                <iconify-icon icon="mdi:magnify" class="button-icon"></iconify-icon>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Defect Analysis Section -->
                <div class="filter-section">
                    <div class="section-header">
                        <iconify-icon icon="mdi:bug" class="section-icon"></iconify-icon>
                        <h4 class="section-title">Defect Analysis</h4>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Version Type</label>
                        <select class="modern-dropdown">
                            <option>Affects Version (Issues Found)</option>
                            <option>Fix Version (Issues Fixed)</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Filter by Status</label>
                        <select class="modern-dropdown">
                            <option>All Statuses</option>
                            <option>Open</option>
                            <option>In Progress</option>
                            <option>Fixed</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Filter by Priority Group</label>
                        <select class="modern-dropdown">
                            <option>All Priorities</option>
                            <option>High Priority (>=HIGH)</option>
                            <option>Low Priority (<HIGH)</option>
                        </select>
                    </div>
                </div>
                
                <!-- Statistics Section -->
                <div class="filter-section">
                    <div class="section-header">
                        <iconify-icon icon="mdi:chart-bar" class="section-icon"></iconify-icon>
                        <h4 class="section-title">Statistics</h4>
                    </div>
                    
                    <div class="stats-container">
                        <label class="control-label">Priority Distribution</label>
                        <div class="stats-alert">
                            High: 5, Medium: 12, Low: 8
                        </div>
                    </div>
                    
                    <div class="stats-container">
                        <label class="control-label">Severity Distribution</label>
                        <div class="stats-alert">
                            Critical: 2, High: 8, Medium: 15
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Display Area -->
            <div class="main-display-area">
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <div class="tab-header">
                        <button class="tab-button active" data-layer="three">
                            <iconify-icon icon="mdi:view-dashboard" class="tab-icon"></iconify-icon>
                            Release Status
                        </button>
                        <button class="tab-button" data-layer="eight">
                            <iconify-icon icon="mdi:table" class="tab-icon"></iconify-icon>
                            Defect Details
                        </button>
                        <button class="tab-button" data-layer="seven">
                            <iconify-icon icon="mdi:chart-timeline-variant" class="tab-icon"></iconify-icon>
                            Roadmap
                        </button>
                        <button class="tab-button" data-layer="five">
                            <iconify-icon icon="mdi:chart-bar" class="tab-icon"></iconify-icon>
                            Charts
                        </button>
                        <button class="tab-button" data-layer="six">
                            <iconify-icon icon="mdi:chart-bubble" class="tab-icon"></iconify-icon>
                            Bubble Chart
                        </button>
                        <button class="tab-button" data-layer="two">
                            <iconify-icon icon="mdi:chart-line" class="tab-icon"></iconify-icon>
                            Trends
                        </button>
                        <button class="tab-button" data-layer="one">
                            <iconify-icon icon="mdi:counter" class="tab-icon"></iconify-icon>
                            Counts
                        </button>
                    </div>
                    
                    <div class="export-section">
                        <button class="modern-button success">
                            <iconify-icon icon="mdi:file-excel" class="button-icon"></iconify-icon>
                            Export
                        </button>
                    </div>
                </div>
                
                <!-- Content Layers -->
                <div class="content-layers">
                    <!-- Layer 3 - Release Status (Default Active) -->
                    <div class="content-layer layer-three active" id="layer-three">
                        <div class="layer-header">
                            <iconify-icon icon="mdi:view-dashboard" class="layer-icon"></iconify-icon>
                            <h3 class="layer-title">Release Status</h3>
                        </div>
                        <div class="layer-content">
                            <div class="modern-release-table-container">
                                <table class="modern-release-table">
                                    <thead>
                                        <tr class="table-header-row">
                                            <th class="sortable">Project</th>
                                            <th class="sortable">Epic</th>
                                            <th class="sortable">Status</th>
                                            <th class="sortable">Stories & Tasks</th>
                                            <th class="sortable">Defects</th>
                                            <th class="sortable">Progress</th>
                                        </tr>
                                    </thead>
                                    <tbody id="release-table-body">
                                        <tr class="table-data-row">
                                            <td>Cookie Platform</td>
                                            <td><a href="#" class="mock-link">COOK-123</a></td>
                                            <td><span class="status-progress">In Progress</span></td>
                                            <td><span class="mock-progress">8/12 Complete</span></td>
                                            <td><span class="mock-progress">2/5 Fixed</span></td>
                                            <td><span class="mock-progress">75%</span></td>
                                        </tr>
                                        <tr class="table-data-row">
                                            <td>Jazz Framework</td>
                                            <td><a href="#" class="mock-link">JAZZ-456</a></td>
                                            <td><span class="status-done">Done</span></td>
                                            <td><span class="mock-progress">15/15 Complete</span></td>
                                            <td><span class="mock-progress">3/3 Fixed</span></td>
                                            <td><span class="mock-progress">100%</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Layer 8 - Defect Details -->
                    <div class="content-layer layer-eight" id="layer-eight">
                        <div class="layer-header">
                            <iconify-icon icon="mdi:table" class="layer-icon"></iconify-icon>
                            <h3 class="layer-title">Defects Dashboard</h3>
                        </div>
                        <div class="layer-content table-container">
                            <div class="table-wrapper">
                                <table class="modern-table">
                                    <thead>
                                        <tr class="table-header-row">
                                            <th class="sortable">Key</th>
                                            <th class="sortable">Summary</th>
                                            <th class="sortable">Status</th>
                                            <th class="sortable">Priority</th>
                                            <th class="sortable">Severity</th>
                                            <th class="sortable">Assigned To</th>
                                            <th class="sortable">Aging (Days)</th>
                                        </tr>
                                    </thead>
                                    <tbody id="defect-table-body">
                                        <tr class="defect-row">
                                            <td><a href="#" class="mock-link">BUG-001</a></td>
                                            <td>Login page not responsive on mobile</td>
                                            <td><span class="status-open">Open</span></td>
                                            <td><span class="priority-high">High</span></td>
                                            <td><span class="priority-medium">Medium</span></td>
                                            <td>John Doe</td>
                                            <td>5</td>
                                        </tr>
                                        <tr class="defect-row">
                                            <td><a href="#" class="mock-link">BUG-002</a></td>
                                            <td>Database connection timeout</td>
                                            <td><span class="status-progress">In Progress</span></td>
                                            <td><span class="priority-high">Critical</span></td>
                                            <td><span class="priority-high">High</span></td>
                                            <td>Jane Smith</td>
                                            <td>2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other Layers -->
                    <div class="content-layer layer-five" id="layer-five">
                        <div class="layer-header">
                            <iconify-icon icon="mdi:chart-bar" class="layer-icon"></iconify-icon>
                            <h3 class="layer-title">Charts</h3>
                        </div>
                        <div class="layer-content">
                            📊 Charts would be displayed here
                        </div>
                    </div>
                    
                    <div class="content-layer layer-seven" id="layer-seven">
                        <div class="layer-header">
                            <iconify-icon icon="mdi:chart-timeline-variant" class="layer-icon"></iconify-icon>
                            <h3 class="layer-title">Roadmap</h3>
                        </div>
                        <div class="layer-content">
                            🗺️ Roadmap timeline would be displayed here
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
    <script src="assets/js/modern_version.js"></script>
    <script>
        function switchLayer(layer) {
            if (window.modernVersion) {
                window.modernVersion.switchLayer(layer);
            }
        }

        function toggleSidebar() {
            if (window.modernVersion) {
                window.modernVersion.toggleSidebar();
            }
        }

        function simulateMobile() {
            const isMobile = document.body.style.maxWidth === '1200px';
            document.body.style.maxWidth = isMobile ? 'none' : '1200px';
            if (window.modernVersion) {
                window.modernVersion.handleResize();
            }
        }

        function addMockData() {
            const releaseBody = document.getElementById('release-table-body');
            const defectBody = document.getElementById('defect-table-body');
            
            if (releaseBody) {
                const newRow = document.createElement('tr');
                newRow.className = 'table-data-row';
                newRow.innerHTML = `
                    <td>New Project</td>
                    <td><a href="#" class="mock-link">NEW-789</a></td>
                    <td><span class="status-open">Open</span></td>
                    <td><span class="mock-progress">3/10 Complete</span></td>
                    <td><span class="mock-progress">1/4 Fixed</span></td>
                    <td><span class="mock-progress">30%</span></td>
                `;
                releaseBody.appendChild(newRow);
            }
            
            if (defectBody) {
                const newRow = document.createElement('tr');
                newRow.className = 'defect-row';
                newRow.innerHTML = `
                    <td><a href="#" class="mock-link">BUG-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}</a></td>
                    <td>New defect added dynamically</td>
                    <td><span class="status-open">Open</span></td>
                    <td><span class="priority-medium">Medium</span></td>
                    <td><span class="priority-low">Low</span></td>
                    <td>Demo User</td>
                    <td>1</td>
                `;
                defectBody.appendChild(newRow);
            }
        }
    </script>
</body>
</html>
