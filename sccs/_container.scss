// scss/_container.scss

.container-login-button {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 20px;
}

.container_custom {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--background-content);
    width: 50%;
    text-align: center;
    z-index: inherit;
    padding: 10px;
}



// Responsive Containers
.container1x4 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--background-content);
    width: 25%;
    height: 25%;
    text-align: center;
    z-index: inherit;
    padding: 10px;
}

.container1x2 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--background-content);
    width: 80%;
    height: auto;
    text-align: center;
    z-index: inherit;
    padding: 10px;

    display: flex;
    /* Use flexbox */
    flex-direction: column;
    /* Arrange children vertically */
    align-items: center;
    /* Center items horizontally */
}

/* Extra small devices (phones, 576px and down) */
@media (max-width: 576px) {
    .container1x2 {
        width: 90%;
        padding: 5px;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
    .container1x2 {
        width: 80%;
        padding: 10px;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .container1x2 {
        width: 70%;
        padding: 15px;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .container1x2 {
        width: 60%;
        padding: 20px;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container1x2 {
        width: 50%;
        padding: 20px;
    }
}

.container3x4 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--background-content);
    width: 75%;
    height: 75%;
    text-align: center;
    z-index: inherit;
    padding: 10px;
}

@media (max-width: $breakpoint-small) {
    .container1x4,
    .container1x2,
    .container3x4 {
        width: 100%;
    }
}
