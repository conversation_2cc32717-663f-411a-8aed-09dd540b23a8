// scss/_layout.scss
.page-style {    
    display: grid;
    grid-gap: 1px;
    grid-template-columns: auto;
    grid-template-areas:
        "header-area"
        "content-area";
    grid-template-rows: auto 1fr;
    grid-auto-columns: min-content;

    // @media (min-width: $breakpoint-medium) {
    //     grid-template-columns: 1fr 3fr;
    //     grid-template-areas:
    //         "header-area header-area"
    //         "sidebar-area content-area";
    // }
}

.page-style div:first-child:not(:empty) {
    max-width: auto;
}

header {
    grid-area: header-area;    
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10; 
    height: $header-height;

    // @media (min-width: $breakpoint-medium) {
    //     position: static;
    //     height: auto;
    // }
}

main {
    grid-area: content-area;    
    padding-top: $header-height;
    padding-bottom: $content-padding-bottom;
    position: relative;      
    height: calc(100vh - ($header-height + $content-padding-bottom));
    vertical-align: middle;

    // @media (min-width: $breakpoint-medium) {
    //     padding-top: 0;
    //     height: auto;
    // }
}

// Add a sidebar for larger screens if needed
.sidebar {
    grid-area: sidebar-area;
    display: none;

    @media (min-width: $breakpoint-medium) {
        display: block;
    }
}
