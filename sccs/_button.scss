.format_button {
    font-family: $font-family-r<PERSON><PERSON><PERSON>;
    font-size: small;
    font-weight: 700;
    line-height: 1.5;
    color: #fff;
    text-transform: uppercase;
    width: 225px;
    height: 50px;
    border-radius: 25px;
    background: $button_color_green;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 25px;
    transition: all 0.4s;
}

.format_button:hover {
    cursor: pointer;
}

.format_button:disabled,
.format_button[disabled=disabled] {
    background-color: $button_color_disabled;
    text-decoration: line-through;
}
