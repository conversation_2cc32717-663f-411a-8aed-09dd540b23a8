# this is a new registration
                        # Validate against O365 creds and then send email with registration details
                        # schema = f"{schema_name},public"
                        my_logger.debug("Inside new OTP reegistraion flow")
                        session['new_registration'] = 'new'
                        home_path = os.getenv('HOME', 'c:/vishal/KeyPass')

                        ref = pkp(
                            filename=os.path.join(home_path, 'Database.kdbx'),
                            keyfile=os.path.join(home_path, 'Database.key')
                        )
                        msal_map = {}
                        for title in ['CLIENT_ID', 'TENANT_ID', 'CLIENT_SECRET']:
                            entry = ref.find_entries(title=title, first=True)
                            # exec(f'{title} = {entry.password}')
                            msal_map[title] = entry.password
                        AUTHORITY = f"https://login.microsoftonline.com/{msal_map['TENANT_ID']}"
                        scopes = ['Mail.Send']

                        my_logger.info("Initiatiating client calls")

                        msal_app = msal.ConfidentialClientApplication(
                            client_id=msal_map['CLIENT_ID'],
                            client_credential=msal_map['CLIENT_SECRET'],
                            authority=AUTHORITY)
                        my_logger.info("After client call initiatied")
                        auth_code_flow_dict = msal_app.initiate_auth_code_flow(
                            scopes=scopes,
                            redirect_uri=f"{flask.request.root_url}getAToken"
                        )
                        my_logger.debug(f"auth code = {auth_code_flow_dict}")

                        auth_response = open_in_new_window(auth_code_flow_dict["auth_uri"])
                        state = auth_code_flow_dict["state"]
                        auth_code_flow_dict["state"] = [state]
                        my_logger.debug(f'auth response = {request.args.to_dict()}')

                        access_token = msal_app.acquire_token_by_auth_code_flow(
                            auth_code_flow=auth_code_flow_dict,
                            auth_response=auth_response
                        )

                        my_logger.debug(f'access toke = {access_token}')

                        # Now we have authentication token
                        # initiate process to send the token
                        token_url = f"https://login.microsoftonline.com/{msal_map['TENANT_ID']}/oauth2/v2.0/token"
                        resource = 'https://graph.microsoft.com'

                        response = requests.post(
                            token_url,
                            data={
                                "client_id": msal_map['CLIENT_ID'],
                                "client_secret": msal_map['CLIENT_SECRET'],
                                "grant_type": "client_credentials",
                                "scope": resource + "/.default"
                            }
                        )
                        my_logger.info(f'response of post request: {response.status_code}')

                        token_string, qr_image_file = helper.token_2fa(username)

                        headers = {
                            "Authorization": "Bearer " + access_token['access_token'],
                            "Content-Type": "application/json"
                        }

                        message = {
                            "message": {
                                "subject": "New Registration Code",
                                "body": {
                                    "contentType": "HTML",
                                    "content": "Use the registration code " + token_string + " or scan the attached QR code in Google Authenticator App"
                                },
                                "toRecipients": [
                                    {
                                        "emailAddress": {
                                            "address": f"{username}"
                                        }
                                    }
                                ],
                                "ccRecipients": [
                                    {
                                        "emailAddress": {
                                            "address": f"{username}"
                                        }
                                    }
                                ],
                                # include attachments
                                'attachments': [
                                    qr_image_file
                                ]
                            },
                            "saveToSentItems": "false"
                        }

                        # Send the email

                        response = requests.post(
                            f"https://graph.microsoft.com/v1.0/me/sendMail",
                            headers=headers,
                            data=json.dumps(message)
                        )
                        my_logger.debug(f"respnonse of post request: {response.status_code}")

                        # Check the response status code
                        if response.status_code == 202:
                            my_logger.debug("Email sent successfully.")
                            # update user row with generated token
                            db.update_user_otp_token(username, token_string)
                            session['2fa_enabled'] = True
                        else:
                            my_logger.debug("Failed to send email. Response status code: " + str(response.status_code))
                            my_logger.debug(response.json())

                        return f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa"