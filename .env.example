# JIRA Dashboard Environment Configuration Example
# Copy this file to .env and update the values according to your environment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_PATH=c:/path/to/your/Database.kdbx
MASTER_KEYFILE=c:/path/to/your/Database.key

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_TOKEN=generate-a-secure-random-token-here

# =============================================================================
# DASH APPLICATION CONFIGURATION
# =============================================================================
DASH_BASE_PATH=/

# =============================================================================
# MICROSOFT AZURE AD / MSAL CONFIGURATION
# =============================================================================
CLIENT_ID=your-azure-client-id
CLIENT_SECRET=your-azure-client-secret
TENANT_ID=your-azure-tenant-id

# =============================================================================
# TWO-FACTOR AUTHENTICATION
# =============================================================================
TWO_FA_ENABLED=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_DIR=${TEMP}

# =============================================================================
# DEVELOPMENT/TESTING CONFIGURATION
# =============================================================================
FLASK_ENV=development
FLASK_DEBUG=true

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_TYPE=RedisCache
CACHE_DEFAULT_TIMEOUT=600
CACHE_KEY_PREFIX=jira_dashboard:

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
ALLOWED_DOMAINS=graph.microsoft.com,corecard.atlassian.net
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
REDIS_MAX_CONNECTIONS=20
REDIS_RETRY_ON_TIMEOUT=true
SHORT_TIME_OUT=600
TIME_OUT=3600
LONG_TIME_OUT=14400

# =============================================================================
# MONITORING AND DEBUGGING
# =============================================================================
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_REQUEST_TIMING=false
