# test_totp_verification.py
import pytest
import allure
import pyotp
import time
import ntplib

from data.helper import verify_token

@allure.feature("2FA TOTP")
@allure.story("Verify with system time")
def test_verify_with_system_time():
    secret = pyotp.random_base32()
    totp = pyotp.TOTP(secret)

    # Generate current valid code
    code = totp.now()

    with allure.step("Verify token with system time"):
        result = verify_token(secret, code)
        assert result is True


@allure.feature("2FA TOTP")
@allure.story("Verify fallback with NTP")
def test_verify_with_ntp(monkeypatch):
    secret = pyotp.random_base32()
    totp = pyotp.TOTP(secret)

    # Use integer timestamp for typing compatibility with totp.at()
    real_time = int(time.time())
    code = totp.at(real_time)  # <-- pass int, not float

    # --- Simulate clock skew: system clock is off by +5 minutes ---
    skewed_time = real_time + 300  # seconds

    # Patch time.time() to return the skewed (bad) system time
    monkeypatch.setattr(time, "time", lambda: float(skewed_time))

    # Fake NTP response returning the real (correct) time as an int
    class FakeResponse:
        def __init__(self, tx_time: int):
            self.tx_time = tx_time

    class FakeNTPClient:
        def request(self, host, version=3):
            return FakeResponse(tx_time=real_time)  # int

    # Patch ntplib.NTPClient to our fake implementation
    monkeypatch.setattr(ntplib, "NTPClient", FakeNTPClient)

    with allure.step("Verify should fail on system time but succeed with NTP"):
        result = verify_token(secret, code, valid_window=1, ntp_server="time.google.com")
        assert result is True
