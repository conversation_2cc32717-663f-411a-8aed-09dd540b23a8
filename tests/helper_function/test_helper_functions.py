import custom_container
from data import helper
import pytest


@pytest.mark.parametrize(
    "entry_name, custom_string_name, expected",
    [
        ("PG_DB", "DB_SERVER_RO_PORT", '5432'),
        ("PG_DB", "DB_SERVER_RW_PORT", '5432'),
        ("PG_DB", "DB_DRIVER", 'postgresql+psycopg2'),
        ("PG_DB", "DB_SERVER_NAME", "localhost")
    ]
)
def test_get_custom_string(entry_name, custom_string_name, expected):
    container = custom_container.MyKeePassContainer()
    manager = container.password_manager()
    custom_string = manager.get_custom_string(entry_name, custom_string_name)
    assert custom_string == expected


