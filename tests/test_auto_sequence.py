import sqlite3
import threading

from data.auto_sequence_generator import (
    AutoSe<PERSON>GeneratorThreaded, AutoSequenceGenerator,
    AutoSequenceGeneratorSingleton, AutoSequenceGeneratorThreadedDb
)


def test_auto_sequence_generator_threaded():
    # Create a generator instance
    generator = AutoSequenceGeneratorThreaded()

    # Test instance counter
    assert generator.nextval() == 2
    assert generator.nextval() == 3

    # Create another generator instance
    generator2 = AutoSequenceGeneratorThreaded()

    # Test class counter and instance counter of new instance
    assert generator2.nextval() == 2
    assert generator2.nextval() == 3

    # Test thread safety
    def increment(instance):
        for counter in range(1000):
            instance.get_next_instance_number()

    threads = []
    for i in range(10):
        t = threading.Thread(target=increment, args=(generator,))
        t.start()
        threads.append(t)

    for t in threads:
        t.join()

    assert generator.nextval() == 4


def test_auto_sequence_generator_threaded_db():
    # Set up the database connection and generator
    conn = sqlite3.connect(":memory:")
    gen = AutoSequenceGeneratorThreadedDb(conn)

    # Test that sequence numbers are unique across multiple threads
    def test_thread(results, index):
        for i in range(10):
            results[index].append(gen.get_next_sequence_number())
            # assert gen.get_next_sequence_number() == (threading.get_ident() - 1) * 10 + i + 1

    results = [[] for _ in range(10)]
    threads = [threading.Thread(target=test_thread, args=(results, i)) for i in range(10)]
    for thread in threads:
        thread.start()
    for thread in threads:
        thread.join()
    # Flatten the results and check for uniqueness
    all_numbers = [num for sublist in results for num in sublist]
    assert len(all_numbers) == len(set(all_numbers)), "Sequence numbers are not unique across threads"

    # Test that sequence numbers are unique at the instance level
    gen1 = AutoSequenceGeneratorThreadedDb(conn)
    gen2 = AutoSequenceGeneratorThreadedDb(conn)
    assert gen1.get_next_sequence_number() == 11, "Sequence number is not 11"
    assert gen2.get_next_sequence_number() == 12, "Sequence number is not 12"
    assert gen1.get_next_sequence_number() == 13, "Sequence number is not 13"
    assert gen2.get_next_sequence_number() == 14, "Sequence number is not 14"

    # Clean up
    conn.close()


def test_auto_sequence_generator():
    # Test that sequence numbers are unique across multiple threads
    def test_thread():
        for i in range(10):
            assert AutoSequenceGenerator.get_next_sequence_number() == (threading.get_ident() - 1) * 10 + i + 1

    threads = [threading.Thread(target=test_thread) for _ in range(10)]
    for thread in threads:
        thread.start()
    for thread in threads:
        thread.join()

    # Test that sequence numbers are unique at the class level
    assert AutoSequenceGenerator.get_next_sequence_number() == 11
    assert AutoSequenceGenerator.get_next_sequence_number() == 12


def test_auto_sequence_generator_singleton():
    # Test that sequence numbers are unique at the instance level
    gen1 = AutoSequenceGeneratorSingleton.get_instance()
    gen2 = AutoSequenceGeneratorSingleton.get_instance()
    assert gen1.nextval() == 1
    assert gen2.nextval() == 2
    assert gen1.nextval() == 3
    assert gen2.nextval() == 4

    # Test that sequence numbers are unique at the class level
    assert AutoSequenceGeneratorSingleton.get_next_class_number() == 1
    assert AutoSequenceGeneratorSingleton.get_next_class_number() == 2

    # Test that singleton instance is created only once
    gen3 = AutoSequenceGeneratorSingleton()
    assert gen1 is gen2 is gen3
