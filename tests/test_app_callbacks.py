import pytest
from contextvars import copy_context
from dash._callback_context import context_value
from dash._utils import AttributeDict

@pytest.mark.parametrize(
    "n_clicks, prop_id, from_version, version_list, rn_project, pf_version, kms_version, "
    "cookie_report_version, cookie_karfa_verion, cookie_report_deliver_version, "
    "jazz_report_version, jazz_karfa_verion, jazz_report_deliver_version, pf_rn_flag, kms_rn_flag, branch_name, "
    "package_name",
    [
        (
                1, "id-download-rn.n_clicks", "23.2.4.5", "23.2.4.6, 23.8, 23.8.1", ['<PERSON><PERSON>', '<PERSON>'], ['4.3.2.11'],
                '3.7', '16.00.05 - 22.7', '22.10.2', '16.00.05', '22.8', '22.10.1 - 22.10.2', '22.10.3 - 22.10.5',
                [], [], ['Plat_23.1'], ['Plat_23.1.2 - Package']
        ),
        # Only <PERSON><PERSON>
        (
                2, "id-download-rn.n_clicks", "23.2.4.5", "23.2.4.6", ['<PERSON><PERSON>'], ['4.3.2.11'], '3.7',
                '16.00.05 - 22.7', '22.10.2', '16.00.05', '22.8', '22.10.1 - 22.10.2', '22.10.3 - 22.10.5',
                [], [], ['Plat_23.1'], ['Plat_23.1.2 - Package']
        ),
        # Only <PERSON>
(
                3, "id-download-rn.n_clicks", "23.2.4.5", "23.2.4.6, 23.8, 23.8.1", ['Cookie', 'Jazz'], ['4.3.2.11'],
                '3.7', '16.00.05 - 22.7', '22.10.2', '16.00.05', '22.8', '22.10.1 - 22.10.2', '22.10.3 - 22.10.5',
                [], [], ['Plat_23.1'], ['Plat_23.1.2 - Package']
        ),
    ]
)
def test_generate_rn_callback(
        n_clicks, prop_id, from_version, version_list, rn_project, pf_version, kms_version,
        cookie_report_version, cookie_karfa_verion, cookie_report_deliver_version,
        jazz_report_version, jazz_karfa_verion, jazz_report_deliver_version, pf_rn_flag,
        kms_rn_flag, branch_name, package_name
):
    @pytest.mark.skip(reason="Skipping this test function")
    def run_callback():
        context_value.set(
            AttributeDict(
                **{
                    "triggered_inputs": [
                        {"prop_id": prop_id, 'value': n_clicks},
                    ]
                }
            ))
        # return generate_rn(
        #     n_clicks, '/plat/releasenotes', from_version, version_list, rn_project,
        #     pf_version, kms_version, cookie_report_version,
        #     cookie_karfa_verion, cookie_report_deliver_version,
        #     jazz_report_version, jazz_karfa_verion, jazz_report_deliver_version,
        #     pf_rn_flag, kms_rn_flag, branch_name, package_name
        # )

    ctx = copy_context()
    output = ctx.run(run_callback)

