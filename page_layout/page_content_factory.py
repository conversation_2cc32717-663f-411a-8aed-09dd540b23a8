import os

from dash import (dcc, html)

from urllib.parse import quote

from page_layout.page_contents.page_content import NotFoundPageContent, PageContent
from page_layout.page_contents.source_control import SourceControlPageContent
from page_layout.page_contents.project_version import VersionPageContent
from page_layout.page_contents import OverviewPageContent
from page_layout.page_contents.home_page_content import HomePageContent
from page_layout.page_contents.compliance_description import ComplianceDescription
from page_layout.page_contents.issue_aging import IssueAging
from page_layout.page_contents.project_compliance import ProjectCompliance
from page_layout.page_contents.sql_code_review import SQLCodeReview
from .page_contents.credit_card_context import CreditCardContext
from .page_contents.issue_transition import IssueTransition
from .page_contents.openai_chatgpt import ChatGPTContext
from .page_contents.release_notes import ReleaseNotes
from .page_contents.isc_timesheet import WDTimeSheet
from .page_contents.weekly_report import WeeklyReport
from .page_contents.service_availability import ServiceAvailability
import dash_bootstrap_components as dbc
import dash_ag_grid as dag

from page_layout.page_contents.time_log import TimeLog


class BulkCreateTeams(PageContent):
    def render(self):
        return html.Div(
            children=[
                dcc.Upload(
                    [
                        'Drag and Drop or ',
                        html.A('Select a File'),
                        html.P("Only File with extension .xlsx allowed")
                    ],
                    multiple=False,
                    disabled=False,
                    style={
                        'width': '80%',
                        'height': '120px',
                        'lineHeight': '60px',
                        'borderWidth': '1px',
                        'borderStyle': 'dashed',
                        'borderRadius': '5px',
                        'textAlign': 'center',
                        'border-style': 'dashed',
                        'background-color': 'gray',
                        'margin-bottom': '10px',
                    },
                    id="id-file-upload-plat-teams",
                ),
                html.Div(children=[
                    dcc.Loading(html.Button(
                        id="id-run-plat-team", children="Upload!", className="format_button",
                        disabled=False, n_clicks=0
                    )),
                ], className="container-login-button"),
                html.Hr(className='white'),
                dbc.Alert(id="id-plat-teams-msg", style={'width': '80%'}),
            ], className="container_custom"
        )


class UploadRequestTracker(PageContent):
    def render(self):
        return html.Div(
            children=[
                dcc.Loading(
                    dcc.Upload(
                        [
                            'Drag and Drop or ',
                            html.A('Select a File'),
                            html.P("Only File with extension .xls allowed")
                        ],
                        multiple=False,
                        disabled=False,
                        style={
                            'width': '80%',
                            'height': '120px',
                            'lineHeight': '60px',
                            'borderWidth': '1px',
                            'borderStyle': 'dashed',
                            'borderRadius': '5px',
                            'textAlign': 'center',
                            'border-style': 'dashed',
                            'background-color': 'gray',
                            'margin-bottom': '10px',
                        },
                        id="id-file-upload-request-tracker",
                    )
                ),
                html.Div(children=[
                    dcc.Loading(html.Button(
                        id="id-run-request-tracker", children="Upload!", className="format_button",
                        disabled=False, n_clicks=0
                    )),
                ], className="container-login-button"),
                html.Hr(className='white'),
                dbc.Alert(id="id-request-tracker", style={'width': '80%'}),

                html.Div(
                    children=[
                        dcc.Textarea(id="id-text-pattern-rt", persistence="true"),
                        dcc.Input(
                            type="number", id="id-percent-rt", debounce=True, className="mt-2",
                            persistence="true"
                        ),
                    ],
                    className="row vstack w-75"
                ),

                dcc.Download(id="id-download-file-rt"),
                html.Div(children=[
                    html.Button(
                        id="id-execute-pattern-rt", children="Download File!", className="format_button",
                        disabled=False, n_clicks=0
                    ),
                ], className="container-login-button"),

            ], className="container_custom"
        )


class ShowRequestTracker(PageContent):
    def render(self):
        columnDefs = [
            {'headerName': 'RT ID', 'field': 'id', 'filter': False, },
            {'headerName': 'Description', 'field': 'desc', 'filter': False},
            {'headerName': 'category', 'field': 'category', 'filter': False},
            {'headerName': 'label', 'field': 'label', 'filter': True},
            {
                'headerName': 'confidence', 'field': 'confidence_percent', 'filter': True,
                'valueFormatter': 'data.number.toFixed(2)'
            },
            ]
        defaultColDef = {
            'flex': 1,
            "filter": "agNumberColumnFilter",
            "resizable": True,
            "sortable": True,
            "editable": False,
            "floatingFilter": True,
            'autoHeight': True,
        }

        return html.Div(
            children=[
                html.Div(children=[
                    dcc.Loading(html.Button(
                        id="id-get-rt-data", children="Get RT Data", className="format_button",
                        disabled=False, n_clicks=0
                    )),
                ], className="container-login-button"),
                dag.AgGrid(
                    id="id-request-tracker-data",
                    className="ag-theme-alpine-dark",
                    style={"height": "80vh", "width": "70%"},
                    columnDefs=columnDefs,
                    columnSize="sizeToFit",
                    defaultColDef=defaultColDef,
                    dashGridOptions={
                        "undoRedoCellEditing": True, "rowSelection": "single",
                        "loadingOverlayComponent": "CustomLoadingOverlay",
                        "loadingOverlayComponentParams": {
                            "loadingMessage": "One moment please...",
                            "color": "white",
                        },
                        "pagination": False,
                        "paginationAutoPageSize": False
                    },
                    csvExportParams={
                        "fileName": "ag_grid_test.csv",
                    },
                )
            ]
        )


class PageContentFactory:
    @staticmethod
    def create(url: str, project: str, session_factory):
        url_path_comp = os.getenv('DASH_BASE_PATH', '/')

        if url == url_path_comp:
            return HomePageContent(project, session_factory)
        elif url in [f'{url_path_comp}{project}/overview']:
            return OverviewPageContent(project, session_factory)
        elif url in [f'{url_path_comp}{project}/version']:
            return VersionPageContent(project, session_factory)
        elif url in [f'{url_path_comp}{project}/svn']:
            return SourceControlPageContent(project, session_factory)
        elif url in [f'{url_path_comp}{project}/description']:
            return ComplianceDescription(project, session_factory)
        elif url in [f'{url_path_comp}{project}/timelog']:
            return TimeLog(project, session_factory)
        elif url in [f'{url_path_comp}plat/weekly_report']:
            return WeeklyReport(project, session_factory)
        elif url in [f'{url_path_comp}{project}/issueaging']:
            return IssueAging(project, session_factory)
        elif url in [f'{url_path_comp}{project}/compliance']:
            return ProjectCompliance(project, session_factory)
        elif url in [f'{url_path_comp}{quote("plat/SQL Code Review")}']:
            return SQLCodeReview(project, session_factory)
        elif url in [f'{url_path_comp}plat/wdts']:
            return WDTimeSheet(project, session_factory)
        elif url in [f'{url_path_comp}plat/releasenotes']:
            return ReleaseNotes(project, session_factory)
        elif url in [f'{url_path_comp}plat/weekly_report']:
            return WeeklyReport(project, session_factory)
        elif url == f'{url_path_comp}plat/transition':
            return IssueTransition(project, session_factory)
        elif url in [f'{url_path_comp}r&d/creditcard']:
            return CreditCardContext(project, session_factory)
        elif url == f'{url_path_comp}r&d/chatgpt':
            return ChatGPTContext(project, session_factory)
        elif url == f'{url_path_comp}r&d/service':
            return ServiceAvailability('public', session_factory)
        elif url == f'{url_path_comp}plat/teams':
            return BulkCreateTeams(project, session_factory)
        elif url == f'{url_path_comp}public/requesttracker':
            return UploadRequestTracker(project, session_factory)
        elif url == f'{url_path_comp}public/rt':
            return ShowRequestTracker('public', session_factory)
        else:
            return NotFoundPageContent(project, session_factory)
