import os
from textwrap import dedent
from dash import html, dcc
from app import session
from flask_login import current_user

from .abc_page_layout import PageLayoutStrategy
from .html_layouts import login_2fa, login, failed, html_header
from .page_content_factory import PageContentFactory
import dash_bootstrap_components as dbc


def show_callbacks(app):
    def wrap_list(items, padding=24):
        return ("\n" + " " * padding).join(items)

    def format_regs(registrations):
        vals = sorted("{}.{}".format(i['id'], i['property'])
                      for i in registrations)
        return wrap_list(vals)

    output_list = []
    for callback_id, callback in app.callback_map.items():
        wrapped_func = callback["callback"].__wrapped__
        inputs = callback["inputs"]
        states = callback["state"]

        if callback_id.startswith(".."):
            outputs = callback_id.strip(".").split("...")
        else:
            outputs = [callback_id]

        str_values = {
            "callback": wrapped_func.__name__,
            "outputs": wrap_list(outputs),
            "filename": os.path.split(wrapped_func.__code__.co_filename)[-1],
            "lineno": wrapped_func.__code__.co_firstlineno,
            "num_inputs": len(inputs),
            "num_states": len(states),
            "inputs": format_regs(inputs),
            "states": format_regs(states),
            "num_outputs": len(outputs),
        }

        output = dedent(
            """
            callback    {callback} @ {filename}:{lineno}
            Outputs{num_outputs:>3}  {outputs}
            Inputs{num_inputs:>4}  {inputs}
            States{num_states:>4}  {states}
            """.format(**str_values)
        )

        output_list.append(output)

    return "\n".join(output_list)


class TestPageLayoutStrategy(PageLayoutStrategy):
    def render(self):
        return html.Div(), "login_2fa"

    def render_header(self):
        pass

    def render_content(self):
        pass


class LoginPageLayoutStrategy(PageLayoutStrategy):
    def render(self):
        session.permanent = True
        session['logged_in'] = True
        session['logout_reason'] = None
        return html.Div(), login

    def render_content(self):
        session.permanent = True
        session['logged_in'] = True
        session['logout_reason'] = None
        return login

    def render_header(self):
        pass


class Login2faPageStrategy(PageLayoutStrategy):
    def render(self):
        if session.get('email_id'):
            return html.Div(), login_2fa
        else:
            return html.Div(), dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}login", id="dummy")

    def render_header(self):
        pass

    def render_content(self):
        if session.get('email_id'):
            return login_2fa
        else:
            dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}login", id="dummy")


class SuccessPageStrategy(PageLayoutStrategy):
    def render(self):
        if current_user.is_authenticated:
            return html.Div(), dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}", id="dummy")
        else:
            return html.Div(), failed

    def render_header(self):
        pass

    def render_content(self):
        if current_user.is_authenticated:
            return dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}", id="dummy")
        else:
            return failed


class LogoutPageStrategy(PageLayoutStrategy):
    def render(self):
        from .html_layouts import create_modern_logout_page
        logout_reason = session.get('logout_reason', 'unknown')
        return html.Div(), create_modern_logout_page(logout_reason)
        # if current_user.is_authenticated:
        #     print("Calling from LogoutPageStrategy [page_layout.py]")
        #     session['logged_in'] = False
        #     session['logout_reason'] = 'user_initiated'
        #
        #     for key, value in session.items():
        #         print(f"{key} -> {value}")
        #
        #     for key in ('email_id', 'jira_basic_auth', 'displayName', 'projects'):
        #         session.pop(key)
        #
        #     logout_user()
        #     identity_changed.send(current_app._get_current_object(), identity=Identity(AnonymousIdentity()))
        #
        #     return html.Div([
        #         dbc.Container([
        #             html.Div([
        #                 dbc.Container([
        #                     html.H1(id='logout-message')
        #                 ])
        #             ], className='background-photo'),
        #
        #             html.Div([
        #                 html.Div([
        #                     html.Div(id='loading-message'),
        #                     html.Div(id='login-message')
        #                 ], className='middle-block'),
        #
        #                 html.Div([
        #                     html.A([
        #                         html.I(className='fa fa-3x', id='loading-icon')
        #                     ], href='#'),  # Replace '#' with the appropriate href link
        #                     html.Span('Loading...', className='sr-only')
        #                 ], className='round-class')
        #             ]),
        #
        #             html.Div([
        #                 dbc.Container([
        #                     dbc.Row([
        #                         dbc.Col([
        #                             html.Div([
        #                                 html.Div([
        #                                     html.I(className='fa fa-thumbs-o-up fa-lg')
        #                                 ], className='round-class ball'),
        #                                 html.Div('Thanks for using the site. Hope you liked it.',
        #                                          className='right-text')
        #                             ], className='row'),
        #
        #                             html.Div([
        #                                 html.Div([
        #                                     html.I(className='fa fa-mobile fa-2x')
        #                                 ], className='round-class ball'),
        #                                 html.Div('Pls share your valuable feedback', className='right-text')
        #                             ], className='row')
        #                         ], className='col-xs-12 col-sm-6')
        #                     ])
        #                 ])
        #             ], className='second')
        #         ])
        #     ]), html.Div("You have been successfully logged off.")
        # else:
        #     return html.Div(), login

    def render_header(self):
        pass

    def render_content(self):
        pass


class LoggedInPageStrategy(PageLayoutStrategy):
    def __init__(
            self,
            pathname, session_factory
    ):
        self.pathname = pathname
        self.session_factory = session_factory
        print("postgres_connection_key in LoggedInPageStrategy =", self.session_factory.config.postgres_connection_key())

    def render(self):
        # print(type(dash_app.callback_map.values()))
        project = self.pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else self.pathname.split("/")[1]
        session['DASH_BASE_PATH'] = os.getenv('DASH_BASE_PATH', '/')
        print(f"project in LoggedInPageStrategy = {project}")


        if current_user.is_authenticated:
            session['requested_path'] = None
            page_content = PageContentFactory.create(
                # self.pathname, self.pathname.split("/")[1], self.session_factory
                self.pathname, project, self.session_factory
            )
            return html_header(session=session), page_content.render()
        else:
            session['requested_path'] = self.pathname
            return html.Div(), dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}login", id="dummy")

    def render_header(self):
        if current_user.is_authenticated:
            session['DASH_BASE_PATH'] = os.getenv('DASH_BASE_PATH', '/')
            return html_header(session=session)
        return html.Div()

    def render_content(self):
        project = self.pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else self.pathname.split("/")[1]
        if current_user.is_authenticated:
            session['requested_path'] = None
            page_content = PageContentFactory.create(self.pathname, project, self.session_factory)
            return page_content.render()
        else:
            session['requested_path'] = self.pathname
            return dcc.Location(pathname=f"{os.getenv('DASH_BASE_PATH', '/')}login", id="dummy")
