from dash import html, dcc

from page_layout.page_contents import PageContent
from data import get_from_db as db


class ReleaseNotes(PageContent):
    def render(self):
        project = 'plat'
        with self.pg_session_ro as pg_session:
            if project == 'plat':
                branch_list = db.get_branch_details(pg_session)
                package_list = db.get_package_details(pg_session)
                branch_options = [dict(label=item, value=item) for item in branch_list]
                package_options = [dict(label=item, value=item) for item in package_list]
            else:
                branch_options = package_options = []

            dff = db.get_versions(pg_session)[['id',
                                               'name', 'archived', 'released', 'issuesCount', 'issuesUnresolvedCount',
                                               'issuesFixedCount',
                                               'issuesAffectedCount']].copy(deep=True)

            dff = dff.sort_values(by=['name'], ascending=False)
            options = [dict(label=name, value=name) for name, id_ in dff[['name', 'id']].values]
            platform_version = db.get_platform_version(pg_session)
            platform_options = [dict(label=item, value=item) for item in platform_version]

        return html.Div(
            children=[
                dcc.Checklist(
                    options=[
                        {'label': 'Cookie', 'value': '<PERSON>ie'},
                        {'label': 'Jazz', 'value': 'Jazz'}
                    ],
                    value=['Cookie', 'Jazz'],
                    inline=True,
                    className="container-check-box",
                    persistence=True,
                    persistence_type='local',
                    id='id-rn-project'
                ),
                html.Div(
                    children=[
                        html.Label(
                            "App From Version:",
                            style={
                                'margin-left': '50px', 'margin-right': '10px'
                            }
                        ),
                        dcc.Dropdown(
                            id="id-from-dash_app-version",
                            options=options,
                            searchable=True,
                            clearable=True,
                            className="dcc-dropdown",
                            persistence=True,
                        )
                    ],
                    className="container-text-dropdown"
                ),
                html.Div(
                    children=[
                        html.Label(
                            "App  To  Version:",
                            style={
                                'margin-left': '50px', 'margin-right': '10px'
                            }
                        ),
                        dcc.Dropdown(
                            id="id-to-dash_app-version",
                            className="dcc-dropdown",
                        )
                    ],
                    className="container-text-dropdown"
                ),
                dcc.Textarea(id="id-label-textarea",
                             style={'margin-top': '10px', 'width': '100%', 'border-radius': '10px'}),
                html.Div(
                    children=[
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Branch"), ]),
                                dcc.Dropdown(
                                    options=branch_options,
                                    className="dcc-dropdown",
                                    multi=True,
                                    searchable=True,
                                    id="id-svn-branch",
                                    persistence=True
                                ),
                            ], className="cls-format-rn-ul"
                        ),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Package"), ]),
                                dcc.Dropdown(
                                    options=package_options,
                                    className="dcc-dropdown",
                                    multi=True,
                                    searchable=True,
                                    id="id-svn-package",
                                    persistence=True
                                ),
                            ], className="cls-format-rn-ul"
                        ),
                    ]
                ),

                html.Ul(
                    children=[
                        html.Li(children=[html.Label("Platform Version"), ]),
                        html.Li(children=[
                            # dcc.Input(
                            #     placeholder="Platform Version",
                            #     id="id-rn-pf-version",
                            #     debounce=True,
                            #     persistence=True
                            # ),
                            dcc.Dropdown(
                                options=platform_options,
                                # value=platform_version[0],
                                className="dcc-dropdown",
                                multi=True,
                                searchable=True,
                                id="id-rn-pf-version",
                                persistence=True
                            ),
                            dcc.Checklist(['Yes'], ['Yes'], persistence=True, id="id-gen-pf-rn")
                        ]),
                        # html.Li(children=[html.Label("Get Platform RN")]),
                        # html.Li(dcc.Checklist(['Yes'], ['Yes'], persistence=True))
                    ], className="cls-format-rn-ul"
                ),
                html.Ul(
                    children=[
                        html.Li(children=[html.Label("KMS Version"), ]),
                        html.Li(children=[
                            dcc.Input(
                                placeholder="KMS Version",
                                id="id-rn-kms-version",
                                debounce=True,
                                persistence=True
                            ),
                            dcc.Checklist(['Yes'], ['Yes'], persistence=True, id="id-gen-kms-rn")
                        ])
                    ], className="cls-format-rn-ul"
                ),

                html.Details(
                    children=[
                        html.Summary("Cookie"),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Reporting Version"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="Reporting Version",
                                        id="id-rn-cookie-rpt-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])
                            ]
                        ),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("PDF Kafka"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="PDF Kafka Version",
                                        id="id-rn-cookie-kafka-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])

                            ]
                        ),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Report Delivery"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="Report Delivery Version",
                                        id="id-rn-cookie-rpt-delivery-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])

                            ]
                        ),
                    ],
                    open=True,
                    className="rn-details details-hide",
                    id="id-rn-cookie-details"
                ),
                html.Details(
                    children=[
                        html.Summary("Jazz"),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Reporting Version"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="Reporting Version",
                                        id="id-rn-jazz-rpt-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])
                            ]
                        ),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("PDF Kafka"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="PDF Kafka Version",
                                        id="id-rn-jazz-kafka-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])

                            ]
                        ),
                        html.Ul(
                            children=[
                                html.Li(children=[html.Label("Report Delivery"), ]),
                                html.Li(children=[
                                    dcc.Input(
                                        placeholder="Report Delivery Version",
                                        id="id-rn-jazz-rpt-delivery-version",
                                        debounce=True,
                                        persistence=True
                                    ),
                                ])

                            ]
                        ),
                    ],
                    open=True,
                    className="rn-details details-hide",
                    id="id-rn-jazz-details"
                ),

                html.Div(id="id-pf-version-output"),
                html.Div(
                    children=[
                        html.Button(
                            id="id-download-rn", children="Download RN", className="format_button",
                            disabled=True, n_clicks=0
                        ),
                    ],
                    className="container-login-button"
                ),
                dcc.Download(id="id-download-rn-xlsx"),
                html.Div(id="id-test-rn-area")
            ],
            className="container_custom fixed-bottom",
        )
