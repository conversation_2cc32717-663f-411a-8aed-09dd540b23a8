from datetime import datetime, date, timedelta
from dash import html, dcc

from page_layout.page_contents import PageContent
import dash_ag_grid as dag


class TimeLog(PageContent):
    def render(self):
        weekday = date.today().weekday()
        days_to_friday = (weekday - 4) % 7
        last_friday = date.today() - timedelta(days=days_to_friday)

        defaultColDef = {
            'flex': 1,
            "filter": "agNumberColumnFilter",
            "resizable": True,
            "sortable": True,
            "editable": False,
            "floatingFilter": True,
            # 'wrapText': True,
            'autoHeight': True,
            # 'pagination': True,
            # 'paginationPageSize': 25,
            # 'enableSorting': True,
            "minWidth": 100
        }
        columnDefs = [
            {'headerName': 'Started', 'field': 'started', 'filter': True, },
            {'headerName': 'Week Start', 'field': 'week_start', 'filter': True, },
            {'headerName': 'project', 'field': 'project', 'filter': True, },
            {'headerName': 'release', 'field': 'release', 'filter': True, },

            {'headerName': 'Epic Key', 'field': 'epic_key', 'filter': True, },
            {'headerName': 'key', 'field': 'key', 'filter': True, },
            {'headerName': 'Issue Type', 'field': 'issuetype', 'filter': True, },
            {'headerName': 'Summary', 'field': 'summary', 'filter': True, },

            {'headerName': 'Team Name', 'field': 'team_name', 'filter': True, },
            {'headerName': 'assignee', 'field': 'team_member', 'filter': True, },
            {'headerName': 'Logged Effort', 'field': 'effort_spent', 'filter': True, },

        ]

        return html.Div(
            children=[
                html.Div(
                    children=[
                        dcc.DatePickerRange(
                            id="id-timelog-date-picker-range",
                            initial_visible_month=date.today(),
                            end_date=last_friday,
                            className="date-picker mt-1",
                            max_date_allowed=last_friday,
                            show_outside_days=True,
                        ),
                        html.Button(
                            id="id-get-timelog-details", children="Get Details",
                            className="format_button",
                            disabled=False, n_clicks=0
                        ),

                        html.I(className="line-divider"),
                        html.Label(children=["Team Name"]),
                        dcc.Dropdown(id="id-timelog-team-name", disabled=True, clearable=False),
                        html.Label(children=["Team Member"]),
                        dcc.Dropdown(id="id-timelog-team-member", disabled=True, clearable=False),
                        html.Label(children=["Release"]),
                        dcc.Dropdown(
                            id="id-timelog-release-selector",
                            options=[
                                {'label': 'ALL', 'value': 'ALL'},
                                {'label': 'Professional Services', 'value': 'Professional Services'},
                                {'label': 'Production Support', 'value': 'Production Support'},
                                {'label': 'Managed Services', 'value': 'Managed Services'},
                                {'label': 'Non Billable', 'value': 'Non Billable'},
                            ],
                            multi=False, searchable=False,
                            value="ALL"
                        ),
                        html.Button(
                            id="id-get-timelog-filter", children="Filter",
                            className="format_button",
                            disabled=False, n_clicks=0
                        ),
                        dcc.Store(id="store-timelog-df")
                    ],
                    className="side-menu",
                    id=dict(type='side-panel', index='timelog')
                ),
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.Button(
                                    className="format-button",
                                    id=dict(type='toggle-panel', index="timelog")
                                ),
                            ],
                        ),
                        html.Div(
                            "Worklog Charts",
                            className="header-menu-aging",
                            id="id-timelog-header-chart"
                        ),
                        html.Div("", className="header-right-side"),
                        html.Div("", className="middle-vacant"),
                        html.Article(
                            children=[
                                # dcc.Loading(html.Div(className="layer two show", id="id-timelog-2")),
                                dcc.Loading(html.Div(className="layer two show", id="id-timelog-2")),
                                dcc.Loading(
                                    html.Div(
                                        className="layer three", id="id-timelog-3",
                                        children=[
                                            html.Button(
                                                "Download CSV", id="timelog-csv-button", n_clicks=0,
                                                className='format_button'
                                            ),
                                            dag.AgGrid(
                                                id="id-time-log-data",
                                                className="ag-theme-alpine-dark",
                                                columnDefs=columnDefs,
                                                columnSize="sizeToFit",
                                                defaultColDef=defaultColDef,
                                                dashGridOptions={
                                                    "undoRedoCellEditing": True, "rowSelection": "single",
                                                    "loadingOverlayComponent": "CustomLoadingOverlay",
                                                    "loadingOverlayComponentParams": {
                                                        "loadingMessage": "One moment please...",
                                                        "color": "white",
                                                    },
                                                    "pagination": True,
                                                    "paginationAutoPageSize": True

                                                },
                                                csvExportParams={
                                                    "fileName": "timelog.csv",
                                                },
                                            )
                                        ]
                                    )
                                ),
                            ],
                            id="id-timelog-main",
                            className="main-area",
                        ),
                        html.Div("", className="side-vacant"),
                        html.Div("", className="bottom-left"),
                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(id="id-timelog-bullet-2", className="active"),
                                    html.Li(id="id-timelog-bullet-3"),
                                ],
                                className="bullets"
                            )],
                            className="bottom-aging"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                    className="display-area",
                    id=dict(type='main-panel', index='timelog')
                )
            ],
            className="page-grid"
        )