import os
from dash import html, dcc
import socket
import pykeepass as kp

from page_layout.page_contents import PageContent
from data import is_server_accessible


def check_server_availability() -> list:
    # Read KeyPass DB
    kp_db = os.getenv("DATABASE_PATH")
    kp_key = os.getenv("MASTER_KEYFILE")
    key_pass = kp.PyKeePass(filename=kp_db, keyfile=kp_key)
    entry = key_pass.find_entries(title='JIRA_RO', first=True)

    availability = []
    # Postgres Server
    pg_server = entry._get_string_field('DB_SERVER_NAME')
    pg_port = entry._get_string_field('DB_SERVER_RO_PORT')

    availability.append(
        is_server_accessible(ip=(pg_server), port=int(pg_port))
    )

    # Redis
    availability.append(
        is_server_accessible(ip='localhost', port=6379)
    )
    return availability


class ServiceAvailability(PageContent):

    def render(self):
        status = check_server_availability()
        child_tag = []
        if status[0]:
            child_tag.append(
                html.Div(
                    children=[
                        html.Span("Postgres", className="col"),
                        html.I(
                            className="fas fa-check-circle col", style={'color': '#2ECC40', 'margin-right': '5px'}
                        ),
                    ],
                )
            )
        else:
            child_tag.append(
                html.Div(
                    children=[
                        html.Span("Postgres", className="col"),
                        html.I(
                            className="fas fa-times-circle col", style={'color': '#2ECC40', 'margin-right': '5px'}
                        ),
                    ],
                )
            )
        if status[1]:
            child_tag.append(
                html.Div(
                    children=[
                        html.Span("Redis", className="col"),
                        html.I(
                            className="fas fa-check-circle col", style={'color': '#2ECC40', 'margin-right': '5px'}
                        ),
                    ],
                )
            )
        else:
            child_tag.append(
                html.Div(
                    children=[
                        html.Span("Redis", className="col"),
                        html.I(
                            className="fas fa-times-circle col", style={'color': '#2ECC40', 'margin-right': '5px'}
                        ),
                    ],
                )
            )

        return html.Div(
            children=[
                html.H2(
                    'Service Availability Status',
                    style={'backgroundColor': '#F4B400', 'padding': '10px', 'color': 'white'},
                    className="mb-4"
                ),
                html.P(
                    'Refreshed less than 1 minute ago',
                    style={'text-align': 'right', 'font-size': '12px', 'color': '#555'}
                ),
                html.Div(children=child_tag, className="row"),
                html.Div(style={'padding': '20px', 'font-size': '12px'}, children=[
                    html.Span([
                        html.I(className="fas fa-check-circle", style={'color': '#2ECC40', 'margin-right': '5px'}),
                        "Operational"
                    ], style={'margin-right': '20px'}),
                    html.Span([
                        html.I(className="fas fa-exclamation-triangle",
                               style={'color': '#FF851B', 'margin-right': '5px'}),
                        "Degraded Performance"
                    ], style={'margin-right': '20px'}),
                    html.Span([
                        html.I(className="fas fa-minus-circle", style={'color': '#FFDC00', 'margin-right': '5px'}),
                        "Partial Outage"
                    ], style={'margin-right': '20px'}),
                    html.Span([
                        html.I(className="fas fa-times-circle", style={'color': '#FF4136', 'margin-right': '5px'}),
                        "Major Outage"
                    ], style={'margin-right': '20px'}),
                    html.Span([
                        html.I(className="fas fa-tools", style={'color': '#AAAAAA', 'margin-right': '5px'}),
                        "Maintenance"
                    ])
                ])
            ]

        )


if __name__ == "__main__":
    check_server_availability()
