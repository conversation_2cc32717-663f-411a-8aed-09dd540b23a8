from dash import html, dcc
import dash_mantine_components as dmc
from dash_iconify import DashIconify

from data.helper import make_progress_graph
from page_layout.page_contents import PageContent


class WeeklyReport(PageContent):
    def render(self):
        return html.Div(
            className="modern-weekly-report-layout",
            children=[
                # Header Section
                html.Div(
                    className="report-header",
                    children=[
                        html.Div(
                            className="header-content",
                            children=[
                                html.Div(
                                    className="title-section",
                                    children=[
                                        DashIconify(icon="mdi:file-document-multiple", className="header-icon"),
                                        html.H1("Weekly Project Reports", className="header-title"),
                                        html.P("Automated generation of comprehensive project status reports", className="header-subtitle")
                                    ]
                                ),
                                html.Div(
                                    className="header-actions",
                                    children=[
                                        html.Button(
                                            [
                                                DashIconify(icon="mdi:help-circle", className="button-icon"),
                                                "Help"
                                            ],
                                            className="modern-button secondary",
                                            title="Get help with report generation"
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                ),

                # Main Content
                html.Div(
                    className="report-content",
                    children=[
                        # Modern Tab System
                        html.Div(
                            className="modern-tab-container",
                            children=[
                                html.Div(
                                    className="tab-header",
                                    children=[
                                        html.Button(
                                            [
                                                DashIconify(icon="mdi:cookie", className="tab-icon"),
                                                "Cookie Project"
                                            ],
                                            className="tab-button active",
                                            id="cookie-tab-btn",
                                            **{"data-tab": "display-cookie"}
                                        ),
                                        html.Button(
                                            [
                                                DashIconify(icon="mdi:music-note", className="tab-icon"),
                                                "Jazz Project"
                                            ],
                                            className="tab-button",
                                            id="jazz-tab-btn",
                                            **{"data-tab": "display-jazz"}
                                        ),
                                        html.Button(
                                            [
                                                DashIconify(icon="mdi:shield-alert", className="tab-icon"),
                                                "Risk Register"
                                            ],
                                            className="tab-button",
                                            id="risk-tab-btn",
                                            **{"data-tab": "display-risk"}
                                        )
                                    ]
                                ),

                                # Tab Content
                                html.Div(
                                    className="tab-content-container",
                                    children=[
                                        # Cookie Tab Content
                                        html.Div(
                                            className="tab-panel active",
                                            id="cookie-tab-panel",
                                            children=[
                                                html.Div(
                                                    className="panel-header",
                                                    children=[
                                                        html.H3("Cookie Project Configuration", className="panel-title"),
                                                        html.P("Configure release versions and report parameters for Cookie project", className="panel-description")
                                                    ]
                                                ),

                                                # Version Management Section
                                                html.Div(
                                                    className="config-section",
                                                    children=[
                                                        html.Div(
                                                            className="section-header",
                                                            children=[
                                                                DashIconify(icon="mdi:source-branch", className="section-icon"),
                                                                html.H4("Version Management", className="section-title"),
                                                                html.Div(
                                                                    className="version-controls",
                                                                    children=[
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:plus", className="button-icon"),
                                                                                "Add Version"
                                                                            ],
                                                                            id="add-filter",
                                                                            className="modern-button primary small",
                                                                            n_clicks=0
                                                                        ),
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:minus", className="button-icon"),
                                                                                "Remove Version"
                                                                            ],
                                                                            id="remove-filter",
                                                                            className="modern-button secondary small",
                                                                            n_clicks=0
                                                                        )
                                                                    ]
                                                                )
                                                            ]
                                                        ),

                                                        # Configuration Grid Header
                                                        html.Div(
                                                            className="config-grid-header",
                                                            children=[
                                                                html.Div("Release Pattern", className="grid-header-cell"),
                                                                html.Div("Release Dropdown", className="grid-header-cell"),
                                                                html.Div("Branch Dropdown", className="grid-header-cell"),
                                                                html.Div("Report Header", className="grid-header-cell")
                                                            ]
                                                        ),

                                                        # Dynamic Configuration Rows
                                                        html.Div(children=[], id="id-status-report-div", className="config-grid-content")
                                                    ]
                                                )
                                            ]
                                        ),
                                        # Jazz Tab Content
                                        html.Div(
                                            className="tab-panel",
                                            id="jazz-tab-panel",
                                            children=[
                                                html.Div(
                                                    className="panel-header",
                                                    children=[
                                                        html.H3("Jazz Project Configuration", className="panel-title"),
                                                        html.P("Configure release versions and report parameters for Jazz project", className="panel-description")
                                                    ]
                                                ),

                                                # Version Management Section
                                                html.Div(
                                                    className="config-section",
                                                    children=[
                                                        html.Div(
                                                            className="section-header",
                                                            children=[
                                                                DashIconify(icon="mdi:source-branch", className="section-icon"),
                                                                html.H4("Version Management", className="section-title"),
                                                                html.Div(
                                                                    className="version-controls",
                                                                    children=[
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:plus", className="button-icon"),
                                                                                "Add Version"
                                                                            ],
                                                                            id="jazz-add-filter",
                                                                            className="modern-button primary small",
                                                                            n_clicks=0
                                                                        ),
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:minus", className="button-icon"),
                                                                                "Remove Version"
                                                                            ],
                                                                            id="jazz-remove-filter",
                                                                            className="modern-button secondary small",
                                                                            n_clicks=0
                                                                        )
                                                                    ]
                                                                )
                                                            ]
                                                        ),

                                                        # Configuration Grid Header
                                                        html.Div(
                                                            className="config-grid-header",
                                                            children=[
                                                                html.Div("Release Pattern", className="grid-header-cell"),
                                                                html.Div("Release Dropdown", className="grid-header-cell"),
                                                                html.Div("Branch Dropdown", className="grid-header-cell"),
                                                                html.Div("Report Header", className="grid-header-cell")
                                                            ]
                                                        ),

                                                        # Dynamic Configuration Rows
                                                        html.Div(children=[], id="id-jazz-status-report-div", className="config-grid-content")
                                                    ]
                                                )
                                            ]
                                        ),

                                        # Risk Register Tab Content
                                        html.Div(
                                            className="tab-panel",
                                            id="risk-tab-panel",
                                            children=[
                                                html.Div(
                                                    className="panel-header",
                                                    children=[
                                                        html.H3("Risk Register Management", className="panel-title"),
                                                        html.P("Manage project risks, mitigation plans, and contingency strategies", className="panel-description")
                                                    ]
                                                ),

                                                # Risk Management Section
                                                html.Div(
                                                    className="config-section",
                                                    children=[
                                                        html.Div(
                                                            className="section-header",
                                                            children=[
                                                                DashIconify(icon="mdi:shield-alert", className="section-icon"),
                                                                html.H4("Risk Management", className="section-title"),
                                                                html.Div(
                                                                    className="version-controls",
                                                                    children=[
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:plus", className="button-icon"),
                                                                                "Add Risk"
                                                                            ],
                                                                            id="id-add-filter-risk",
                                                                            className="modern-button primary small",
                                                                            n_clicks=0
                                                                        ),
                                                                        html.Button(
                                                                            [
                                                                                DashIconify(icon="mdi:minus", className="button-icon"),
                                                                                "Remove Risk"
                                                                            ],
                                                                            id="id-remove-filter-risk",
                                                                            className="modern-button secondary small",
                                                                            n_clicks=0
                                                                        )
                                                                    ]
                                                                )
                                                            ]
                                                        ),

                                                        # Risk Grid Header
                                                        html.Div(
                                                            className="risk-grid-header",
                                                            children=[
                                                                html.Div([
                                                                    DashIconify(icon="mdi:calendar", className="header-icon"),
                                                                    "Date Raised"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:alert-circle", className="header-icon"),
                                                                    "Risk Description"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:percent", className="header-icon"),
                                                                    "Probability"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:impact", className="header-icon"),
                                                                    "Impact"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:shield-check", className="header-icon"),
                                                                    "Mitigation Plan"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:backup-restore", className="header-icon"),
                                                                    "Contingency Plan"
                                                                ], className="risk-header-cell"),
                                                                html.Div([
                                                                    DashIconify(icon="mdi:checkbox-marked", className="header-icon"),
                                                                    "Select"
                                                                ], className="risk-header-cell")
                                                            ]
                                                        ),

                                                        # Dynamic Risk Rows
                                                        html.Div(children=[], id="id-plat-risk-register", className="risk-grid-content")
                                                    ]
                                                )
                                            ]
                                        )
                                    ]
                                )
                            ]
                        ),


                        # Progress and Actions Section
                        html.Div(
                            className="progress-section",
                            children=[
                                html.Div(
                                    className="progress-header",
                                    children=[
                                        DashIconify(icon="mdi:progress-check", className="progress-icon"),
                                        html.H3("Report Generation Progress", className="progress-title")
                                    ]
                                ),

                                # Progress Display
                                html.Div(
                                    className="progress-content",
                                    children=[
                                        html.Div(
                                            className="status-display",
                                            children=[
                                                html.P(id="paragraph_id", children=["Ready to generate report"], className="status-text"),
                                                dcc.Graph(
                                                    id="progress_bar_graph",
                                                    figure=make_progress_graph(0, 10),
                                                    className="progress-chart"
                                                )
                                            ]
                                        ),

                                        # Action Buttons
                                        html.Div(
                                            className="action-buttons-section",
                                            children=[
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:play-circle", className="button-icon"),
                                                        "Generate Report"
                                                    ],
                                                    id="button_id",
                                                    className="modern-button primary large",
                                                    n_clicks=0
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:stop-circle", className="button-icon"),
                                                        "Cancel Job"
                                                    ],
                                                    id="cancel_button_id",
                                                    className="modern-button secondary large"
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:download", className="button-icon"),
                                                        "Download Report"
                                                    ],
                                                    id="id-download-report",
                                                    className="modern-button success large",
                                                    n_clicks=0
                                                )
                                            ]
                                        )
                                    ]
                                )
                            ]
                        ),

                        # Log Section
                        html.Div(
                            className="log-section",
                            children=[
                                html.Div(
                                    className="log-header",
                                    children=[
                                        DashIconify(icon="mdi:text-box-multiple", className="log-icon"),
                                        html.H3("Generation Log", className="log-title")
                                    ]
                                ),
                                html.Div(
                                    id='log',
                                    className="log-content",
                                    children=[html.P("Waiting for report generation to start...", className="log-message")]
                                )
                            ]
                        )
                    ]
                ),

                # Hidden Components
                dcc.Download(id='id-status-rpt-download'),
                dcc.Store(id="id-status-rpt-filename", storage_type='memory'),
                dcc.Interval(
                    id='log-update',
                    interval=1 * 1000  # in milliseconds
                )
            ]
        )
