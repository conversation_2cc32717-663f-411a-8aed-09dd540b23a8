from dash import html, dcc
import dash_bootstrap_components as dbc
from page_layout.page_contents import PageContent
from data import get_from_db as db


class SourceControlPageContent(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            branch_list = db.get_branch_details(pg_session)
            package_list = db.get_package_details(pg_session)

        if self.project == 'plat':
            branch_options = [dict(label=item, value=item) for item in branch_list]
            package_options = [dict(label=item, value=item) for item in package_list]
        else:
            branch_options = package_options= []

        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.Label("Branch"),
                        dcc.Dropdown(
                            options=branch_options,
                            className="dcc-dropdown",
                            clearable=False,
                            searchable=True,
                            id="id-svn-branch-dash",
                            multi=False,
                        ),
                        html.Label("Package"),
                        dcc.Loading(
                            dcc.Dropdown(
                                id="id-svn-package-dash",
                                className="dcc-dropdown",
                                options=package_options,
                                clearable=False,
                                searchable=True,
                                multi=False,
                            )
                        ),
                        html.Label("Filter By: Issuetype"),
                        dcc.Dropdown(
                            id="id-svn-issuetype",
                            className="dcc-dropdown",
                            options=dict(
                                ALL='ALL', epic='Epic', standard='Standard',
                                bugs='Bugs', subtask='Sub-task', invalid='Invalid'
                            ),
                            value='ALL',
                            multi=False, clearable=False, searchable=False
                        ),
                        html.Label("Show: FixVersions"),
                        dcc.Dropdown(
                            id="id-svn-fixversion",
                            className="dcc-dropdown",
                            options=dict(ALL='ALL', missing='Missing'),
                            value='ALL',
                            multi=False, clearable=False, searchable=False
                        ),
                        html.Label("No of Records"),
                        dbc.Alert(id="id-svn-show-counts")
                    ],
                    className="side-menu",
                    id=dict(type='side-panel', index='svn')
                ),
                html.Div(
                    children=[
                        html.Button(
                            className="format-button",
                            id=dict(type='toggle-panel', index="svn")
                        ),
                        html.Div(
                            children=[
                                html.Label("SVN JIRA Mapping"),
                            ], className="header-menu",
                            id="id-svn-main-header"
                        ),
                        html.Div(
                            children=[
                                html.Button(
                                    id="id-svn-excel-download",
                                    className="format-export-button"
                                ),
                                dcc.Download(id="id-svn-download")
                            ], className="header-right-side"
                        ),
                        html.Div("", className="middle-vacant"),
                        html.Article(
                            children=[
                                dcc.Loading(html.Div(
                                    children=[html.Div("")],
                                    className="layer two show",
                                    id="id-svn-layer-2"
                                ), ),
                            ],
                            id="id-svn-details",
                            className="main-area",
                        ),
                        html.Div("", className="side-vacant"),
                        html.Div("", className="bottom-left"),
                        html.Div(children=[
                            html.Ul(
                                children=[
                                    html.Li(className="active", id="id-svn-bullet-2"),
                                    html.Li(id="id-svn-bullet-3"),

                                ],
                                className="bullets"
                            )],
                            className="bottom-middle"
                        ),
                        html.Div("", className="bottom-right"),
                    ],
                    className="display-area",
                    id=dict(type='main-panel', index='svn')
                )
            ],
            className="page-grid"
        )