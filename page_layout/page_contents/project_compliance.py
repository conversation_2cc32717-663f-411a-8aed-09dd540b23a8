from dash import html, dcc

from page_layout.page_contents import PageContent
from data import get_from_db as db


class ProjectCompliance(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            options_team = [{'label': i[0], 'value': i[0]} for i in db.get_team_names(pg_session)]

        return html.Div(
            children=[
                html.Div(children=[
                    html.Label(children=["Team"], className="", id="id-label-comp-team"),
                    dcc.Dropdown(
                        id="id-team-comp", className="dcc-dropdown",
                        options=options_team,
                        value='PLAT_REWARDS'
                    ),
                    html.I(className="line-divider"),
                    html.Label('Time Taken'),
                    dcc.Loading(id="id-fetch-time-comp"),
                ],
                    className="side-menu"),
                html.Div(children=[
                    # html.Div(children=[html.Button(className="format-button", id="id-toggle-button-aging"), ], ),
                    html.Div(" ", className="header-menu-aging", id="id-compliance-header"),
                    # html.Div("", className="header-right-side"),
                    # html.Div("", className="middle-vacant"),
                    # html.Div(id='id-issuetype-graph'),
                    html.Div(
                        children=[
                            dcc.Loading(dcc.Graph(className="layer two show", id="id-comp-graph-2")),
                            dcc.Loading(dcc.Graph(className="layer three", id="id-comp-graph-3")),
                            dcc.Loading(dcc.Graph(className="layer five", id="id-comp-graph-5")),
                            dcc.Loading(dcc.Graph(className="layer six", id="id-comp-graph-6")),
                            dcc.Loading(dcc.Graph(className="layer seven", id="id-comp-data-7")),
                        ],
                        id="id-compliance-graph",
                        className="main-area-aging",
                    ),

                    html.Div(children=[
                        html.Ul(
                            children=[
                                html.Li(id="id-comp-bullet-2", className="active"),
                                html.Li(id="id-comp-bullet-3"),
                                html.Li(id="id-comp-bullet-5"),
                                html.Li(id="id-comp-bullet-6"),
                                html.Li(id="id-comp-bullet-7"),
                            ],
                            className="bullets"
                        )],
                        className="bottom-aging"
                    ),
                    html.Div("", className="bottom-right"),
                ],
                    className="display-area-3level")
            ],
            className="page-grid"
        )