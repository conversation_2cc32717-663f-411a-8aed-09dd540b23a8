from dash import html, dcc

from page_layout.page_contents import PageContent
from data import get_from_db as db


class IssueAging(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            df = db.get_open_issues(pg_session)
            df_standardissue = df[(df["issuetype"] != 'Epic') & (~df['isSubTask'])]['issuetype'].copy(deep=True)
            df_subtaskissue = df[(df["issuetype"] != 'Epic') & (df['isSubTask'])]['issuetype'].copy(deep=True)
            standard_series = df_standardissue.value_counts()
            subtask_series = df_subtaskissue.value_counts()
            option_standard = [{'label': f'{key}, {standard_series.get(key)}', 'value': key} for key in
                               standard_series.keys()]
            option_subtask = [{'label': f'{key}, {subtask_series.get(key)}', 'value': key} for key in subtask_series.keys()]
        return html.Div(
            children=[
                html.Div(children=[
                    html.Label("Epic"),
                    dcc.Dropdown(
                        id='id-epic-issue',
                        options=[{'label': 'Epic', 'value': 'Epic'}],
                        className="dcc-dropdown",
                    ),
                    html.Label("Standard Issue Type"),
                    dcc.Dropdown(id='id-standard-issue', options=option_standard, className="dcc-dropdown", ),
                    html.Label("SubTask Issue Type"),
                    dcc.Dropdown(id='id-subtask-issue', options=option_subtask, className="dcc-dropdown", ),
                    html.Label("Created or Updated"),
                    dcc.Dropdown(
                        id='id-issuetype-when',
                        options=[{'label': 'created', 'value': 'created'}, {'label': 'updated', 'value': 'updated'}],
                        value='created',
                        clearable=False,
                        searchable=False
                    ),
                    html.Label('Time Taken'),
                    dcc.Loading(id="id-fetch-time-issuetype"),
                ],
                    className="side-menu"),
                html.Div(children=[
                    html.Div(" ", className="header-menu-aging", id="id-version-main-header"),

                    html.Div(
                        children=[
                            dcc.Loading(dcc.Graph(className="layer one show", id="id-issuetype-graph-1")),
                            dcc.Loading(dcc.Graph(className="layer two", id="id-issuetype-graph-2")),
                            dcc.Loading(dcc.Graph(className="layer three", id="id-issuetype-graph-3")),
                        ],
                        id="id-issuetype-graph",
                        className="main-area-aging",
                    ),

                    html.Div(children=[
                        html.Ul(
                            children=[
                                html.Li(className="active", id="id-aging-bullet-1"),
                                html.Li(id="id-aging-bullet-2"),
                                html.Li(id="id-aging-bullet-3"),
                                html.Li(id="id-aging-bullet-4"),
                            ],
                            className="bullets"
                        )],
                        className="bottom-aging"
                    ),
                    html.Div("", className="bottom-right"),
                ],
                    className="display-area-3level")
            ],
            className="page-grid"
        )