import dash.html as html
from dash import get_asset_url
from dash_extensions import Mermaid
from data import mermaid_constants as mc

from page_layout.page_contents import PageContent


class CreditCardContext(PageContent):
    def render(self):
        container_list = []
        for caption, image_name in [
            ["C4 System Diagram - Credit Card Processing System Architecture", "img/cc/C4_Elements.svg"],
            ["C4 Container Diagram - Credit Card Processing System Architecture", "img/cc/C4_Container_Diagram.svg"],
            ["C4 System Diagram - Credit Card Approval System", "img/cc/Credit_Card_Approval_C4_System_Diagram.svg"],
            ["C4 Container Diagram - Credit Card Approval System", "img/cc/Credit_Card_Approval_C4Container.svg"],
            ["C4 System Diagram - Credit Card Fraud Detection System",
             "img/cc/Credit_Card_Fraud_Detection_C4_System.svg"],
            ["C4 Container Diagram - Credit Card Fraud Detection System",
             "img/cc/Credit_Card_Fraud_Detection_C4_System.svg"],

        ]:
            container_list.append(
                html.Div(
                    className="swiper-slide container-swiper",
                    children=[
                        html.Img(src=get_asset_url(image_name), className="row"),
                        # html.Div(
                        #     children=[
                        #         html.Caption(caption, className="row format-caption"),
                        #         html.Img(src=get_asset_url(image_name), className="row")
                        #     ]
                        # )
                    ]
                )
            )
        for caption, chart_name in [
            ["Context Diagram of services in the credit card industry", mc.credit_card_system_context],
            ["Credit Card system containers", mc.credit_card_system_containers],
            ["Context Diagram: Services in the credit card industry", mc.credit_card_services_systems],
            ["", mc.container_issuer_sub_functions],
            ["", mc.container_issuer_authorization],
            ["C4 Context", mc.c4_contenxt_issuer_authorization],
            ["", mc.container_issuer_card_management],
            ["", mc.container_issuer_transaction_processing],
            ["", mc.container_issuer_customer_service],
            ["", mc.container_card_network],
            ["", mc.container_card_network_transaction_processing],
            ["", mc.container_card_network_authorization],
            ["", mc.container_card_network_clearing_and_settlement],
            ["", mc.container_processor],
            ["", mc.container_merchant],
            ["", mc.container_acquirer],
            ["", mc.container_gateway],
            ["", mc.container_cardholder],
            ["", mc.container_cardholder_digital],
            ["", mc.container_credit_scoring],
            ["", mc.components_credit_scoring],
            ["", mc.container_credit_scoring_data_sources],
            ["", mc.container_credit_scoring_decision_engine],
        ]:
            container_list.append(
                html.Div(
                    className="swiper-slide container-swiper",
                    children=[
                        html.Div(
                            children=[
                                # html.Caption(caption, className="row format-caption"),
                                Mermaid(chart=chart_name, className="row mermaid-diagram")
                            ]
                        )
                    ]
                )
            )

        container_list.append(
            html.Div(
                className="swiper-slide container-swiper",
                children=[
                    html.Table(
                        children=[
                            html.Thead(children=[
                                html.Tr(children=[
                                    html.Th("Function"),
                                    html.Th("Description"),
                                    html.Th("Function"),
                                    html.Th("Description"),
                                ])
                            ]),
                            html.Tbody(children=[
                                html.Tr(children=[
                                    html.Td("Cardholder"),
                                    html.Td("A person who owns a credit card and uses it to make purchases."),
                                    html.Td("Merchant"),
                                    html.Td("A business or entity that accepts credit cards as a form of payment.")
                                ], ),
                                html.Tr(children=[
                                    html.Td("Acquirer"),
                                    html.Td(
                                        "A financial institution that processes credit card transactions on behalf of merchants."
                                    ),
                                    html.Td("Issuer"),
                                    html.Td(
                                        "A financial institution that issues credit cards to cardholders."
                                    )
                                ], ),
                                html.Tr(children=[
                                    html.Td("Card Network"),
                                    html.Td(
                                        "A third-party network that facilitates the transfer of funds and information between merchants, acquirers, and issuers."
                                    ),
                                    html.Td("Payment Gateway"),
                                    html.Td(
                                        "A system that enables online transactions by securely transmitting credit card data between a merchant's website and an acquirer."
                                    )
                                ], ),
                                html.Tr(children=[
                                    html.Td("Processor"),
                                    html.Td(
                                        "A system that processes credit card transactions on behalf of acquirers and issuers."
                                    ),
                                    html.Td("Supporting Services"),
                                    html.Td(
                                        "A group of services that support the credit card industry, including customer service, fraud detection, and credit scoring."
                                    )
                                ], ),
                            ])
                        ], className="format-table"
                    ),

                ]
            ),

        )

        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.Div(
                            className="swiper",
                            children=[
                                html.Div(
                                    className="swiper-wrapper",
                                    children=container_list
                                ),
                                html.Div(
                                    className="swiper-pagination"
                                ),
                                html.Div(className="swiper-button-prev"),
                                html.Div(className="swiper-button-next"),
                                html.Div(className="swiper-scrollbar")
                            ],
                            id="id-credit-card-swiper"
                        ),
                    ],
                    # id="mermaind-credit-card-context"
                ),
            ]
        )
