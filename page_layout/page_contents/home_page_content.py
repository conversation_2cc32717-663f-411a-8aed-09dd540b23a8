import time
from collections import Counter

import psutil
from dash import html

import app
from data import get_from_db as db, is_server_accessible
import dash_bootstrap_components as dbc
from page_layout.page_contents.page_content import PageContent
from app import session, current_user, superuser_permission, roles_and_permissions, superuser_role


# Function to get the current memory usage in MB
def get_memory_usage():
    process = psutil.Process()
    mem_info = process.memory_info()
    return mem_info.rss / (1024 ** 2)


# Function to get the current CPU usage as a percentage
def get_cpu_usage():
    return psutil.cpu_percent(interval=1)


class HomePageContent(PageContent):
    def render(self):
        # DO NOT DELETE
        # Reference code on how to use Counter
        # rows = db.get_users(pg_session)
        # account_type_counts = Counter([(row.accountType, row.active) for row in rows])
        start_time = time.time()
        with self.pg_session_ro() as pg_session:
            res = db.get_user_counts(pg_session)
        inter_time = time.time()
        print(f"db execution time: {time.time() - start_time} secs")
        div_tag = html.Div(
            children=[
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.Div(
                                    children=[
                                        html.P(
                                            f"{session.get('displayName')}", id="id-welcome-user",
                                            className="card-text"
                                        ),
                                    ],
                                    className="card text-center"
                                ),
                            ],
                            className="mb-4 col-md-6 col-lg-4"
                        ),
                    ],
                    className="row justify-content-center mt-3"
                ),
                html.Div(
                    children=[
                        html.Div(
                            children=[
                                html.Div(
                                    children=[
                                        html.H3("Infra Parameters", className="card-title"),
                                        html.P(
                                            f"Memory: {get_memory_usage():.2f} MB", className="card-text"
                                        ),
                                        html.P(
                                            f"CPU:\n{get_cpu_usage():.2f}%", className="card-text"
                                        ),
                                    ],
                                    className="card"
                                ),
                            ],
                            className="mb-4 col-md-6 col-lg-4"
                        ),
                        html.Div(
                            children=[
                                html.Div(
                                    children=[
                                        html.H3("Account Summary", className="card-title"),
                                        html.I(f"User Active = {res.ActiveUser}",
                                               className="card-text"),
                                        html.I(f"User Inactive = {res.InactiveUser}",
                                               className="card-text"),
                                        html.P(f"Functional Active = {res.ActiveApp}",
                                               className="card-text"),
                                    ],
                                    className="card"
                                ),
                            ],
                            className="mb-4 col-md-6 col-lg-4"
                        ),
                    ],
                    className="row"
                ),
            ],
            # className='container_custom'
        )

        print(f"{__name__} Time taken in rendering: {time.time() - inter_time} secs")
        return div_tag
