from dash import html, dcc
import dash_bootstrap_components as dbc

from data.helper import make_progress_graph
from page_layout.page_contents import PageContent
from data import get_from_db as db


class UpdateEpicEstimates(PageContent):
    def render(self):
        df = db.get_user_detail()
        df.drop(df[df['emailAddress'] == 'NaN'].index, inplace=True)

        suggestions = df['emailAddress'].values.tolist()

        return [html.Div(children=[
            html.Ul(id='id-progressbar-epic', children=[
                html.Li(children=["O365 Login id"], id="id-1st-level-epic"),
                html.Li(children=["Verify Login"], id="id-2nd-level-epic"),
                html.Li(children=["Upload File"], id="id-3rd-level-epic"),
                html.Li(children=["Done"], id="id-4th-level-epic"),
            ], className="progressbar"),
        ], className="container_custom")] + [html.Div(children=[
            html.Ul(children=[
                html.Li(children=[html.Div(children=[
                    html.Datalist(id="id-suggested-emails-epic",
                                  children=[html.Option(value=word) for word in suggestions]),
                    html.Div(
                        children=[
                            dcc.Input(
                                type='email', id="id-login-user-epic", list="id-suggested-emails-epic",
                                placeholder="O365 email id",
                                className="input-creds",
                                debounce=True,
                                persistence=True
                            ),
                            html.Span(className='focus-input-creds'),
                            html.Span(children=[html.I(className="fa fa-envelope")], className="symbol-input-creds"),
                        ],
                        className='wrap-input-creds', tabIndex="1"
                    ),
                ]), ]),
                html.Li("", className="check-container", id="id-valid-login-name-epic")
            ], className="login-container-ul"),
            html.Div(
                html.Ul(
                    children=[
                        html.Li(children=[
                            dcc.Input(type='password', id="id-login-passwd-epic", placeholder="Password",
                                      className="input-creds", debounce=True),
                            html.Span(className='focus-input-creds'),
                            html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds")
                        ], className='wrap-input-creds'),
                        html.Li(children=[dcc.Loading(html.Div(""))], className="check-container",
                                id="id-valid-jira-cred-epic")
                    ], className="login-container-ul"
                ),
            ),
            dcc.Upload(
                [
                    'Drag and Drop or ',
                    html.A('Select a File'),
                    html.P("Only File with extension txt, csv allowed", id="id-namefile-epic")
                ],
                multiple=False,
                disabled=True,
                style={
                    'width': '95%',
                    'height': '120px',
                    'lineHeight': '60px',
                    'borderWidth': '1px',
                    'borderStyle': 'dashed',
                    'borderRadius': '5px',
                    'textAlign': 'center',
                    'border-style': 'dashed',
                    'background-color': 'gray',
                    'margin-bottom': '10px'
                },
                id="id-file-upload-epic",
            ),
            html.Div(children=[
                html.Button(id="id-run-button-id", children="Run Job!", className="format_button", disabled=False),
                html.Button(id="id_cancel_button_id", children="Cancel Running Job!", className="format_button",
                            disabled=True),
            ], className="container-login-button"),
            html.Hr(className='white'),
            # html.Div(id="id-progress-status", children=[html.P("Pls upload file!!")], className="details"),
            dbc.Alert(id="id-progress-status", ),
            html.Div(id="id-file-output"),
            dcc.Store(id='id-store-epic-list'),
            dcc.Graph(id="id-progress-bar-graph-epic", figure=make_progress_graph(0, 10)),
        ], className="container_custom")]
