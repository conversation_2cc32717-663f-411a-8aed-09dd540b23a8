import datetime
from dash import html, dcc

from page_layout.page_contents import PageContent
from data import get_from_db as db


class ComplianceDescription(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            options_team = [
                {'label': team_name[0], 'value': team_name[0]} for team_name in db.get_team_names(pg_session)
            ]
        return html.Div(
            children=[
                html.Div(
                    children=[
                        html.Label(children=["Team"], className="", id="id-label-desc-team"),
                        dcc.Dropdown(
                            id="id-team-desc", className="dcc-dropdown",
                            options=options_team,
                            value='PLAT_REWARDS',
                            clearable=False,
                            searchable=False
                        ),
                        html.Label(children=["Team Members"]),
                        dcc.Dropdown(id="id-team-member-desc", className="dcc-dropdown", clearable=False,
                                     searchable=False, ),
                        html.Label(children=["Date Range"]),
                        dcc.DatePickerRange(
                            id="id-desc-date-picker-range",
                            initial_visible_month=datetime.date.today(),
                            # min_date_allowed=datetime.date(2022, 5, 30),
                            max_date_allowed=datetime.date.today() - datetime.timedelta(days=1),
                            start_date=datetime.date(2022, 5, 30),
                            end_date=datetime.date.today() - datetime.timedelta(days=1),
                            className="date-picker"
                        ),
                        html.I(className="line-divider"),
                    ],
                    className="side-menu",
                    id=dict(type='side-panel', index='description')
                ),
                html.Div(children=[
                    html.Div(
                        children=[
                            html.Button(
                                className="format-button",
                                id=dict(type='toggle-panel', index="description")
                            ),
                        ],
                    ),
                    html.Div(
                        "Description validation: Analysis, Design, Coding and Unit Test",
                        className="header-menu-aging",
                        id="id-compliance-desc-header"
                    ),
                    html.Div("", className="header-right-side"),
                    html.Div("", className="middle-vacant"),
                    html.Article(
                        children=[
                            dcc.Loading(html.Div(className="layer three show", id="id-comp-description-3")),
                            dcc.Loading(html.Div(className="layer five", id="id-comp-description-5")),
                        ],
                        id="id-compliance-description",
                        className="main-area",
                    ),
                    html.Div("", className="side-vacant", id=""),
                    html.Div("", className="bottom-left"),
                    html.Div(children=[
                        html.Ul(
                            children=[
                                html.Li(id="id-desc-bullet-3", className="active"),
                                html.Li(id="id-desc-bullet-5"),
                            ],
                            className="bullets"
                        )],
                        className="bottom-aging"
                    ),
                    html.Div("", className="bottom-right"),
                ],
                    className="display-area",
                    id=dict(type='main-panel', index='description')
                )
            ],
            className="page-grid"
        )
