from dash import html, dcc
import dash_bootstrap_components as dbc

from data.helper import make_progress_graph
from page_layout.page_contents import PageContent
from data import get_from_db as db


class IssueTransition(PageContent):
    def render(self):
        with self.pg_session_ro as pg_session:
            df = db.get_user_detail(pg_session)
        df.drop(df[df['emailAddress'] == 'NaN'].index, inplace=True)

        suggestions = df['emailAddress'].values.tolist()

        return [
                   html.Div(
                       children=[
                           html.Ul(id='id-progressbar', children=[
                               # html.Li(children=["O365 Login id"], id="id-1st-level"),
                               # html.Li(children=["Verify Login"], id="id-2nd-level"),
                               html.Li(children=["Upload File"], id="id-3rd-level"),
                               html.Li(children=["Done"], id="id-4th-level"),
                           ], className="progressbar"),
                       ], className="container3x4")
               ] + [
                   html.Div(
                       children=[
                           # html.Ul(children=[
                           #     html.Li(children=[html.Div(children=[
                           #         html.Datalist(id="id-suggested-emails",
                           #                       children=[html.Option(value=word) for word in suggestions]),
                           #         html.Div(
                           #             children=[
                           #                 dcc.Input(
                           #                     type='email', id="id-login-user", list="id-suggested-emails",
                           #                     placeholder="O365 email id",
                           #                     className="input-creds",
                           #                     debounce=True
                           #                 ),
                           #                 html.Span(className='focus-input-creds'),
                           #                 html.Span(children=[html.I(className="fa fa-envelope")],
                           #                           className="symbol-input-creds"),
                           #             ],
                           #             className='wrap-input-creds', tabIndex="1"
                           #         ),
                           #     ]), ]),
                           #     html.Li("", className="check-container", id="id-valid-login-name")
                           # ], className="login-container-ul"),
                           # html.Div(
                           #     html.Ul(
                           #         children=[
                           #             html.Li(children=[
                           #                 dcc.Input(type='password', id="id-login-passwd", placeholder="Password",
                           #                           className="input-creds", debounce=True),
                           #                 html.Span(className='focus-input-creds'),
                           #                 html.Span(children=[html.I(className="fa fa-lock"), ],
                           #                           className="symbol-input-creds")
                           #             ], className='wrap-input-creds'),
                           #             html.Li(children=[dcc.Loading(html.Div(""))], className="check-container",
                           #                     id="id-valid-jira-cred")
                           #         ], className="login-container-ul"
                           #     ),
                           # ),
                           dcc.Upload(
                               [
                                   'Drag and Drop or ',
                                   html.A('Select a File'),
                                   html.P("Only txt, csv, xls, xlsx, ods file types allowed", id="namefile")
                               ],
                               multiple=False,
                               disabled=False,
                               style={
                                   'width': '500px',
                                   'height': '120px',
                                   'lineHeight': '60px',
                                   'borderWidth': '1px',
                                   'borderStyle': 'dashed',
                                   'borderRadius': '5px',
                                   'textAlign': 'center',
                                   'border-style': 'dashed',
                                   'background-color': 'gray',
                                   'margin-bottom': '10px'
                               },
                               id="id-file-upload-transition",
                           ),
                           html.Div(
                               children=[
                                   html.Button(id="id-run-button-transition", children="Run Job!",
                                               className="format_button",
                                               disabled=True),
                                   html.Button(id="id_cancel_button-transition", children="Cancel Running Job!",
                                               className="format_button", disabled=True),
                               ],
                               className="container-login-button"
                           ),
                           html.Hr(className='white'),
                           # html.Div(id="id-progress-status-transition", children=[html.P("Pls upload file!!")], className="details"),
                           dbc.Alert(id="id-progress-status-transition"),
                           html.Hr(className='white'),
                           # html.Div(id='output-data-upload'),
                           html.Hr(className='white'),
                           html.Div(id="id-file-output-transition"),
                           dcc.Graph(id="progress_bar_graph", figure=make_progress_graph(0, 10)),
                           dcc.Store(id='id-store-issue-list')
                       ], className="container3x4")]
