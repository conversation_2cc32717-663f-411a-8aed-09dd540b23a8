import time
from typing import List, Dict, Any

from dash import html, dcc
import dash_bootstrap_components as dbc
from dash_iconify import DashIconify
from page_layout.page_contents import PageContent
from data import get_from_db as db



class VersionPageContent(PageContent):
    def render(self):
        with self.pg_session_ro() as pg_session:
            print(f"{__file__} db url is {pg_session.get_bind().url}")
            versions: list =  db.get_all_active_versions_natsort(pg_session)
            options = [
                dict(label=name, value=name) for name in versions
            ]

        return html.Div(
            className="modern-version-layout",
            children=[
                # Modern Header Section
                html.Div(
                    className="version-header",
                    children=[
                        html.Div(
                            className="header-content",
                            children=[
                                html.Div(
                                    className="title-section",
                                    children=[
                                        DashIconify(icon="mdi:source-branch", className="header-icon"),
                                        html.Div([
                                            html.H1("Release Roadmap", className="header-title"),
                                            html.P("Manage project versions, releases, and defect tracking", className="header-subtitle")
                                        ])
                                    ]
                                ),
                                html.Div(
                                    className="header-actions",
                                    children=[
                                        html.Button(
                                            [
                                                DashIconify(icon="mdi:content-copy", className="button-icon"),
                                                "Copy Epic List"
                                            ],
                                            id="id-copy-epic-btn",
                                            className="modern-button secondary",
                                            title="Copy epic list to clipboard"
                                        ),
                                        dcc.Clipboard(
                                            id="id-copy-epic",
                                            style={"display": "none"}
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                ),

                # Main Content Area
                html.Div(
                    className="version-content",
                    children=[
                        # Modern Sidebar
                        html.Div(
                            className="modern-sidebar",
                            id=dict(type='side-panel', index='version'),
                            children=[
                                # Sidebar Header
                                html.Div(
                                    className="sidebar-header",
                                    children=[
                                        html.Button(
                                            DashIconify(icon="mdi:menu", className="toggle-icon"),
                                            className="sidebar-toggle",
                                            id=dict(type='toggle-panel', index="version"),
                                            title="Toggle Sidebar"
                                        ),
                                        html.H3("Filters & Configuration", className="sidebar-title")
                                    ]
                                ),

                                # Version Selection Section
                                html.Div(
                                    className="filter-section",
                                    children=[
                                        html.Div(
                                            className="section-header",
                                            children=[
                                                DashIconify(icon="mdi:filter-variant", className="section-icon"),
                                                html.H4("Version Selection", className="section-title")
                                            ]
                                        ),

                                        # Version State Controls (Initially Hidden)
                                        html.Details(
                                            className="version-state-details",
                                            id='details',
                                            open=False,
                                            children=[
                                                html.Summary(
                                                    [
                                                        DashIconify(icon="mdi:cog", className="summary-icon"),
                                                        "Advanced Version State"
                                                    ],
                                                    className="modern-summary"
                                                ),
                                                html.Div(
                                                    className="state-controls",
                                                    children=[
                                                        html.Div(
                                                            className="control-group",
                                                            children=[
                                                                html.Label("Released Status", className="control-label"),
                                                                dcc.Dropdown(
                                                                    options=[
                                                                        dict(label='No (Active)', value=0),
                                                                        dict(label='Yes (Released)', value=1),
                                                                    ],
                                                                    className="modern-dropdown",
                                                                    clearable=False,
                                                                    searchable=False,
                                                                    id="id-version-released-value",
                                                                    value=0
                                                                )
                                                            ]
                                                        ),
                                                        html.Div(
                                                            className="control-group",
                                                            children=[
                                                                html.Label("Archive Status", className="control-label"),
                                                                dcc.Dropdown(
                                                                    options=[
                                                                        dict(label='No (Active)', value=0),
                                                                        dict(label='Yes (Archived)', value=1),
                                                                    ],
                                                                    className="modern-dropdown",
                                                                    clearable=False,
                                                                    searchable=False,
                                                                    id="id-version-archive-value",
                                                                    value=0
                                                                )
                                                            ]
                                                        )
                                                    ]
                                                )
                                            ]
                                        ),

                                        # Version Selection
                                        html.Div(
                                            className="control-group",
                                            children=[
                                                html.Label("Select Releases", className="control-label"),
                                                dcc.Loading(
                                                    dcc.Dropdown(
                                                        id="id-versions",
                                                        className="modern-dropdown",
                                                        multi=True,
                                                        searchable=True,
                                                        options=options,
                                                        placeholder="Select one or more releases..."
                                                    ),
                                                    type="default"
                                                )
                                            ]
                                        ),

                                        # Pattern Search
                                        html.Div(
                                            className="control-group",
                                            children=[
                                                html.Label("Pattern Search", className="control-label"),
                                                html.P("Use comma-separated patterns. Prefix with ~ to exclude.", className="help-text"),
                                                html.Div(
                                                    className="search-input-group",
                                                    children=[
                                                        dcc.Input(
                                                            id="id-version-search",
                                                            type="text",
                                                            debounce=True,
                                                            value='',
                                                            placeholder="e.g., v2.1.*, ~v2.1.0",
                                                            className="modern-input"
                                                        ),
                                                        html.Button(
                                                            DashIconify(icon="mdi:magnify", className="button-icon"),
                                                            id="id-version-serach-button",
                                                            className="modern-button primary small",
                                                            title="Search versions by pattern"
                                                        )
                                                    ]
                                                )
                                            ]
                                        )
                                    ]
                                ),

                                # Defect Analysis Section
                                html.Div(
                                    className="filter-section",
                                    id="defect-analysis-section",
                                    children=[
                                        html.Div(
                                            className="section-header",
                                            children=[
                                                DashIconify(icon="mdi:bug", className="section-icon"),
                                                html.H4("Defect Analysis", className="section-title")
                                            ]
                                        ),

                                        # Version Type Selection
                                        html.Div(
                                            className="control-group",
                                            children=[
                                                html.Label("Version Type", className="control-label", id="id-release-type"),
                                                dcc.Dropdown(
                                                    id="id-release-selector",
                                                    className="modern-dropdown",
                                                    options=[
                                                        {'label': 'Affects Version (Issues Found)', 'value': 0},
                                                        {'label': 'Fix Version (Issues Fixed)', 'value': 1},
                                                    ],
                                                    value=0,
                                                    clearable=False
                                                )
                                            ]
                                        ),

                                        # Status Filter
                                        html.Div(
                                            className="control-group",
                                            children=[
                                                html.Label("Filter by Status", className="control-label", id="id-release-status-label"),
                                                dcc.Dropdown(
                                                    id="id-release-status-value",
                                                    className="modern-dropdown",
                                                    disabled=True,
                                                    clearable=False,
                                                    placeholder="Select status filter..."
                                                )
                                            ]
                                        ),

                                        # Priority Filter
                                        html.Div(
                                            className="control-group",
                                            children=[
                                                html.Label("Filter by Priority Group", className="control-label", id="id-release-priority-label"),
                                                dcc.Dropdown(
                                                    id="id-release-priority-value",
                                                    className="modern-dropdown",
                                                    disabled=True,
                                                    clearable=False,
                                                    options=[
                                                        {'label': 'All Priorities', "value": 'ALL'},
                                                        {"label": 'High Priority (>=HIGH)', "value": 'FILTER_HIGH'},
                                                        {"label": 'Low Priority (<HIGH)', "value": 'FILTER_OUT_HIGH'}
                                                    ],
                                                    value='ALL'
                                                )
                                            ]
                                        )
                                    ]
                                ),

                                # Statistics Section
                                html.Div(
                                    className="filter-section",
                                    children=[
                                        html.Div(
                                            className="section-header",
                                            children=[
                                                DashIconify(icon="mdi:chart-bar", className="section-icon"),
                                                html.H4("Statistics", className="section-title")
                                            ]
                                        ),

                                        # Priority Stats
                                        html.Div(
                                            className="stats-container",
                                            children=[
                                                html.Label("Priority Distribution", className="control-label", id="id-release-counts"),
                                                dbc.Alert(
                                                    id="id-release-alert",
                                                    className="stats-alert",
                                                    color='info',
                                                    children="Select versions to view priority statistics"
                                                )
                                            ]
                                        ),

                                        # Severity Stats
                                        html.Div(
                                            className="stats-container",
                                            children=[
                                                html.Label("Severity Distribution", className="control-label"),
                                                dbc.Alert(
                                                    id="id-release-urgency",
                                                    className="stats-alert",
                                                    color='info',
                                                    children="Select versions to view severity statistics"
                                                )
                                            ]
                                        )
                                    ]
                                )
                            ]
                        ),

                        # Main Display Area
                        html.Div(
                            className="main-display-area",
                            id=dict(type='main-panel', index='version'),
                            children=[
                                # Tab Navigation
                                html.Div(
                                    className="tab-navigation",
                                    children=[
                                        # Toggle Button
                                        html.Button(
                                            DashIconify(icon="mdi:menu", className="toggle-icon"),
                                            className="modern-toggle-button",
                                            id=dict(type='toggle-panel', index="version"),
                                            n_clicks=0,
                                            title="Toggle Sidebar"
                                        ),
                                        html.Div(
                                            className="tab-header",
                                            children=[
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:view-dashboard", className="tab-icon"),
                                                        "Release Status"
                                                    ],
                                                    className="tab-button active",
                                                    id="id-version-bullet-3",
                                                    **{"data-layer": "three"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:table", className="tab-icon"),
                                                        "Defect Details"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-8",
                                                    **{"data-layer": "eight"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:chart-timeline-variant", className="tab-icon"),
                                                        "Roadmap"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-7",
                                                    **{"data-layer": "seven"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:chart-bar", className="tab-icon"),
                                                        "Charts"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-5",
                                                    **{"data-layer": "five"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:chart-bubble", className="tab-icon"),
                                                        "Bubble Chart"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-6",
                                                    **{"data-layer": "six"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:chart-line", className="tab-icon"),
                                                        "Trends"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-2",
                                                    **{"data-layer": "two"}
                                                ),
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:counter", className="tab-icon"),
                                                        "Counts"
                                                    ],
                                                    className="tab-button",
                                                    id="id-version-bullet-1",
                                                    **{"data-layer": "one"}
                                                )
                                            ]
                                        ),

                                        # Export Button
                                        html.Div(
                                            className="export-section",
                                            children=[
                                                html.Button(
                                                    [
                                                        DashIconify(icon="mdi:file-excel", className="button-icon"),
                                                        "Export"
                                                    ],
                                                    id="id-excel-download",
                                                    className="modern-button success",
                                                    title="Export data to Excel"
                                                ),
                                                dcc.Download(id="id-download")
                                            ]
                                        )
                                    ]
                                ),

                                # Content Layers
                                html.Div(
                                    className="content-layers",
                                    children=[
                                        # Layer 1 - Counts
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-one",
                                                id="id-version-layer-1",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:counter", className="layer-icon"),
                                                            html.H3("Unresolved Issue Count", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(className="layer-content", children=["Select versions to view issue counts"])
                                                ]
                                            ),
                                            type='graph'
                                        ),

                                        # Layer 2 - Trends
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-two",
                                                id="id-version-layer-2",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:chart-line", className="layer-icon"),
                                                            html.H3("Related Issue Count", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(className="layer-content", children=["Select versions to view related issues"])
                                                ]
                                            ),
                                            type='circle'
                                        ),

                                        # Layer 3 - Release Status (Default Active)
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-three active",
                                                id="id-version-layer-3",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:view-dashboard",
                                                                        className="layer-icon"),
                                                            html.H3("Release Status", className="layer-title",
                                                                    id="id-version-main-header")
                                                        ]
                                                    ),
                                                    html.Div(
                                                        className="layer-content",
                                                        children=["Select versions to view release status"]
                                                    )
                                                ]
                                            ),
                                        ),

                                        # Layer 5 - Charts
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-five",
                                                id="id-version-layer-5",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:chart-bar", className="layer-icon"),
                                                            html.H3("Release Roadmap", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(className="layer-content", children=["Select versions to view charts"])
                                                ]
                                            )
                                        ),

                                        # Layer 6 - Bubble Chart
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-six",
                                                id="id-version-layer-6",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:chart-bubble", className="layer-icon"),
                                                            html.H3("Issue Type Analysis", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(className="layer-content", children=["Select versions to view bubble chart"])
                                                ]
                                            )
                                        ),

                                        # Layer 7 - Roadmap
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-seven",
                                                id="id-version-layer-7",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:chart-timeline-variant", className="layer-icon"),
                                                            html.H3("Release Roadmap", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(className="layer-content", children=["Select versions to view roadmap"])
                                                ]
                                            )
                                        ),

                                        # Layer 8 - Defect Details Table
                                        dcc.Loading(
                                            html.Div(
                                                className="content-layer layer-eight",
                                                id="id-version-layer-8",
                                                children=[
                                                    html.Div(
                                                        className="layer-header",
                                                        children=[
                                                            DashIconify(icon="mdi:table", className="layer-icon"),
                                                            html.H3("Defects Dashboard", className="layer-title")
                                                        ]
                                                    ),
                                                    html.Div(
                                                        className="layer-content table-container",
                                                        children=[
                                                            html.Div(
                                                                className="table-wrapper",
                                                                children=[
                                                                    html.Table(
                                                                        className="modern-table",
                                                                        id="id-version-bugs-table",
                                                                        children=[
                                                                            html.Thead(
                                                                                children=[
                                                                                    html.Tr(
                                                                                        className="table-header-row",
                                                                                        children=[
                                                                                            html.Th("Key",
                                                                                                    className="sortable"),
                                                                                            html.Th("Summary",
                                                                                                    className="sortable"),
                                                                                            html.Th("Status",
                                                                                                    className="sortable"),
                                                                                            html.Th("Priority",
                                                                                                    className="sortable"),
                                                                                            html.Th("Severity",
                                                                                                    className="sortable"),
                                                                                            html.Th("CVSS Score",
                                                                                                    className="sortable"),
                                                                                            html.Th("Issue Type",
                                                                                                    className="sortable"),
                                                                                            html.Th("Project",
                                                                                                    className="sortable"),
                                                                                            html.Th("Affects Version",
                                                                                                    className="sortable"),
                                                                                            html.Th("Fix Version",
                                                                                                    className="sortable"),
                                                                                            html.Th("Assigned To",
                                                                                                    className="sortable"),
                                                                                            html.Th("Reporter",
                                                                                                    className="sortable"),
                                                                                            html.Th("Aging (Days)",
                                                                                                    className="sortable"),
                                                                                            html.Th("Team",
                                                                                                    className="sortable"),
                                                                                            html.Th("Components",
                                                                                                    className="sortable"),
                                                                                            html.Th("Test Case#",
                                                                                                    className="sortable"),
                                                                                            html.Th("Test Name",
                                                                                                    className="sortable"),
                                                                                            html.Th("Test Automation",
                                                                                                    className="sortable"),
                                                                                        ]
                                                                                    )
                                                                                ]
                                                                            ),
                                                                            html.Tbody(
                                                                                id="id-version-bugs-table-body",
                                                                                children=[
                                                                                    html.Tr(
                                                                                        className="empty-row",
                                                                                        children=[
                                                                                            html.Td(
                                                                                                "Select versions and configure filters to view defect details",
                                                                                                colSpan=18,
                                                                                                className="empty-message"
                                                                                            )
                                                                                        ]
                                                                                    )
                                                                                ]
                                                                            )
                                                                        ]
                                                                    )
                                                                ]
                                                            )
                                                        ]
                                                    )
                                                ]
                                            )
                                        ),

                                    ]
                                )
                            ]
                        )
                    ]
                )
            ]
        )
