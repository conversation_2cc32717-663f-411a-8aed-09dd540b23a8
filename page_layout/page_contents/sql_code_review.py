import datetime

from dash import html, dcc

from data import get_from_db as db
from page_layout.page_contents import PageContent
import dash_ag_grid as dag


class SQLCodeReview(PageContent):
    def render(self):
        # with self.pg_session_ro as pg_session:
        #     df = db.get_open_code_reviews(pg_session)

        # Converted created to UTC
        date_obj = "d3.utcParse('%Y-%m-%d')(params.data.date)"

        columnDefs = [
            {'headerName': 'Issue Key', 'field': 'key', 'filter': False, },
            {'headerName': 'Summary', 'field': 'summary', 'filter': False},
            {
                'headerName': 'Description',
                'field': 'description_markdown',
                'filter': False, 'cellRenderer': 'markdown'
            },
            {
                'headerName': 'Team',
                'field': 'Team',
                'filter': True,
            },
            {
                'headerName': 'Status',
                'field': 'status',
                'filter': True,
            },
            {'headerName': 'Reporter', 'field': 'reporter', 'filter': True},
            {'headerName': 'Assigned To', 'field': 'assignee', 'filter': True},
            {'headerName': 'Last Assignee', 'field': 'last_assignee', 'filter': True},
            {'headerName': 'Created On', 'field': 'created', 'filter': "agDateColumnFilter",},
        ]

        defaultColDef = {
            'flex': 1,
            "filter": "agNumberColumnFilter",
            "resizable": True,
            "sortable": True,
            "editable": False,
            "floatingFilter": True,
            # 'wrapText': True,
            # 'autoHeight': True,
            # 'dangerously_allow_html': True,
            # 'pagination': True,
            # 'paginationPageSize': 25,
            # 'enableSorting': True,
        }

        return html.Div(
            children=[
                html.Div(
                    children=[
                        # html.Label(children=["Versions"], className="", id="id-label-code-version"),
                        # dcc.Dropdown(
                        #     id="id-code-version", className="dcc-dropdown",
                        #     options=df['affected'].unique(),
                        # ),
                        dcc.DatePickerRange(
                            start_date_placeholder_text='Start Period',
                            end_date_placeholder_text='End Period',
                            # end_date=datetime.date.today(),
                            display_format='Do MMMM YY',
                            id='id-sql-date-picker-range',
                            max_date_allowed=datetime.date.today(),
                            initial_visible_month=datetime.date.today(),
                        ),
                        html.Button(
                            id="id-submit-code-review", children="Get Details",
                            className="format_button ms-4",
                            disabled=False, n_clicks=0
                                    ),
                        html.I(className="line-divider"),
                        html.Label('Time Taken'),
                        dcc.Loading(id="id-fetch-time-comp"),
                    ],
                    className="side-menu"
                ),
                html.Div(children=[
                    html.Div("SQL Code Review", className="header-menu-aging", id="id-sql-code-header"),
                    html.Div(
                        children=[
                            # dcc.Loading(html.Div(className="layer three", id="id-review-graph-3")),
                            html.Div(
                                className="layer eight show", id="id-review-graph-8",
                                children=[
                                    dcc.Markdown("Click on the download button below to export data to CSV"),
                                    html.Button("Download CSV", id="csv-button", n_clicks=0),
                                    dag.AgGrid(
                                        id="my-data-grid",
                                        className="ag-theme-alpine-dark",
                                        rowData=None,
                                        columnDefs=columnDefs,
                                        columnSize="sizeToFit",
                                        defaultColDef=defaultColDef,
                                        dashGridOptions={
                                            "undoRedoCellEditing": True, "rowSelection": "single",
                                            "loadingOverlayComponent": "CustomLoadingOverlay",
                                            "loadingOverlayComponentParams": {
                                                "loadingMessage": "One moment please...",
                                                "color": "white",
                                            },
                                            "pagination": True,
                                            "paginationAutoPageSize": True
                                        },
                                        csvExportParams={
                                            "fileName": "ag_grid_test.csv",
                                        },
                                    )

                                ]
                            ),
                            # html.Div(className="layer ten", id="id-review-graph-10",
                            #          children=[
                            #              html.Div(id="id-review-graph-child-1"),
                            #              html.Div(id="id-review-graph-child-2"),
                            #              html.Div(id="id-review-graph-child-3"),
                            #              html.Div(id="id-review-graph-child-4"),
                            #          ]
                            #          )

                        ],
                        id="id-compliance-graph",
                        className="main-area-aging",
                    ),

                    html.Div(children=[
                        html.Ul(
                            children=[
                                # html.Li(id="id-review-bullet-3"),
                                html.Li(id="id-review-bullet-8", className="active"),
                                # html.Li(id="id-review-bullet-10", ),


                            ],
                            className="bullets"
                        )],
                        className="bottom-aging"
                    ),
                    html.Div("", className="bottom-right"),
                ],
                    className="display-area-3level")
            ], className="page-grid"
        )
