import os

import openai
from dash import html, dcc

from page_layout.page_contents import PageContent


class ChatGPTContext(PageContent):
    def render(self):
        openai.api_key = os.getenv("API_KEY")

        return html.Div(
            children=[
                html.H1("OpenAi Dash streaming MVP"),
                dcc.Input(id="text-prompt", placeholder="Ask a question"),
                html.<PERSON><PERSON>("Submit", id="submit-prompt"),
                html.H2("OpenAI text stream"),
                html.P(
                    id="response-window",
                    style={"max-width": "600px", "text-align": "justify"},
                ),
            ]

        )