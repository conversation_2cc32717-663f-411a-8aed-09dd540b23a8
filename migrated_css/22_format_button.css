:root {
    --background_content: #0e1012;
     /* --background_content: #161A1D;  */
    /* --background_content: transparent; */
}

.container-login-button {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding-top: 20px;
}

.format_button {
    /*
    font-family: Montserrat-Bold;
    font-size: 15px;
    */
    font-family: <PERSON><PERSON><PERSON>;
    font-size: small;
    font-weight: 900;
    line-height: 1.5;
    color: #fff;
    text-transform: uppercase;
  
    width: 225px ;
    height: 50px;
    border-radius: 25px;
    background: #57b846;
    
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 25px;
  
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
    /*
    color: #2186f4;
    border-style: solid;
    border-width: 1px;
    border-color: hsl(211, 91%, 54%);
    border-radius: 5px;
    background-color: #0e1012;
    text-transform: uppercase;
    font-family: sans-serif;
    transition: all 0.2s ease;
    margin-right: 10px;
    */
}

.format_button:hover {
    cursor: pointer;
}

.format_button:disabled, .format_button[disabled=disabled] {
    background-color: gray;
    text-decoration: line-through;
}

.container_custom {        
    position: absolute;
    top: 50%;
    left: 50%;
    /* horizontally and vertically center the element by moving it back by half of its width and height */
    transform: translate(-50%, -50%);    
    overflow-x: hidden;
    overflow-y: auto;
    background-color: var(--background_content);
    width: 50%;
    text-align: center;
    z-index: inherit;       
    padding: 10px;    
    

    /*
    top: 50px;
    margin-top: 50px;    
    margin-left: 100px;
    padding: 15px;
    overflow-x: hidden;
    margin-bottom: 20px;
    background-color: var(--background_content) ;    
    width: 50%;
    align-items: center;   
    text-align: center;
    */
}


.container-minimal {    
    width: 40%;
    background-color: gray;
    top: 50px;
    margin-top: 50px;
    padding: 15px;    
    border: 1px red solid;
    overflow-x: hidden;
}

.element-spacer {
    display: flex;
    justify-content: space-between;

}