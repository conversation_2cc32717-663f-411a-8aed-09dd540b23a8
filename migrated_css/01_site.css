/*
Site Wide Settings

Define Global setting for following html tags:  
- body
- header
- main
- .page-style




---------------
 


No font family specified - header
Using font family Roboto Size medium and weight 400 - body
uses display as grid with header and content area definded - .page-style

Styleguide 1.0

*/
body {
    margin: 0px; 
    padding: 0px;
    font-family: "Roboto";
    font-size: medium;    
    font-weight: 400;
    background: #dfe9ff;  
}

.page-style {    
    display: grid;
    grid-gap: 1px;
    grid-template-columns: auto;
    grid-template-areas:
    "header-area",
    "content-area";    
    grid-template-rows: auto 1fr;
    grid-auto-columns: min-content;
}

.page-style div:first-child:not(:empty){
    max-width: auto;
}

header {
    grid-area: "header-area";    
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10; 
    height: 60px;  
}


main {
    grid-area: "content-area";    
    padding-top: 60px;
    padding-bottom: 40px;
    position: relative;      
    height: calc(100vh - 120px);
    vertical-align: middle;         
}