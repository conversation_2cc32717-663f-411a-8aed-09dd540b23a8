[uwsgi]
# Source https://blog.ionelmc.ro/2022/03/14/how-to-run-uwsgi/
# source https://www.bloomberg.com/company/stories/configuring-uwsgi-production-deployment/
# Error on unknown options (prevents typos)
strict = true

# WSGI module (application callable expected inside)
module = wsgi
master = true
enable-threads = true
socket = index.sock
chmod-socket = 666

single-interpreter = true
need-app = false
# Change user after binding the socket
uid = dash
gid = dash

vacuum = true                          ; Delete sockets during shutdown
# Respect SIGTERM and do shutdown instead of reload
die-on-term = true
chdir = /opt/jiradashboard
env = .env
logto = /var/log/uwsgi/uwsgi.log
log-maxsize = 10485760      ; 10 MB = 10 * 1024 * 1024 bytes

# Respawn processes that take more than ... seconds
harakiri = 300                      ; Forcefully kill workers after 300 seconds
harakiri-verbose = true

# Respawn processes after serving ... requests
max-requests = 2000                  ; Restart workers after this many requests
max-worker-lifetime = 7200           ; Restart workers after this many seconds
reload-on-rss = 2048                 ; Restart workers after this much resident memory
reload-on-as = 1024
worker-reload-mercy = 180            ; How long to wait before forcefully killing workers



# We don't expect abuse so lets have fastest respawn possible
forkbomb-delay = 0

# Good for debugging/development
disable-logging = true                 ; enable built-in logging
log-4xx = true                          ; but log 4xx's anyway
log-5xx = true                          ; and 5xx's
auto-procname = true                    ; Process Labeling
procname-prefix = "dash "

log-zero = true
log-slow = 1000
log-date = [%%Y-%%m-%%d %%H:%%M:%%S]
log-format = %(ftime) "%(method) %(uri)" %(status) %(rsize)+%(hsize) in %(msecs)ms pid:%(pid) worker:%(wid) core:%(core)
log-format-strftime = [%%Y-%%m-%%d %%H:%%M:%%S]

# Enable the stats service for uwsgitop, pip install uwsgitop, and run:
#   uwsgitop /var/run/dash_app.stats
stats = /var/run/celery/app.stats

py-call-osafterfork = true

# Dynamic Worker Scaling (cheaper)
cheaper-algo = busyness
processes = 500                      ; Maximum number of workers allowed
cheaper = 4                          ; Minimum number of workers allowed
cheaper-initial = 6                  ; Workers created at startup
cheaper-overload = 120               ; Specifies the window, in seconds, for tracking the average busyness of workers
cheaper-step = 1                     ; How many workers to spawn at a time

cheaper-busyness-multiplier = 20     ; How many cycles to wait before killing workers
cheaper-busyness-min = 25            ; Below this threshold, kill workers (if stable for multiplier cycles)
cheaper-busyness-max = 50            ; Above this threshold, spawn new workers
cheaper-busyness-backlog-step = 2    ; How many emergency workers to create if there are too many requests in the queue
cheaper-busyness-penalty = 2         ; automatically tune the number of idle cycles needed to stop worker
cheaper-busyness-verbose = true      ; This option enables debug logs from the cheaper_busyness plugin
cheaper-busyness-backlog-alert = 50  ; Spawn emergency workers if more than this many requests are waiting in the queue
cheaper-busyness-backlog-multiplier = 2 ;
cheaper-busyness-backlog-nonzero = 60   ;