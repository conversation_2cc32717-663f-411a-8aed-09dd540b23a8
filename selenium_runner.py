import asyncio
import os
import time
from abc import ABC
from concurrent.futures import as_completed, ThreadPoolExecutor
from dependency_injector.wiring import inject, Provide
from selenium import webdriver
from selenium.common import WebDriverException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager

from custom_container import AppContainer


class ISeleniumClass(ABC):

    def __init__(
            self, headless: bool,

    ):
        options = Options()
        if headless:
            options.add_argument("--headless=new")
        cache_dir = os.path.join(os.getcwd(), ".chromedriver-cache")  # Specify the cache directory

        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    @inject
    def login(self, entry: str, my_keepass: AppContainer = Provide[AppContainer.my_keepass]):
        my_keepass = my_keepass or AppContainer()
        x = my_keepass.password_manager()
        try:
            self.driver.get(x.get_url(entry))
        except WebDriverException:
            raise WebDriverException

        username = self.driver.find_element(by=By.NAME, value='user')
        username.send_keys(x.get_username(entry))
        self.driver.find_element(by=By.NAME, value='pw').send_keys(x.get_password(entry))
        login = self.driver.find_element(By.NAME, value='ctl01')
        login.click()
        # return self.driver.get(x.get_url(entry))

    def search_by_id(self, id) -> list:
        self.driver.switch_to.new_window()
        self.driver.get(f'http://10.80.2.6/edit_bug.aspx?id={id}')
        try:
            element = self.driver.find_element(By.NAME, value='id')
            element.send_keys(id)
            search = self.driver.find_element(By.XPATH,
                                              value='/html[1]/body[1]/table[1]/tbody[1]/tr[1]/td[8]/form[1]/input[1]')
            search.click()
            summary = self.driver.find_element(By.ID, value="short_desc")

            return [id, summary.get_attribute("value")]
        except Exception:
            return [id, None]

    def close(self):
        self.driver.close()


def submit_request(x: ISeleniumClass, id: int):
    ret = x.search_by_id(id)
    return ret


def main_thread():
    x = ISeleniumClass(headless=False)
    x.login(entry="Request Tracker")
    ids = [9706237, 9691583, 9691575, 9691572, 9691592, 9689497, 9691576, 9691570, 9691602, 9691584, 9691593, 9688110,
           9691585, 9691577]

    with ThreadPoolExecutor() as executor:
        futures = [executor.submit(submit_request, x, i) for i in ids]
        results = [future.result() for future in as_completed(futures)]

    for value in results:
        print(value)

    x.close()


async def main():
    x = ISeleniumClass(headless=False)
    await x.login(entry="Request Tracker")

    semaphore_limit = 20
    x.semaphore = asyncio.Semaphore(semaphore_limit)

    ids = [9706237, 9691583, 9691575, 9691572, 9691592, 9689497, 9691576, 9691570, 9691602, 9691584, 9691593, 9688110,
           9691585, 9691577]
    tasks = [x.search_by_id(i) for i in ids]
    results = await asyncio.gather(*tasks)

    for value in results:
        print(value)


if __name__ == "__main__":
    start_time = time.perf_counter_ns()
    container = AppContainer()
    container.wire(modules=[__name__])
    # asyncio.run(main())
    main_thread()
    print(f'Total Time: {(time.perf_counter_ns() - start_time) * pow(10, -9)} s')

