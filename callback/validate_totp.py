import os

from dash import callback, Output, Input, State
from dash.exceptions import PreventUpdate
from dependency_injector.wiring import inject, Provide
from flask import session, current_app
from flask_login import login_user
from flask_principal import identity_changed, Identity

import custom_container
from data import get_from_db as db, helper
from data.helper import UserAuth


@callback(
    Output("id-validate-otp-token", "className"),
    Input("id-button-token", "n_clicks"),
    Input("id-input-token", "n_submit"),
    State("id-input-token", "value"),
)
@inject
def validate_totp_value(
        n_clicks: int, n_submit: int, token: str,
        session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory]
):
    if n_submit is None and n_clicks == 0:
        raise PreventUpdate

    if n_clicks > 0 or n_submit > 0:
        if not token.isnumeric():
            raise PreventUpdate

        # all_cookies = dict(flask.request.cookies)
        # email_address = all_cookies.get('email_address', '')
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': 'public'})
        with session_factory.db_conn_provider().session() as pg_session:
            row = db.get_single_user(session['email_id'], pg_session)

        if row['result'] == 'FOUND':
            print(f'row = {row}')
            print(f'totp_secret = {row["totp_secret"]}')
            result = helper.verify_token(row['totp_secret'], token)
            # _ = UserAuth(session['email_id'], session['displayName'])
            if result:
                return "fa fa-check fa-3x green"
            else:
                return "fa fa-times fa-3x red"
        else:
            return "fa fa-times fa-3x red"
    else:
        raise PreventUpdate


@callback(
    Output("url_login_2fa", "pathname"),
    Input("id-button-token", "n_clicks"),
    Input("id-input-token", "n_submit"),
    State("id-input-token", "value"),
    prevent_initial_callbacks=True
)
def validate_token(n_clicks: int, n_submit: int, token: str):
    if n_clicks > 0 or n_submit > 0:
        if not token.isnumeric():
            raise PreventUpdate

        # all_cookies = dict(flask.request.cookies)
        # email_address = all_cookies.get('email_address', '')

        email, totp_secret = db.get_user_totp(session['email_id'])
        result = helper.verify_token(totp_secret, token)
        print(f'Validate token result = {result}')
        user = UserAuth(session['email_id'], session['displayName'])

        if result:
            login_user(user)
            identity_changed.send(current_app._get_current_object(),
                                  identity=Identity(user.id, auth_type='jira_token+authenticator'))
            return f"{os.getenv('DASH_BASE_PATH', '/')}success"
        else:
            return f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa"
    else:
        raise PreventUpdate
