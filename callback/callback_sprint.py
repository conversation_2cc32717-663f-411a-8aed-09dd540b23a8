import os
import time
from abc import ABC
from datetime import datetime, timezone
from html.parser import HTMLParser

import numpy as np
import pandas as pd

from dash import callback, Output, Input, State, callback_context, html, dcc, dash_table
from dash.exceptions import PreventUpdate
from plotly import express as px

from container import DatabaseContainer
from data import cache, helper, <PERSON><PERSON>ogger, restapi as api, get_from_db as db, clicked
from dependency_injector.wiring import inject, Provide

# Source: https://www.educative.io/edpresso/what-is-the-html-parser-in-python
class Parser(HTMLParser, ABC):

    def __init__(self, start_tags, end_tags, all_data, comments):
        super().__init__()
        self.start_tags = start_tags
        self.end_tags = end_tags
        self.all_data = all_data
        self.comments = comments

    # method to append the start tag to the list start_tags.
    def handle_starttag(self, tag, attrs):
        self.start_tags.append(tag)

    # method to append the end tag to the list end_tags.
    def handle_endtag(self, tag):
        self.end_tags.append(tag)

    # method to append the data between the tags to the list all_data.
    def handle_data(self, data):
        self.all_data.append(data)

    # method to append the comment to the list comments.
    def handle_comment(self, data):
        self.comments.append(data)


@callback(
    Output("id-active-sprint", "options"),
    Output("id-future-sprint", "options"),
    Output("id-closed-sprint", "options"),
    Input("id-sprint-board-values", "value"),
)
def update_sprint_values(board: int):
    if board is None:
        raise PreventUpdate
    my_logger = MyLogger().get_logger()

    sprint_list = api.get_sprint_details(board)

    active_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "active"]
    future_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "future"]
    closed_sprint = [{key: value for key, value in elem.items() if key != "state"} for elem in sprint_list if
                     elem['state'] == "closed"]

    my_logger.info(f'active_sprint: {active_sprint}')
    my_logger.info(f'future_sprint: {future_sprint}')
    my_logger.info(f'closed_sprint: {closed_sprint}')
    return active_sprint, future_sprint, closed_sprint


@callback(
    Output("id-active-sprint", "value"),
    Output("id-future-sprint", "value"),
    Output("id-closed-sprint", "value"),
    Output("id-fetch-time-sprints", "value"),
    Output("id-overview-layer-1", "children"),
    Output("id-overview-layer-2", "children"),
    Output("id-overview-layer-3", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    Input("url", "pathname"),
    [
        State("id-sprint-board-values", "value"),
    ]
)
@cache.memoize(timeout=helper.TIME_OUT)
@inject
def update_sprint_details(
        active, future, closed, pathname, boardId,
    session_factory: DatabaseContainer = Provide[DatabaseContainer],
):
    user_clicked = clicked(callback_context)

    if user_clicked is None:
        raise PreventUpdate
    my_logger = MyLogger().get_logger()

    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]

    value_dict = {
        "id-active-sprint": [active, [active, "", ""]],
        "id-future-sprint": [future, ["", future, ""]],
        "id-closed-sprint": [closed, ["", "", closed]]
    }

    start_time_ns = time.perf_counter_ns()
    sprintId = value_dict[user_clicked][0]
    retValue = value_dict[user_clicked][1]

    resp = api.get_scope_change_burndown_chart(boardId, sprintId)

    for key in ["changes", "statisticField", "issueToParentKeys", "issueToSummary", "workRateData", "openCloseChanges"]:
        resp.pop(key, None)

    parser = Parser(start_tags=[], end_tags=[], all_data=[], comments=[])
    for key, value in resp.items():
        if 'lastUserWhoClosedHtml' in key:
            parser.feed(resp['lastUserWhoClosedHtml'])
            resp[key] = ",".join(parser.all_data)
        elif "warningMessage" in key:
            pass
        else:
            resp[key] = time.strftime('%d %b %Y', time.localtime(value / 1000))

    if 'lastUserWhoClosedHtml' not in resp:
        resp['lastUserWhoClosedHtml'] = 'NA'
    if 'completeTime' not in resp:
        resp['completeTime'] = 'NA'

    epic_list, issue_list, aggregatetimeoriginalestimate, storypoint = \
        api.get_board_issues_for_sprint(boardId, sprintId)
    my_logger.info(
        f'Time taken in get_board_issues_for_sprint: {(time.perf_counter_ns() - start_time_ns) * pow(10, -9)} seconds')

    # df = db.get_epic_linked_issues(project, epic_list)

    time_secs = 0
    retValue.append(time_secs)
    session_factory.config.postgres_connection_key.override(f"{project}_ro")

    SessionLocal = session_factory.postgres_sync_sessions()
    with SessionLocal() as pg_session:
        df_issue_list = db.get_issue_counts(pg_session)

    df_issue_list = df_issue_list[(df_issue_list['sprintid'] == sprintId)].copy(deep=True)
    df_story = df_issue_list[(df_issue_list['issuetype'] == 'Story')].copy(deep=True)
    df_task = df_issue_list[(df_issue_list['issuetype'] == 'Task')].copy(deep=True)
    df_bug = df_issue_list[(df_issue_list['issuetype'].isin(['Bug']))].copy(deep=True)
    df_story = df_story[['statuscategory', 'count']]
    df_story = df_story.groupby('statuscategory').sum().reset_index()
    df_task = df_task[['statuscategory', 'count']].groupby('statuscategory').sum().reset_index()
    df_bug = df_bug[['statuscategory', 'count']].groupby('statuscategory').sum().reset_index()
    df_components = df_issue_list[['components', 'count']].groupby('components').sum().reset_index()
    df_estimate = df_issue_list[['components', 'originalestimate']].groupby('components').sum().reset_index()
    row_estimate, col_estimate = df_estimate.shape

    df_storypoints = df_issue_list[['components', 'storypoints']].groupby('components').sum().reset_index()
    row_sp, _ = df_storypoints.shape

    df_initiative_estimate = df_issue_list[['initiative', 'originalestimate']].groupby('initiative').sum().reset_index()
    row_init_est, _ = df_initiative_estimate.shape

    df_initiative_sp = df_issue_list[['initiative', 'storypoints']].groupby('initiative').sum().reset_index()
    row_init_sp, _ = df_initiative_sp.shape

    fig_height = 200
    fig1 = px.bar(df_story, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  height=fig_height, width=350, text='count'
                  )
    fig2 = px.bar(df_task, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  title="Task",
                  height=fig_height, width=350, text='count'
                  )
    fig3 = px.bar(df_bug, x='statuscategory', y='count',
                  labels={'statuscategory': 'Status Category'},
                  title="Bug",
                  height=fig_height, width=350, text='count'
                  )
    fig4 = px.bar(df_components, x='components', y='count',
                  title="Count of Components", text='count', height=300,
                  )
    fig4.layout._config = dict(responsive=True)
    fig5 = {}
    if row_estimate > 0:
        fig5 = px.bar(df_estimate, x='components', y='originalestimate',
                      title="Original Effort Estimate by Components", height=300, text='originalestimate', width=1000
                      )
        fig5.update_layout(
            title=dict(x=0.5, y=0.9, xanchor='center', yanchor='top'),
            font_family="Courier New", title_font_family="Arial"
        )
        fig5.update_xaxes(type='category', showgrid=False)
        fig5.update_traces(width=0.2)

    fig6 = {}
    if row_sp > 0:
        fig6 = px.bar(df_storypoints, x='components', y='storypoints',
                      title="Story points by Components", height=300, text='storypoints', width=1000
                      )
        fig6.update_layout(
            title=dict(x=0.5, y=0.9, xanchor='center', yanchor='top'),
            font_family="Rajdhani", title_font_family="Arial"
        )
        fig6.update_xaxes(type='category', showgrid=False)
        fig6.update_traces(width=0.2)

    # Add code
    fig7 = {}
    if row_init_est > 0:
        fig7 = px.bar(df_initiative_estimate, x='initiative', y='originalestimate',
                      title="Estimation by Initiative", height=300, text='originalestimate', width=1000
                      )
    fig8 = {}
    if row_init_sp > 0:
        fig8 = px.bar(df_initiative_sp, x='initiative', y='storypoints',
                      title="Story points by Initiative", height=300, text='storypoints', width=1000
                      )
    # end code add

    fig1.update_layout(
        title=dict(text='Story', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig1.update_xaxes(type='category', showgrid=False)
    fig1.update_traces(width=0.2)

    fig2.update_layout(
        title=dict(text='Task', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig2.update_xaxes(type='category', showgrid=False)
    fig2.update_traces(width=0.2)

    fig3.update_layout(
        title=dict(text='Bug', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial"
    )
    fig3.update_xaxes(type='category', showgrid=False)
    fig3.update_traces(width=0.2)

    fig4.update_layout(
        title=dict(text='Components', x=0.5, y=0.9, xanchor='center', yanchor='top'),
        font_family="Courier New", title_font_family="Arial", autosize=True
    )
    fig4.update_xaxes(type='category', showgrid=False)
    fig4.update_traces(width=0.2)

    main_child_list = [
        html.Table(children=[
            html.Tr(children=[
                html.Td(html.Div(f"Start Date: {resp['startTime']}")),
                html.Td(html.Div(f"End Date: {resp['endTime']}")),
                html.Td(f"Completion Date: {resp['completeTime']}"),
                html.Td(f"Completed by: {resp['lastUserWhoClosedHtml']}"),
                html.Td(f"Total Original estimate(h): {aggregatetimeoriginalestimate}"),
                html.Td(f"Total story points: {storypoint}")
            ]),
        ]),
        html.Table(children=[
            html.Tr(children=[
                html.Td(html.Div(dcc.Graph(figure=fig1)), ),
                html.Td(html.Div(dcc.Graph(figure=fig2)), ),
                html.Td(html.Div(dcc.Graph(figure=fig3)), ),
            ]),
        ]),
        html.Table(children=[
            html.Tr(children=[
                html.Td(dcc.Graph(figure=fig4), ),
            ]),
        ]),
    ]

    retValue.append(main_child_list)

    retValue.append(html.Table(children=[
        html.Tr(children=[html.Td(dcc.Graph(figure=fig5))]),
        html.Tr(children=[html.Td(dcc.Graph(figure=fig6))])
    ]))

    retValue.append(html.Table(children=[
        html.Tr(children=[html.Td(dcc.Graph(figure=fig7))]),
        html.Tr(children=[html.Td(dcc.Graph(figure=fig8))])
    ]))

    return retValue


@callback(
    Output("id-overview-main-header", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    [
        State("id-sprint-board-values", "value"),
        State("id-sprint-board-values", "options"),
        State("id-active-sprint", "options"),
        State("id-future-sprint", "options"),
        State("id-closed-sprint", "options"),
    ]
)
def update_overview_header(active_value, future_value, closed_value, board_value, board_state, active_state,
                           future_state, closed_state):
    user_clicked = clicked(callback_context)

    if user_clicked is None:
        raise PreventUpdate

    value_dict = {
        "id-active-sprint": active_value, "id-future-sprint": future_value, "id-closed-sprint": closed_value
    }

    board_label = [x['label'] for x in board_state if x['value'] == board_value]
    sprint_label = [x['label'] for x in active_state + future_state + closed_state if
                    x['value'] == value_dict[user_clicked]]

    return html.Label(
        children=[
            f"{board_label[0]} (Board Id: {board_value}) and {sprint_label[0]} (Sprint Id: {value_dict[user_clicked]})"],
        className="format-text-header"
    )


@callback(
    Output("id-overview-layer-5", "children"),
    Output("id-overview-layer-4", "children"),
    Input("id-active-sprint", "value"),
    Input("id-future-sprint", "value"),
    Input("id-closed-sprint", "value"),
    Input("id-team-sprint", "value"),
    Input("id-status-sprint", "value"),
    Input("url", "pathname"),
    [
        # State("id-team-sprint", "value"),
        # State("id-status-sprint", "value"),
        State("id-sprint-board-values", "value"),
        # State("id-active-sprint", "options"),
        # State("id-future-sprint", "options"),
        # State("id-closed-sprint", "options"),
        State("id-active-sprint", "value"),
        State("id-future-sprint", "value"),
        State("id-closed-sprint", "value"),
    ]
)
@inject
def update_overview_layer_4_5(
        active, future, closed, team_name, status, pathname,
        boardId,  # active_chosen, future_chosen, closed_chosen,
        active_value, future_value, closed_value,
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
):
    user_clicked = clicked(callback_context)
    if user_clicked is None:
        raise PreventUpdate
    project = pathname.split("/")[2] if os.getenv('DASH_BASE_PATH') else pathname.split("/")[1]
    value_dict = {
        "id-active-sprint": active_value, "id-future-sprint": future_value, "id-closed-sprint": closed_value,
        "id-status-sprint": [v for v in [active_value, future_value, closed_value] if v != ""][0],
        "id-team-sprint": [v for v in [active_value, future_value, closed_value] if v != ""][0]
    }

    sprintId = value_dict[user_clicked] if user_clicked in value_dict.keys() else None

    # if sprint is not selected but Team or status drop down is selected.
    # raise PreventUpdate exception
    if sprintId is None:
        raise PreventUpdate

    session_factory.config.postgres_connection_key.override(f"{project}_ro")
    SessionLocal = session_factory.postgres_sync_sessions()

    with SessionLocal() as pg_session:

        # updated_engine = pg_session.get_bind().execution_options(schema_translate_map={None: project})
        # pg_session.bind = updated_engine
        resp = api.get_scope_change_burndown_chart(boardId, sprintId)

        # Sample JSON Response
        # {
        #     "changes": {},
        #     "startTime": 1621865066764,
        #     "endTime": 1624284240000,
        #     "completeTime": 1624464488705,
        #     "now": 1758565900996,
        #
        # start_date = time.strftime("%Y-%m-%d", time.localtime(resp['startTime'] / 1000))
        # end_date = time.strftime("%Y-%m-%d", time.localtime(resp['endTime'] / 1000))

        start_ms = resp['startTime'] / 1000
        end_ms = resp['endTime'] / 1000

        start_date = datetime.fromtimestamp(resp['startTime'] / 1000, tz=timezone.utc)
        end_date = datetime.fromtimestamp(resp['endTime'] / 1000, tz=timezone.utc)

        df = db.get_worklog_details(start_date, end_date, pg_session)

        df = df[df['Team Name'].notna()]

        start_date = datetime.fromtimestamp(resp['startTime'] / 1000)
        end_date = datetime.fromtimestamp(resp['endTime'] / 1000)

        df_team = df.loc[(df['Team Name'] == team_name)].copy(deep=True)
        df_team = df_team[['Name', status, 'Time Spent (h)']].copy(deep=True)

        df_team = df_team.groupby(['Name', status]).sum().reset_index()

        format_list = []
        date_list = [html.Div(children=["Weekday"], className="days-grid")]
        month_list = [html.Div(children=["month"], className="days-grid")]
        for single_date in helper.daterange(start_date, end_date):
            weekno = single_date.weekday()
            if weekno < 5:
                date_list.append(html.Div(children=[single_date.strftime("%a")], className="days-grid"))
                month_list.append(html.Div(children=[single_date.strftime("%b")], className="days-grid"))
            else:
                date_list.append(html.Div(children=[single_date.strftime("%a")], className="days-grid weekend"))
                month_list.append(html.Div(children=[single_date.strftime("%b")], className="days-grid weekend"))

        date_list.append(html.Div(children=[""], className="days-grid"))
        month_list.append(html.Div(children=[''], className="days-grid"))

        format_list.extend(date_list)
        format_list.extend(month_list)
        date_list = format_list

        date_list.append(html.Div(["Name/Day"], className="days-grid highlightrow"))

        for single_date in helper.daterange(start_date, end_date):
            weekno = single_date.weekday()
            if weekno < 5:
                date_list.append(
                    html.Div(children=[single_date.strftime("%d")], className="days-grid highlightrow")
                )
            else:
                date_list.append(
                    html.Div(children=[single_date.strftime("%d")], className="days-grid weekend highlightrow")
                )
        date_list.append(html.Div(["Comp %"], className="days-grid highlightrow"))

        df_team[status] = pd.to_datetime(df_team[status])

        for name in df_team['Name'].unique():
            df_name = df_team[(df_team['Name'] == name)].copy(deep=True)
            name_list = [html.Div(children=[name], className="days-grid")]
            count = 0

            for single_date in helper.daterange(start_date, end_date):
                time_spent = df_name[(df_name[status].dt.date == single_date.date())]['Time Spent (h)'].sum()
                if time_spent > 0:
                    count += 1
                weekno = single_date.weekday()
                if weekno < 5:
                    name_list.append(html.Div(children=[time_spent], className="days-grid"))
                else:
                    name_list.append(html.Div(children=[time_spent], className="days-grid weekend"))
            if end_date.date() < datetime.now().date():
                total_days = np.busday_count(start_date.date(), end_date.date())
            else:
                total_days = np.busday_count(start_date.date(), datetime.now().date())

            print(f'{total_days} - {start_date}:{end_date}')
            name_list.append(html.Div(children=["{:.2%}".format(count / total_days)], className="days-grid"))
            date_list.extend(name_list)

    return [dash_table.DataTable(
        id='datatable-worklog-details',
        columns=[
            {"name": i, "id": i, "selectable": True, } for i in df.columns
        ],
        data=df[(df['Team Name'] == team_name)].to_dict(orient="records"),
        export_format="csv",
        # editable=False,
        filter_action="native",
        sort_action="native",
        # sort_mode="multi",
        # column_selectable="single",
        # row_selectable="multi",
        page_action="native",
        page_current=0,
        page_size=10,
        style_cell={'textAlign': 'left'},
        style_cell_conditional=[
            {
                'if': {'column_id': 'Time Spent (h)'},
                'textAlign': 'right'
            }
        ],
        style_as_list_view=True,
        style_header={
            'backgroundColor': 'rgb(210, 210, 210)',
            'color': 'black',
            'fontWeight': 'bold'
        },
        style_data={
            'color': 'black',
            'backgroundColor': 'white'
        },
        style_data_conditional=[
            {
                'if': {'row_index': 'odd'},
                'backgroundColor': 'rgb(220, 220, 220)',
            }
        ],
    )], date_list


# @callback(
#     Output("id-overview-bullet-1", "className"),
#     Output("id-overview-bullet-2", "className"),
#     Output("id-overview-bullet-3", "className"),
#     Output("id-overview-bullet-4", "className"),
#     Output("id-overview-bullet-5", "className"),
#     Output("id-overview-layer-1", "className"),
#     Output("id-overview-layer-2", "className"),
#     Output("id-overview-layer-3", "className"),
#     Output("id-overview-layer-4", "className"),
#     Output("id-overview-layer-5", "className"),
#     Output("id-label-team", "className"),
#     Output("id-team-sprint", "className"),
#     Output("id-label-view", "className"),
#     Output("id-status-sprint", "className"),
#     Input("id-overview-bullet-1", "n_clicks"),
#     Input("id-overview-bullet-2", "n_clicks"),
#     Input("id-overview-bullet-3", "n_clicks"),
#     Input("id-overview-bullet-4", "n_clicks"),
#     Input("id-overview-bullet-5", "n_clicks")
# )
# def toggle_cards(id_1, id_2, id_3, id_4, id_5):
#     click_event = clicked(callback_context)
#     if click_event is None:
#         raise PreventUpdate
#     if click_event == "id-overview-bullet-1":
#         return "active", "", "", "", "", "layer one show", "layer two", "layer three", "layer four", "layer five", "hide", "dcc-dropdown hide", "hide", "dcc-dropdown hide"
#     elif click_event == "id-overview-bullet-2":
#         return "", "active", "", "", "", "layer one", "layer two show", "layer three", "layer four", "layer five", "hide", "dcc-dropdown hide", "hide", "dcc-dropdown hide"
#     elif click_event == "id-overview-bullet-3":
#         return "", "", "active", "", "", "layer one", "layer two", "layer three show", "layer four", "layer five", "", "dcc-dropdown", "", "dcc-dropdown"
#     elif click_event == "id-overview-bullet-4":
#         return "", "", "", "active", "", "layer one", "layer two", "layer three", "layer four show", "layer five", "", "dcc-dropdown", "", "dcc-dropdown"
#     elif click_event == "id-overview-bullet-5":
#         return "", "", "", "", "active", "layer one", "layer two", "layer three", "layer four", "layer five show", "", "dcc-dropdown", "", "dcc-dropdown"
