body {
  font-family: "Roboto", sans-serif;
}

/* image from https://www.pexels.com/license/ */

.background-photo {
  height: 100vh;
  width: 100%;
  background-image: url("https://images.pexels.com/photos/173435/pexels-photo-173435.jpeg");
  background-image: url("https://images.pexels.com/photos/221451/pexels-photo-221451.jpeg");
  background-size: cover;
}

.jumbotron {
  height: 50vh;
  border-bottom: 4px solid #2f1c36;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
}

.jumbotron h1 {
  padding: 5px 0;
  font-size: 13vh;
  color: #2f1c36;
  font-family: "Poiret One", sans-serif;
}

@media (min-width: 1024px) {
  .jumbotron h1 {
    padding: 0.6em 0;
  }
}
.middle-block {
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: calc( 50vh - 45px );
}
.middle-block .round-class {
  width: 80px;
  height: 80px;
  margin: 5px auto;
  padding: 15px 10px;
}
.middle-block .round-class:hover {
  background: #9e8197;
  color: white;
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.8);
}

.round-class {
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  background: #2f1c36;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.6);
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.6);
  padding: 8px 0;
  text-align: center;
}
.second {
  padding-top: 15px;
}
.second .row {
  height: 60px;
}
.second .row .round-class i.fa-mobile {
  margin-top: -4px;
}
.second .row .right-text {
  width: calc( 100% - 50px );
  padding: 10px 0;
  color: white;
  text-shadow: 1px 1px 2px black;
}
.second .row .ball {
  margin-right: 10px;
}