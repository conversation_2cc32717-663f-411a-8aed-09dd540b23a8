var myApp = angular.module('logoutApp', []);

myApp.config(['$locationProvider', function($locationProvider) {
  $locationProvider.html5Mode(true);
}]);

myApp.controller('logoutController', ['$scope', '$interval', '$timeout', '$location', function($scope, $interval, $timeout, $location) {
  // Initialize the countdown and loading status
  $scope.seconds = 5;
  $scope.loadingShowed = true;
  $scope.counter = 15;

  // Function to redirect to the relogin page
  $scope.redirect = function() {
    // $location.path('/relogin');
    window.location.href = window.dashBasePath;
  };

  // Countdown function to reduce seconds and redirect when time is up
  var countdown = $interval(function() {
    $scope.seconds--;
    if ($scope.seconds <= 0) {
      $scope.loadingShowed = false;
//      $scope.redirect();
      $interval.cancel(countdown); // Cancel the interval to prevent memory leaks
    }
  }, 1000);

  // Ensure the interval is canceled if the scope is destroyed
  $scope.$on('$destroy', function() {
    if (angular.isDefined(countdown)) {
      $interval.cancel(countdown);
    }
  });

  // Redirect after the countdown using $timeout as a fallback
  $timeout(function() {
    if ($timeout.seconds <= 0) {
      $scope.loadingShowed = false;
//      $scope.redirect();
    }
  }, $scope.seconds * 1000 ); // A little buffer to ensure the interval finishes

  // Function to handle icon click
  $scope.iconClick = function() {
    $interval.cancel(countdown); // Cancel the countdown
    $scope.loadingShowed = false;
    $scope.redirect();
  };

// Hide spinner after 2 seconds
setTimeout(function() {
    $scope.loadingShowed = false;
    $scope.redirect();
}, $scope.counter * 1000);

}]);
