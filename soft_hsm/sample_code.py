import os
import uuid

import pkcs11
from pkcs11 import <PERSON><PERSON><PERSON>, Attribute, ObjectClass

# Initialise our PKCS#11 library
lib = pkcs11.lib(os.environ['PKCS11_MODULE'])
token = lib.get_token(token_label='My token 1')

data = b'INPUT DATA'

# Open a session on our token
label = str(uuid.uuid4())
private_key = None
with token.open(user_pin='123456', rw=True) as session:
    # Generate an AES key in this session
    key = session.generate_key(pkcs11.KeyType.AES, 256)
    # Get an initialisation vector
    iv = session.generate_random(128)  # AES blocks are fixed at 128 bits
    # Encrypt our data
    crypttext = key.encrypt(data, mechanism_param=iv)

    pub, priv = session.generate_keypair(
        pkcs11.KeyType.RSA, 2048,
        label=label, store=True,
        private_template={
            Attribute.SENSITIVE: False,
            Attribute.EXTRACTABLE: True,
        }
    )

    signature = priv.sign(data)
    pub.verify(data, signature)

    ciphertext = pub.encrypt("this is test")
    print(f"type = {type(ciphertext)}")
    print(ciphertext)
    print(priv.decrypt(ciphertext))

with token.open(user_pin='123456') as session:
    keys = list(session.get_objects({
        Attribute.LABEL: label,
        Attribute.PRIVATE: True
    }))

    for i, key in enumerate(keys):
        print(i, key)
        print(type(ciphertext))
        print(key.decrypt(ciphertext))





# print(crypttext.decode('unicode_escape'))

