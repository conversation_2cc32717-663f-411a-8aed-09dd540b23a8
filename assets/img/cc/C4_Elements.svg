<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1566px" preserveAspectRatio="none" style="width:951px;height:1566px;background:#FFFFFF;" version="1.1" viewBox="0 0 951 1566" width="951px" zoomAndPan="magnify"><defs/><g><!--MD5=[92848cbaf65985a669855153c5362c37]
entity customer--><g id="elem_customer"><rect fill="#08427B" height="156.0469" rx="2.5" ry="2.5" style="stroke:#073B6F;stroke-width:0.5;" width="218" x="40" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="52" x="123" y="29.457">«person»</text><image height="48" width="48" x="125" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACbklEQVR4Xu1Wq04FMRS8cj+BT+AXsDgMHwAfQILGXUWQgMQQggRDgsBgSLiWgEIREhRIEAjsMum5dLtzetreXgKE7GTUbh/TOY921CyP/xRH+tPvchCU4yAox0FQjt8gaHF9b2XreGnjYGF1R/+dlfWCsP3uyeTt/aMNcPfwDHF6cDkrBcEPkhLi6OJGTylkjSDEKKFGUK2pRhDiwvvHUBe7iKDN/fOnl1esCBuQJfQX2/DOBqBbz726fWzdymfX93Ba786CYHV6XT0ggbDu1rZP6S9kaU09QUhVmiMIfZIjFgILyiwo438O2sWeIOv0iKAfU5hAAp9G2h4P6l49QYgrD3eAt9kxUfiIIC/53xcoaj1B48NLHu4QGptYmhAeI1EKoQAWZEUahmfHaFCFSuUSdLviKtNH0ZVvGRkC9lByIDSkCfVBKzdaUOM8wJbQDim+TIjZTLImIuJYGbTaZkRQIS2fkHC6u5STBcEeLIfzeSYeFfiFE8MtiID/iXMLaeWo7k4QVreaHmJvhaCQiD4v6oBUw5HCkZ2gaBUIMC199CzTORdqmgqyEqJ19vioYSZclLeHXL1hTBECNAhvMyYiiCW9sQ260VSQdSH46tVFWw7fOBLH9hGYCrI2k9SBGv4xIxAy2chKU997U4J848q+D0sgiWI9KFhQNGRiY8LnmeCvtsRenaBoxiXMq4PYoFtA+Lzpyh5mwgxPcbj8Ki2BXKXISNoo7L3cqYnzp3OI6G1KzAiycrAO/1EQyKvOAf200swLsl7+FYhe78S8oMa1IlQmGkk1EawSNU2hoJ/kICjHQVCOg6AcB0E5fgLPSUakWeua4wAAAABJRU5ErkJggg==" y="32.0938"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="75" x="111.5" y="96.7031">Customer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="147" y="114.752"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="198" x="50" y="132.3613">A person who uses credit cards</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="120" x="89" y="149.9707">to make purchases</text></g><!--MD5=[9081ad7617db48ffe3320d057f8988e9]
entity credit_card_system--><g id="elem_credit_card_system"><rect fill="#1168BD" height="108.0469" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="214" x="7" y="264"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="87.5" y="286.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="148" x="40" y="305.7031">Credit Card System</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="112" y="323.752"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="190" x="17" y="341.3613">A system for processing credit</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="109" x="59.5" y="358.9707">card transactions</text></g><!--MD5=[e40dd0b90ebdfcc16fe97bf774d4cfaf]
entity payment_gateway--><g id="elem_payment_gateway"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="177" x="256.5" y="255"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="318.5" y="277.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="266.5" y="294.627">&lt;$Processes credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="69" x="310.5" y="312.2363">payments&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="137" x="276.5" y="331.9219">Payment Gateway</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="343" y="349.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="330.5" y="367.5801">Java</text></g><!--MD5=[bad502f8fb138742b9948c2e5b3f55ae]
entity card_issuer--><g id="elem_card_issuer"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="174" x="707" y="488"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="767.5" y="510.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="150" x="717" y="527.627">&lt;$Issues credit cards to</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="73" x="757.5" y="545.2363">customers&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="88" x="750" y="564.9219">Card Issuer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="792" y="582.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="779.5" y="600.5801">Java</text></g><!--MD5=[f3de39f3d10df5d2308a21770693a92f]
entity acquirer--><g id="elem_acquirer"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="180" x="112" y="969"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="175.5" y="991.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="123.5" y="1008.627">&lt;$Processes credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="160" x="122" y="1026.2363">payments for merchants&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="65" x="169.5" y="1045.9219">Acquirer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="200" y="1063.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="187.5" y="1081.5801">Java</text></g><!--MD5=[91373802ae16a2c8f466f61c38c7d825]
entity card_network--><g id="elem_card_network"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="219" x="331.5" y="736"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="414.5" y="758.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="199" x="341.5" y="775.627">&lt;$Routes transactions between</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="144" x="369" y="793.2363">issuers and acquirers&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="105" x="388.5" y="812.9219">Card Network</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="439" y="830.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="22" x="430" y="848.5801">N/A</text></g><!--MD5=[db10d95f6d5cf22413b6ed668fbbdbc6]
entity fraud_detection--><g id="elem_fraud_detection"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="195" x="10.5" y="1202"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="81.5" y="1224.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="20.5" y="1241.627">&lt;$Detects fraudulent credit</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="117" x="49.5" y="1259.2363">card transactions&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="122" x="47" y="1278.9219">Fraud Detection</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="106" y="1296.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="44" x="86" y="1314.5801">Python</text></g><!--MD5=[3938681022872452b9357901721a7bd2]
entity customer_service--><g id="elem_customer_service"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="220" x="331" y="969"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="414.5" y="991.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="196" x="341" y="1008.627">&lt;$Assists customers with credit</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="165" x="358.5" y="1026.2363">card issues and inquiries&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="136" x="373" y="1045.9219">Customer Service</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="439" y="1063.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="426.5" y="1081.5801">Java</text></g><!--MD5=[bb115c575b04a96deff8efcb0782c50a]
entity processor--><g id="elem_processor"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="171" x="500.5" y="488"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="559.5" y="510.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="138" x="515" y="527.627">&lt;$Handles credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="510.5" y="545.2363">transaction processing&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="79" x="546.5" y="564.9219">Processor</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="584" y="582.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="22" x="575" y="600.5801">N/A</text></g><!--MD5=[3ceb0bfda414806be52a6d87497d4f2b]
entity merchant_portal--><g id="elem_merchant_portal"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="212" x="241" y="1202"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="320.5" y="1224.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="188" x="251" y="1241.627">&lt;$Provides merchant account</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="179" x="257.5" y="1259.2363">management and reporting&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="120" x="287" y="1278.9219">Merchant Portal</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="345" y="1296.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="49" x="322.5" y="1314.5801">Angular</text></g><!--MD5=[bb652fc1ce3da2060c9c3c7211dc4a65]
entity reporting--><g id="elem_reporting"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="178" x="150" y="1435"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="212.5" y="1457.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="154" x="160" y="1474.627">&lt;$Generates credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="127" x="175.5" y="1492.2363">transaction reports&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="76" x="201" y="1511.9219">Reporting</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="237" y="1529.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="224.5" y="1547.5801">Java</text></g><!--MD5=[6c7a3b43a568b319c0b7c06f28312f5c]
entity banking_system--><g id="elem_banking_system"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="183" x="363.5" y="1435"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="428.5" y="1457.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="159" x="373.5" y="1474.627">&lt;$Handles fund transfers</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="122" x="394" y="1492.2363">between accounts&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="125" x="392.5" y="1511.9219">Banking System</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="453" y="1529.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="22" x="444" y="1547.5801">N/A</text></g><!--MD5=[263153da100e84bd2cc2792515088f1a]
entity card_management--><g id="elem_card_management"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="219" x="684.5" y="736"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="767.5" y="758.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="145" x="719.5" y="775.627">&lt;$Manages credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="199" x="694.5" y="793.2363">information and user accounts&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="139" x="724.5" y="812.9219">Card Management</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="792" y="830.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="779.5" y="848.5801">Java</text></g><!--MD5=[f661119d5f460df205a9fe050762bf11]
entity fraud_management--><g id="elem_fraud_management"><rect fill="#1168BD" height="125.6563" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="206" x="691" y="969"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="53" x="767.5" y="991.457">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="182" x="701" y="1008.627">&lt;$Manages fraud prevention</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="157" x="715.5" y="1026.2363">rules and investigations&gt;</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="147" x="720.5" y="1045.9219">Fraud Management</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="792" y="1063.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="29" x="779.5" y="1081.5801">Java</text></g><!--MD5=[7fbffe27b2c06aa1bf672b678712be16]
link customer to credit_card_system--><g id="link_customer_credit_card_system"><path d="M137.3,163.24 C132.74,193.32 127.59,227.31 123.29,255.67 " fill="none" id="customer-to-credit_card_system" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="122.05,263.89,126.2174,256.4313,120.2854,255.5302,122.05,263.89" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="29" x="134" y="213.957">Uses</text></g><!--MD5=[fce0686d5dd2fd94a3389224a4f4909e]
link customer to payment_gateway--><g id="link_customer_payment_gateway"><path d="M214.54,163.24 C237.92,190.8 264.09,221.64 286.82,248.43 " fill="none" id="customer-to-payment_gateway" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="292.21,254.79,289.325,246.7478,284.7484,250.6278,292.21,254.79" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="96" x="266" y="206.457">Purchases using</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="291.5" y="221.5508">[HTTPS]</text></g><!--MD5=[5432c19f5f583ac867fc690334cccd34]
link payment_gateway to card_network--><g id="link_payment_gateway_card_network"><path d="M342.73,381.19 C341.94,441.5 344.47,535.21 363,614 C372.23,653.25 389.15,694.78 404.88,728.44 " fill="none" id="payment_gateway-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="408.43,735.93,407.7122,727.4162,402.2911,729.9875,408.43,735.93" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="115" x="364" y="540.957">Routes transactions</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="44" x="401" y="556.0508">through</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="400.5" y="571.1445">[HTTPS]</text></g><!--MD5=[3c32576c8eb21b02f65e6439ff8778ae]
link payment_gateway to acquirer--><g id="link_payment_gateway_acquirer"><path d="M320.5,381.14 C296.08,445.76 259.48,550.39 240,644 C217.31,753.05 208.08,882.28 204.39,960.39 " fill="none" id="payment_gateway-to-acquirer" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="204.01,968.72,207.3644,960.862,201.3704,960.594,204.01,968.72" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="122" x="241" y="672.457">Transfers payment to</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="279.5" y="687.5508">[HTTPS]</text></g><!--MD5=[bc6993c6931dcf182d6d8e7f590128b9]
link payment_gateway to processor--><g id="link_payment_gateway_processor"><path d="M409.9,381.21 C442.32,412.28 481.61,449.94 514.85,481.8 " fill="none" id="payment_gateway-to-processor" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="520.98,487.68,517.2786,479.9794,513.1278,484.312,520.98,487.68" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="137" x="489" y="424.457">Processes transactions</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="31" x="543.5" y="439.5508">using</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="546" y="454.6445">[API]</text></g><!--MD5=[d15097f246e03204b0e6d6c0950cf047]
link card_issuer to card_network--><g id="link_card_issuer_card_network"><path d="M722.21,614.02 C686.99,643.29 643.33,677.87 602,706 C587.92,715.58 572.79,725.13 557.63,734.25 " fill="none" id="card_issuer-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="550.67,738.41,559.0734,736.8665,555.9865,731.7216,550.67,738.41" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="92" x="684" y="672.457">Routes payment</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="74" x="694.5" y="687.5508">messages to</text></g><!--MD5=[72e67d6310dfd332766db8393494fbdd]
link card_network to acquirer--><g id="link_card_network_acquirer"><path d="M368.55,862.06 C357.5,871.93 346.35,882.13 336,892 C312.51,914.4 287.61,939.75 265.74,962.6 " fill="none" id="card_network-to-acquirer" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="259.95,968.67,267.641,964.9487,263.2977,960.8092,259.95,968.67" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="92" x="337" y="912.957">Routes payment</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="74" x="347.5" y="928.0508">messages to</text></g><!--MD5=[4bf2d5a608137bd884256bb114328dfe]
link card_network to customer_service--><g id="link_card_network_customer_service"><path d="M441,862.21 C441,892.51 441,929.07 441,960.41 " fill="none" id="card_network-to-customer_service" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="441,968.68,444,960.68,438,960.68,441,968.68" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="106" x="442" y="920.457">Routes inquiries to</text></g><!--MD5=[88340201b1355d9e9605e8cfb15cef87]
link acquirer to fraud_detection--><g id="link_acquirer_fraud_detection"><path d="M146.78,1095.17 C140.31,1104.69 134.45,1114.77 130,1125 C120.71,1146.35 115.29,1171.06 112.14,1193.68 " fill="none" id="acquirer-to-fraud_detection" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="111.08,1201.91,115.0692,1194.3544,109.1175,1193.5944,111.08,1201.91" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="146" x="131" y="1153.457">Sends transaction data to</text></g><!--MD5=[516c3367bb13590286a64fc742c0ed5b]
link processor to card_network--><g id="link_processor_card_network"><path d="M500.68,614.06 C491.73,623.3 483.53,633.34 477,644 C461.66,669.04 452.85,700.03 447.8,727.64 " fill="none" id="processor-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="446.41,735.7,450.7234,728.3247,444.8103,727.3071,446.41,735.7" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="104" x="484" y="672.457">Routes processed</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="119" x="478" y="687.5508">transactions through</text></g><!--MD5=[4553fbe1c33e4ae680c0a1f1f03298a3]
link acquirer to merchant_portal--><g id="link_acquirer_merchant_portal"><path d="M259.94,1095.01 C267.81,1104.71 275.42,1114.89 282,1125 C296.02,1146.56 308.71,1171.63 319.08,1194.5 " fill="none" id="acquirer-to-merchant_portal" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="322.37,1201.84,321.836,1193.3127,316.3607,1195.7664,322.37,1201.84" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="135" x="308" y="1138.457">Manages payments and</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="119" x="317.5" y="1153.5508">transactions through</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="354.5" y="1168.6445">[HTTPS]</text></g><!--MD5=[3abfd2318ec91d4b63297bea3e0061b0]
link merchant_portal to reporting--><g id="link_merchant_portal_reporting"><path d="M297.52,1328.04 C291.07,1337.73 284.98,1347.9 280,1358 C269.4,1379.5 261.01,1404.24 254.65,1426.85 " fill="none" id="merchant_portal-to-reporting" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="252.49,1434.74,257.4979,1427.8175,251.7113,1426.2316,252.49,1434.74" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="127" x="281" y="1371.457">Generates transaction</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="76" x="308" y="1386.5508">reports using</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="323.5" y="1401.6445">[HTTPS]</text></g><!--MD5=[c51e7bb39cdc20b82da477b573aaf1a2]
link merchant_portal to banking_system--><g id="link_merchant_portal_banking_system"><path d="M398.3,1328.11 C404.86,1337.75 411.01,1347.88 416,1358 C426.62,1379.51 434.75,1404.37 440.77,1427.06 " fill="none" id="merchant_portal-to-banking_system" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="442.81,1434.99,443.7139,1426.4939,437.9046,1427.9945,442.81,1434.99" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="138" x="435" y="1371.457">Performs fund transfers</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="44" x="483.5" y="1386.5508">through</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="492.5" y="1401.6445">[API]</text></g><!--MD5=[06e9033fbad07b748e59c94e2d346b8f]
link card_issuer to card_management--><g id="link_card_issuer_card_management"><path d="M794,614.08 C794,648.75 794,692.06 794,727.94 " fill="none" id="card_issuer-to-card_management" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="794,735.94,797,727.94,791,727.94,794,735.94" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="117" x="809" y="657.457">Manages credit card</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="145" x="795" y="672.5508">information and accounts</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="44" x="847" y="687.6445">through</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="846.5" y="702.7383">[HTTPS]</text></g><!--MD5=[e28064bc39b0b4572eb3c7c8c5ff813e]
link card_management to fraud_management--><g id="link_card_management_fraud_management"><path d="M794,862.21 C794,892.51 794,929.07 794,960.41 " fill="none" id="card_management-to-fraud_management" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="794,968.68,797,960.68,791,960.68,794,968.68" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="144" x="795" y="905.457">Shares data and rules for</text><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="140" x="798.5" y="920.5508">fraud prevention through</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="45" x="846" y="935.6445">[HTTPS]</text></g><!--MD5=[2e668ee10b3c2200140e581684e16d53]
@startuml C4_Elements

!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5

!define ICONSIZE 16

!include DEVICONS/angular.puml
!include DEVICONS/java.puml
!include DEVICONS/msql_server.puml
!include FONTAWESOME/users.puml

Person(customer, "Customer", "A person who uses credit cards to make purchases", $sprite="users")

System(credit_card_system, "Credit Card System", "A system for processing credit card transactions")

System(payment_gateway, "Payment Gateway", "Java", "Processes credit card payments")

System(card_issuer, "Card Issuer", "Java", "Issues credit cards to customers")

System(acquirer, "Acquirer", "Java", "Processes credit card payments for merchants")

System(card_network, "Card Network", "N/A", "Routes transactions between issuers and acquirers")

System(fraud_detection, "Fraud Detection", "Python", "Detects fraudulent credit card transactions")

System(customer_service, "Customer Service", "Java", "Assists customers with credit card issues and inquiries")

System(processor, "Processor", "N/A", "Handles credit card transaction processing")

System(merchant_portal, "Merchant Portal", "Angular", "Provides merchant account management and reporting")

System(reporting, "Reporting", "Java", "Generates credit card transaction reports")

System(banking_system, "Banking System", "N/A", "Handles fund transfers between accounts")

System(card_management, "Card Management", "Java", "Manages credit card information and user accounts")

System(fraud_management, "Fraud Management", "Java", "Manages fraud prevention rules and investigations")

Rel(customer, credit_card_system, "Uses")

Rel(customer, payment_gateway, "Purchases using", "HTTPS")
Rel(payment_gateway, card_network, "Routes transactions through", "HTTPS")
Rel(payment_gateway, acquirer, "Transfers payment to", "HTTPS")
Rel(payment_gateway, processor, "Processes transactions using", "API")
Rel(card_issuer, card_network, "Routes payment messages to")
Rel(card_network, acquirer, "Routes payment messages to")
Rel(card_network, customer_service, "Routes inquiries to")
Rel(acquirer, fraud_detection, "Sends transaction data to")
Rel(processor, card_network, "Routes processed transactions through")

Rel(acquirer, merchant_portal, "Manages payments and transactions through", "HTTPS")
Rel(merchant_portal, reporting, "Generates transaction reports using", "HTTPS")
Rel(merchant_portal, banking_system, "Performs fund transfers through", "API")

Rel(card_issuer, card_management, "Manages credit card information and accounts through", "HTTPS")
Rel(card_management, fraud_management, "Shares data and rules for fraud prevention through", "HTTPS")

@enduml

@startuml C4_Elements























skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendFontColor #FFFFFF
skinparam LegendBackgroundColor transparent
skinparam LegendBorderColor transparent

skinparam rectangle<<legendArea>> {
    backgroundcolor transparent
    bordercolor transparent
}

skinparam rectangle {
    StereotypeFontSize 12
}

skinparam database {
    StereotypeFontSize 12
}

skinparam queue {
    StereotypeFontSize 12
}

skinparam participant {
    StereotypeFontSize 12
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam person {
    StereotypeFontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    style awesome
}

skinparam rectangle<<boundary>> {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    BorderStyle dashed
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}


























































































skinparam rectangle<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<boundary>>StereotypeFontColor transparent
skinparam rectangle<<boundary>>StereotypeFontColor transparent

skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent




































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}

skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}

skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam participant<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam sequencebox<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}

skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam participant<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent






sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}

sprite $robot [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000005BFFFFFFFFFFFFFFFFFFFFFE9100000000000
0000000000AFFFFFFFFFFFFFFFFFFFFFFFFFE30000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000699405FFFFFFC427FFFFFFFFFC427FFFFFFE009982000
008FFF705FFFFFE10006FFFFFFFE00007FFFFFE00FFFF100
00CFFF705FFFFFA00001FFFFFFF900002FFFFFE00FFFF500
00DFFF705FFFFFB00002FFFFFFFA00003FFFFFE00FFFF500
00DFFF705FFFFFF4000AFFFFFFFF3000BFFFFFE00FFFF500
00DFFF705FFFFFFFA8DFFFFFFFFFFA8DFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00CFFF705FFFFFF87777777777777777CFFFFFE00FFFF500
008FFF705FFFFFF100000000000000009FFFFFE00FFFF100
000699405FFFFFF76666666666666666CFFFFFE009982000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000000EFFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFD0000000000
00000000004CFFFFFFFFFFFFFFFFFFFFFFFF910000000000
000000000000011111111111111111111110000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $robot2 [48x48/16] {
000000000000000088888888888888880000000000000000
000000000000000AFFFFFFFFFFFFFFFFA000000000000000
00000000000000CFFFFFFFFFFFFFFFFFFC00000000000000
00000000000004EFFFFFFFFFFFFFFFFFFE40000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFFA0000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000004CFFFFFFFFFFFFFFFFFFC40000000000000
000000488888848CFFFFFFFFFFFFFFFFC848888884000000
00000CFFFFFFFFC888888888888888888CFFFFFFFFC00000
00008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80000
0000CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0000CFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFC0000
00008FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF80000
00000CFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFC00000
000000488887578888888888888888888864688884000000
000000000000000000000000000000000000000000000000
}




skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam participant<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam sequencebox<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}

skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam participant<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam sequencebox<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent



















sprite $angular [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000016500000000000000000000000
000000000000000000004AFFFFB500000000000000000000
0000000000000000039FFFFFFFFFFB500000000000000000
0000000000000039EFFFFFFEFFFFFFFE9400000000000000
0000000000028DFFFFFFFFF7AFFFFFFFFFEA400000000000
0000000016CFFFFFFFFFFFF12FFFFFFFFFFFFE9300000000
0000000BFFFFFFFFFFFFFF800BFFFFFFFFFFFFFFD0000000
0000000DFFFFFFFFFFFFFF2003FFFFFFFFFFFFFFD0000000
0000000BFFFFFFFFFFFFFA0000CFFFFFFFFFFFFFB0000000
00000009FFFFFFFFFFFFF300004FFFFFFFFFFFFF90000000
00000007FFFFFFFFFFFFC000000DFFFFFFFFFFFF70000000
00000005FFFFFFFFFFFF40043006FFFFFFFFFFFF50000000
00000003FFFFFFFFFFFD000BA000EFFFFFFFFFFF30000000
00000001FFFFFFFFFFF6002FF2007FFFFFFFFFFF10000000
00000000FFFFFFFFFFE0008FF9000EFFFFFFFFFF00000000
00000000EFFFFFFFFF8000EFFF2008FFFFFFFFFD00000000
00000000CFFFFFFFFF1006FFFF9001FFFFFFFFFB00000000
00000000AFFFFFFFF9000DFFFFF1009FFFFFFFF900000000
000000008FFFFFFFF20005555552002FFFFFFFF800000000
000000006FFFFFFFB00000000000000AFFFFFFF600000000
000000004FFFFFFF4000000000000002FFFFFFF400000000
000000002FFFFFFD0005888888888000BFFFFFF200000000
000000000FFFFFF5000EFFFFFFFFF4004FFFFFF000000000
000000000EFFFFE0006FFFFFFFFFFB000CFFFFE000000000
000000000CFFFF7000CFFFFFFFFFFF2005FFFFC000000000
000000000AFFFF1002FFFFFFFFFFFF9000DFFFA000000000
0000000008FFFB444AFFFFFFFFFFFFF3338FFF8000000000
0000000006FFFFFFFFFFFFFFFFFFFFFFFFFFFF6000000000
0000000003EFFFFFFFFFFFFFFFFFFFFFFFFFFE2000000000
000000000018FFFFFFFFFFFFFFFFFFFFFFFF800000000000
00000000000019FFFFFFFFFFFFFFFFFFFF91000000000000
000000000000003CFFFFFFFFFFFFFFFFB200000000000000
00000000000000004DFFFFFFFFFFFFC30000000000000000
0000000000000000005EFFFFFFFFE5000000000000000000
000000000000000000007FFFFFE700000000000000000000
00000000000000000000019FF91000000000000000000000
000000000000000000000002200000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV ANGULAR>> White
sprite $java [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000120000000000000000000
000000000000000000000000000080000000000000000000
0000000000000000000000000001B0000000000000000000
0000000000000000000000000005A0000000000000000000
000000000000000000000000000C70000000000000000000
000000000000000000000000006F10000000000000000000
00000000000000000000000005F700000000000000000000
0000000000000000000000006FB000000000000000000000
000000000000000000000009FB0001650000000000000000
0000000000000000000001CFA0018C200000000000000000
000000000000000000002DF9005E90000000000000000000
00000000000000000001DF8008F700000000000000000000
0000000000000000000BFB005FA000000000000000000000
0000000000000000001FF200CF3000000000000000000000
0000000000000000001FD000FF4000000000000000000000
0000000000000000000DE000EFB000000000000000000000
00000000000000000005F2008FF600000000000000000000
00000000000000000000AA001EFE00000000000000000000
000000000000000000000B4004FF40000000000000000000
0000000000000000000000A100BF20000000000000000000
00000000000000000000000400A900000011000000000000
00000000000000016730000001800000046CB10000000000
00000000000005BD30000000010000120000DC0000000000
0000000000001DFEA7765567889BCB7000008F1000000000
000000000000001468899988753200000000BE0000000000
000000000000000001000000000000000004F50000000000
0000000000000000C800000000024200004E500000000000
0000000000000000CFFDCBBBCEFFFC201881000000000000
00000000000000000268AAAAA86400003000000000000000
000000000000000000000000000000000000000000000000
000000000000000009A10000002410000000000000000000
00000000000000000CFFFEDDEFFFF6000000000000000000
00000000000003520049CEEFFCA720000000000000000000
000000000019B50000000000000000000002400000000000
0000000002FF820000000000000000000378000000000000
00000000018DFFDB9766555566789ACCA610160000000000
00000000000001357899AABAA9875310026AA10000000000
0000000000000003542211111233579BCB72000000000000
000000000000000002457778888765310000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV JAVA>> White
sprite $msql_server [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000001000000000000000000000000000
000000000000000000454300000000000000000000000000
000000000000000176100600000000000000000000000000
000000000000007C77530800000000000000000000000000
000000000000099080246B70000000000000000000000000
00000000000003B75B1283B0000000000000000000000000
00000000000000475BAE9688000000000000000000000000
000000000000000466004ABACB9520000000000000000000
000000000000000049147060005CDD940000000000000000
000000000000000009B600801963304AE810000000000000
000000000000000002FD9497A10800001BF8000000000000
000000000000000000915CFD201902896467C20000000000
000000000000000000802BB8FAAAB8200A002B2000000000
0000000000000000007394602EFA0000940001A100000000
000000000000000000A80928BE5D70056000001800000000
000000000000000000E82DB48500A6371489875200000000
0000000000000000036BF60480001CDCA620000000000000
0000000000000000084DC228038CB7200000000000000000
00000000000000003B941798CA9800000000000000000000
000000000000000196506EE9403900000000000000000000
0000000000000008769C60A0353B00000000000000000000
000000000000009DCD8900A0007B00000000000000000000
00000000000009F91B165090174B00000000000000000000
0000000000009C9059008788602B00000000000000000000
0000000000062590D1004FD1004900000000000000000000
00000000005003C7839C8C27505800000000000000000000
00000000080000EFC830760014A500000000000000000000
00000000831689ED1000D00055B200000000000000000000
00000000E6510754D206728600D000000000000000000000
00000001F10027003C6EB60002A000000000000000000000
00000000CA0080005BFE6000075000000000000000000000
000000002F86148843B037653B0000000000000000000000
0000000002DC62000A100015780000000000000000000000
000000000007D71063037740710000000000000000000000
000000000000049AB8630000800000000000000000000000
000000000000000025887656200000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV MSQL_SERVER>> White
sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White

rectangle "<$users>\n== Customer\n\nA person who uses credit cards to make purchases" <<person>> as customer 

  rectangle "== Credit Card System\n\nA system for processing credit card transactions" <<system>> as credit_card_system

  rectangle "<$Processes credit card payments>\n== Payment Gateway\n\nJava" <<system>> as payment_gateway

  rectangle "<$Issues credit cards to customers>\n== Card Issuer\n\nJava" <<system>> as card_issuer

  rectangle "<$Processes credit card payments for merchants>\n== Acquirer\n\nJava" <<system>> as acquirer

  rectangle "<$Routes transactions between issuers and acquirers>\n== Card Network\n\nN/A" <<system>> as card_network

  rectangle "<$Detects fraudulent credit card transactions>\n== Fraud Detection\n\nPython" <<system>> as fraud_detection

  rectangle "<$Assists customers with credit card issues and inquiries>\n== Customer Service\n\nJava" <<system>> as customer_service

  rectangle "<$Handles credit card transaction processing>\n== Processor\n\nN/A" <<system>> as processor

  rectangle "<$Provides merchant account management and reporting>\n== Merchant Portal\n\nAngular" <<system>> as merchant_portal

  rectangle "<$Generates credit card transaction reports>\n== Reporting\n\nJava" <<system>> as reporting

  rectangle "<$Handles fund transfers between accounts>\n== Banking System\n\nN/A" <<system>> as banking_system

  rectangle "<$Manages credit card information and user accounts>\n== Card Management\n\nJava" <<system>> as card_management

  rectangle "<$Manages fraud prevention rules and investigations>\n== Fraud Management\n\nJava" <<system>> as fraud_management

customer - ->> credit_card_system : **Uses**

customer - ->> payment_gateway : **Purchases using**\n//<size:12>[HTTPS]</size>//
payment_gateway - ->> card_network : **Routes transactions through**\n//<size:12>[HTTPS]</size>//
payment_gateway - ->> acquirer : **Transfers payment to**\n//<size:12>[HTTPS]</size>//
payment_gateway - ->> processor : **Processes transactions using**\n//<size:12>[API]</size>//
card_issuer - ->> card_network : **Routes payment messages to**
card_network - ->> acquirer : **Routes payment messages to**
card_network - ->> customer_service : **Routes inquiries to**
acquirer - ->> fraud_detection : **Sends transaction data to**
processor - ->> card_network : **Routes processed transactions through**

acquirer - ->> merchant_portal : **Manages payments and transactions through**\n//<size:12>[HTTPS]</size>//
merchant_portal - ->> reporting : **Generates transaction reports using**\n//<size:12>[HTTPS]</size>//
merchant_portal - ->> banking_system : **Performs fund transfers through**\n//<size:12>[API]</size>//

card_issuer - ->> card_management : **Manages credit card information and accounts through**\n//<size:12>[HTTPS]</size>//
card_management - ->> fraud_management : **Shares data and rules for fraud prevention through**\n//<size:12>[HTTPS]</size>//

@enduml

PlantUML version 1.2022.7(Mon Aug 22 22:31:30 IST 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: Cp1252
Language: en
Country: US
--></g></svg>