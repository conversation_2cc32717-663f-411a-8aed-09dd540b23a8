<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="831px" preserveAspectRatio="none" style="width:2142px;height:831px;background:#FFFFFF;" version="1.1" viewBox="0 0 2142 831" width="2142px" zoomAndPan="magnify"><defs/><g><!--MD5=[e5810817e344d1c164fb893e42a3bc14]
cluster credit_card_approval_boundary--><g id="cluster_credit_card_approval_boundary"><rect fill="none" height="599" rx="2.5" ry="2.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="2129" x="7" y="226"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="301" x="921" y="264.6094">Credit Card Approval System Boundary</text><text fill="#444444" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="51" x="1046" y="280.582">[System]</text></g><!--MD5=[20a8749ae1de51d7870c48c0ef27ed51]
cluster credit_card_approval_system--><g id="cluster_credit_card_approval_system"><rect fill="#438DD5" height="493" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:1.0;" width="2081" x="31" y="308"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1039" y="322.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="221" x="961" y="341.7031">Credit Card Approval System</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1054" y="357.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="4" x="1069.5" y="374.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="347" x="898" y="392.4551">Processes credit card applications and approvals</text></g><!--MD5=[e68d03d6c58bd21614e180f0313a75cd]
entity application_service--><g id="elem_application_service"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="218" x="970" y="412"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1046.5" y="434.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="147" x="1005.5" y="453.7031">Application Service</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1063" y="469.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1077" y="486.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="194" x="980" y="504.4551">Handles credit card application</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="993.5" y="522.0645">submission and processing</text></g><!--MD5=[dd4aa979f0c0900684652f3f173d98c8]
entity credit_bureau_integration--><g id="elem_credit_bureau_integration"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="212" x="47" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="120.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="192" x="57" y="693.7031">Credit Bureau Integration</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="137" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="151" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="183" x="59.5" y="744.4551">Integration with credit bureau</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="52" x="127" y="762.0645">services</text></g><!--MD5=[db10d95f6d5cf22413b6ed668fbbdbc6]
entity fraud_detection--><g id="elem_fraud_detection"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="200" x="294" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="361.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="122" x="333" y="693.7031">Fraud Detection</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="44" x="372" y="709.6758">[Python]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="392" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="176" x="304" y="744.4551">Performs fraud detection on</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="146" x="321" y="762.0645">credit card applications</text></g><!--MD5=[b143f494c9bf01c18d42d266d61f7ff6]
entity external_services--><g id="elem_external_services"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="173" x="529.5" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="583.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="133" x="549.5" y="693.7031">External Services</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="603" y="709.6758">[N/A]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="614" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="149" x="539.5" y="744.4551">Integration with external</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="144" x="544" y="762.0645">systems for verification</text></g><!--MD5=[a6767aa223d976129dd4a90a4e46aaa2]
entity payment_gateway_integration--><g id="elem_payment_gateway_integration"><rect fill="#438DD5" height="143.2656" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="219" x="737.5" y="642"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="814.5" y="664.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="137" x="776.5" y="683.7031">Payment Gateway</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="82" x="806" y="703.8281">Integration</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="831" y="719.8008">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="845" y="736.9707"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="768.5" y="754.5801">Integration with payment</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="199" x="747.5" y="772.1895">gateway for initial card payment</text></g><!--MD5=[ff673434935b7081903a1bd6bf9d64b7]
entity card_issuance--><g id="elem_card_issuance"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="175" x="991.5" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1046.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="110" x="1024" y="693.7031">Card Issuance</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1063" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1077" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="1001.5" y="744.4551">Handles the issuance of</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="138" x="1010" y="762.0645">approved credit cards</text></g><!--MD5=[953b76fabfee03198493bc256275b77a]
entity limit_component--><g id="elem_limit_component"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="177" x="1201.5" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1257.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="131" x="1224.5" y="693.7031">Limit Component</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1274" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1288" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="1211.5" y="744.4551">Manages credit limits for</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="139" x="1220.5" y="762.0645">approved applications</text></g><!--MD5=[353f3c0900bf5ead58a01dcf8dc9b765]
entity consumer_component--><g id="elem_consumer_component"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="218" x="1414" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1490.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="174" x="1436" y="693.7031">Consumer Component</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1507" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1521" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="194" x="1424" y="744.4551">Manages customer information</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="101" x="1472.5" y="762.0645">and interactions</text></g><!--MD5=[34b64a70c8caf3d5136a1e1fdec34cd2]
entity document_component--><g id="elem_document_component"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="193" x="1667.5" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1731.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="173" x="1677.5" y="693.7031">Document Component</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1748" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1762" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="130" x="1697" y="744.4551">Manages application</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="69" x="1729.5" y="762.0645">documents</text></g><!--MD5=[dd0e937938cc67db12a485ae7369c4a3]
entity application_component--><g id="elem_application_component"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="200" x="1896" y="652"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="1963.5" y="674.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="180" x="1906" y="693.7031">Application Component</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="1980" y="709.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1994" y="726.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="1925.5" y="744.4551">Processes credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="75" x="1958.5" y="762.0645">applications</text></g><!--MD5=[92848cbaf65985a669855153c5362c37]
entity customer--><g id="elem_customer"><rect fill="#08427B" height="156.0469" rx="2.5" ry="2.5" style="stroke:#073B6F;stroke-width:0.5;" width="210" x="974" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="52" x="1053" y="29.457">«person»</text><image height="48" width="48" x="1055" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACbklEQVR4Xu1Wq04FMRS8cj+BT+AXsDgMHwAfQILGXUWQgMQQggRDgsBgSLiWgEIREhRIEAjsMum5dLtzetreXgKE7GTUbh/TOY921CyP/xRH+tPvchCU4yAox0FQjt8gaHF9b2XreGnjYGF1R/+dlfWCsP3uyeTt/aMNcPfwDHF6cDkrBcEPkhLi6OJGTylkjSDEKKFGUK2pRhDiwvvHUBe7iKDN/fOnl1esCBuQJfQX2/DOBqBbz726fWzdymfX93Ba786CYHV6XT0ggbDu1rZP6S9kaU09QUhVmiMIfZIjFgILyiwo438O2sWeIOv0iKAfU5hAAp9G2h4P6l49QYgrD3eAt9kxUfiIIC/53xcoaj1B48NLHu4QGptYmhAeI1EKoQAWZEUahmfHaFCFSuUSdLviKtNH0ZVvGRkC9lByIDSkCfVBKzdaUOM8wJbQDim+TIjZTLImIuJYGbTaZkRQIS2fkHC6u5STBcEeLIfzeSYeFfiFE8MtiID/iXMLaeWo7k4QVreaHmJvhaCQiD4v6oBUw5HCkZ2gaBUIMC199CzTORdqmgqyEqJ19vioYSZclLeHXL1hTBECNAhvMyYiiCW9sQ260VSQdSH46tVFWw7fOBLH9hGYCrI2k9SBGv4xIxAy2chKU997U4J848q+D0sgiWI9KFhQNGRiY8LnmeCvtsRenaBoxiXMq4PYoFtA+Lzpyh5mwgxPcbj8Ki2BXKXISNoo7L3cqYnzp3OI6G1KzAiycrAO/1EQyKvOAf200swLsl7+FYhe78S8oMa1IlQmGkk1EawSNU2hoJ/kICjHQVCOg6AcB0E5fgLPSUakWeua4wAAAABJRU5ErkJggg==" y="32.0938"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="75" x="1041.5" y="96.7031">Customer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="1077" y="114.752"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="186" x="984" y="132.3613">A person applying for a credit</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="28" x="1065" y="149.9707">card</text></g><!--MD5=[0ac8625fba8d05be0823d1303d3e0a6f]
link customer to application_service--><g id="link_customer_application_service"><path d="M1079,163.29 C1079,234.57 1079,339.25 1079,406.65 " fill="none" id="customer-to-application_service" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1079,411.84,1083,402.84,1079,406.84,1075,402.84,1079,411.84" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="128" x="1080" y="206.457">Applies for a credit card</text></g><!--MD5=[23e4c43898810280200b1d3c600fb3a4]
link application_service to credit_bureau_integration--><g id="link_application_service_credit_bureau_integration"><path d="M969.8,480.03 C790.31,490.4 441.6,516.22 329,565 C285.06,584.04 243.61,617.68 211.98,648.04 " fill="none" id="application_service-to-credit_bureau_integration" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="208.17,651.72,217.4184,648.3338,211.7623,648.2422,211.8539,642.5861,208.17,651.72" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="146" x="330" y="585.957">Requests credit report and</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="59" x="375" y="601.0508">score from</text></g><!--MD5=[b131120c8ab90f66860e3729b150c632]
link application_service to fraud_detection--><g id="link_application_service_fraud_detection"><path d="M969.89,476.19 C860.83,481.76 690.86,501.16 559,565 C517.79,584.95 479.2,618.28 449.71,648.22 " fill="none" id="application_service-to-fraud_detection" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="446.16,651.85,455.3162,648.2218,449.6596,648.2789,449.6025,642.6224,446.16,651.85" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="135" x="560" y="585.957">Performs fraud detection</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="77" x="590.5" y="601.0508">on application</text></g><!--MD5=[35250bc41f89b1397bed7f20799e21a0]
link application_service to external_services--><g id="link_application_service_external_services"><path d="M969.77,492.47 C907.36,506 829.36,528.63 767,565 C730.19,586.47 695.67,618.87 668.95,647.83 " fill="none" id="application_service-to-external_services" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="665.4,651.71,674.4343,647.788,668.7825,648.0278,668.5427,642.376,665.4,651.71" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="129" x="768" y="585.957">Integration with external</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="125" x="771.5" y="601.0508">systems for verification</text></g><!--MD5=[155b9a6da45720b05d4db477dfbaa039]
link application_service to payment_gateway_integration--><g id="link_application_service_payment_gateway_integration"><path d="M981.56,535.01 C969.35,544.37 957.48,554.46 947,565 C925.76,586.36 906.2,612.8 890.13,637.38 " fill="none" id="application_service-to-payment_gateway_integration" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="887.38,641.63,895.6245,636.2422,890.0939,637.4306,888.9055,631.9,887.38,641.63" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="119" x="948" y="585.957">Processes initial card</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="92" x="963" y="601.0508">payment through</text></g><!--MD5=[57672091162882a323204122ac75472d]
link application_service to card_issuance--><g id="link_application_service_card_issuance"><path d="M1079,535.17 C1079,569.3 1079,611.94 1079,646.84 " fill="none" id="application_service-to-card_issuance" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1079,651.94,1083,642.94,1079,646.94,1075,642.94,1079,651.94" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="125" x="1080" y="585.957">Issues approved credit</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="76" x="1106" y="601.0508">cards through</text></g><!--MD5=[f743c80e7da61720a787259ca264fa96]
link application_service to limit_component--><g id="link_application_service_limit_component"><path d="M1184.81,535.16 C1196.45,544.29 1207.5,554.28 1217,565 C1238.04,588.76 1254.57,619.66 1266.54,647.05 " fill="none" id="application_service-to-limit_component" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1268.57,651.76,1268.7013,641.912,1266.6004,647.1643,1261.3481,645.0634,1268.57,651.76" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="134" x="1248" y="585.957">Manages credit limits for</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="121" x="1256" y="601.0508">approved applications</text></g><!--MD5=[942630acc59c514d4039cea96afb6c6b]
link application_service to consumer_component--><g id="link_application_service_consumer_component"><path d="M1188.17,490.01 C1251.93,502.79 1331.81,525.49 1394,565 C1427.39,586.21 1457.06,618.59 1479.5,647.61 " fill="none" id="application_service-to-consumer_component" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1482.76,651.86,1480.4614,642.2831,1479.719,647.891,1474.1111,647.1487,1482.76,651.86" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="106" x="1448" y="578.457">Manages customer</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="86" x="1458" y="593.5508">information and</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="64" x="1470.5" y="608.6445">interactions</text></g><!--MD5=[6a8782083086c5415a645424498f0f97]
link application_service to document_component--><g id="link_application_service_document_component"><path d="M1188.09,480.59 C1289.61,489.49 1443.27,511.37 1566,565 C1612.8,585.45 1658.93,618.82 1694.88,648.65 " fill="none" id="application_service-to-document_component" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1698.77,651.9,1694.4135,643.067,1694.9277,648.7005,1689.2943,649.2147,1698.77,651.9" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="114" x="1643" y="585.957">Manages application</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="62" x="1670.5" y="601.0508">documents</text></g><!--MD5=[dbc251711232f9f3866f2633b9f5955f]
link application_service to application_component--><g id="link_application_service_application_component"><path d="M1188.02,474.07 C1327.09,476.8 1573.04,492.73 1769,565 C1822.18,584.61 1875.64,618.48 1917.31,648.83 " fill="none" id="application_service-to-application_component" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1921.56,651.94,1916.6573,643.3981,1917.5244,648.9881,1911.9343,649.8552,1921.56,651.94" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="120" x="1862" y="585.957">Processes credit card</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="67" x="1890" y="601.0508">applications</text></g><!--MD5=[cad9f217fe94bc545fd2da2775db8693]
@startuml C4_Container_Diagram

!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5

!define ICONSIZE 16

!include DEVICONS/java.puml
!include FONTAWESOME/users.puml

System_Boundary(credit_card_approval_boundary, "Credit Card Approval System Boundary") {
    Container(credit_card_approval_system, "Credit Card Approval System", "Java", "Processes credit card applications and approvals") {
        Container(application_service, "Application Service", "Java", "Handles credit card application submission and processing")
        Container(credit_bureau_integration, "Credit Bureau Integration", "Java", "Integration with credit bureau services")
        Container(fraud_detection, "Fraud Detection", "Python", "Performs fraud detection on credit card applications")
        Container(external_services, "External Services", "N/A", "Integration with external systems for verification")
        Container(payment_gateway_integration, "Payment Gateway Integration", "Java", "Integration with payment gateway for initial card payment")
        Container(card_issuance, "Card Issuance", "Java", "Handles the issuance of approved credit cards")
        
        Container(limit_component, "Limit Component", "Java", "Manages credit limits for approved applications")
        Container(consumer_component, "Consumer Component", "Java", "Manages customer information and interactions")
        Container(document_component, "Document Component", "Java", "Manages application documents")
        Container(application_component, "Application Component", "Java", "Processes credit card applications")
    }
}

Person(customer, "Customer", "A person applying for a credit card", $sprite="users")

customer - -> application_service : "Applies for a credit card"
application_service - -> credit_bureau_integration : "Requests credit report and score from"
application_service - -> fraud_detection : "Performs fraud detection on application"
application_service - -> external_services : "Integration with external systems for verification"
application_service - -> payment_gateway_integration : "Processes initial card payment through"
application_service - -> card_issuance : "Issues approved credit cards through"

application_service - -> limit_component : "Manages credit limits for approved applications"
application_service - -> consumer_component : "Manages customer information and interactions"
application_service - -> document_component : "Manages application documents"
application_service - -> application_component : "Processes credit card applications"

@enduml

@startuml C4_Container_Diagram























skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendFontColor #FFFFFF
skinparam LegendBackgroundColor transparent
skinparam LegendBorderColor transparent

skinparam rectangle<<legendArea>> {
    backgroundcolor transparent
    bordercolor transparent
}

skinparam rectangle {
    StereotypeFontSize 12
}

skinparam database {
    StereotypeFontSize 12
}

skinparam queue {
    StereotypeFontSize 12
}

skinparam participant {
    StereotypeFontSize 12
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam person {
    StereotypeFontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    style awesome
}

skinparam rectangle<<boundary>> {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    BorderStyle dashed
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}


























































































skinparam rectangle<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<boundary>>StereotypeFontColor transparent
skinparam rectangle<<boundary>>StereotypeFontColor transparent

skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent




































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}

skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}

skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam participant<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam sequencebox<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}

skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam participant<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent






sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}

sprite $robot [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000005BFFFFFFFFFFFFFFFFFFFFFE9100000000000
0000000000AFFFFFFFFFFFFFFFFFFFFFFFFFE30000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000699405FFFFFFC427FFFFFFFFFC427FFFFFFE009982000
008FFF705FFFFFE10006FFFFFFFE00007FFFFFE00FFFF100
00CFFF705FFFFFA00001FFFFFFF900002FFFFFE00FFFF500
00DFFF705FFFFFB00002FFFFFFFA00003FFFFFE00FFFF500
00DFFF705FFFFFF4000AFFFFFFFF3000BFFFFFE00FFFF500
00DFFF705FFFFFFFA8DFFFFFFFFFFA8DFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00CFFF705FFFFFF87777777777777777CFFFFFE00FFFF500
008FFF705FFFFFF100000000000000009FFFFFE00FFFF100
000699405FFFFFF76666666666666666CFFFFFE009982000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000000EFFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFD0000000000
00000000004CFFFFFFFFFFFFFFFFFFFFFFFF910000000000
000000000000011111111111111111111110000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $robot2 [48x48/16] {
000000000000000088888888888888880000000000000000
000000000000000AFFFFFFFFFFFFFFFFA000000000000000
00000000000000CFFFFFFFFFFFFFFFFFFC00000000000000
00000000000004EFFFFFFFFFFFFFFFFFFE40000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFFA0000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000004CFFFFFFFFFFFFFFFFFFC40000000000000
000000488888848CFFFFFFFFFFFFFFFFC848888884000000
00000CFFFFFFFFC888888888888888888CFFFFFFFFC00000
00008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80000
0000CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0000CFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFC0000
00008FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF80000
00000CFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFC00000
000000488887578888888888888888888864688884000000
000000000000000000000000000000000000000000000000
}




skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam participant<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam sequencebox<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}

skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam participant<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam sequencebox<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent



















sprite $java [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000120000000000000000000
000000000000000000000000000080000000000000000000
0000000000000000000000000001B0000000000000000000
0000000000000000000000000005A0000000000000000000
000000000000000000000000000C70000000000000000000
000000000000000000000000006F10000000000000000000
00000000000000000000000005F700000000000000000000
0000000000000000000000006FB000000000000000000000
000000000000000000000009FB0001650000000000000000
0000000000000000000001CFA0018C200000000000000000
000000000000000000002DF9005E90000000000000000000
00000000000000000001DF8008F700000000000000000000
0000000000000000000BFB005FA000000000000000000000
0000000000000000001FF200CF3000000000000000000000
0000000000000000001FD000FF4000000000000000000000
0000000000000000000DE000EFB000000000000000000000
00000000000000000005F2008FF600000000000000000000
00000000000000000000AA001EFE00000000000000000000
000000000000000000000B4004FF40000000000000000000
0000000000000000000000A100BF20000000000000000000
00000000000000000000000400A900000011000000000000
00000000000000016730000001800000046CB10000000000
00000000000005BD30000000010000120000DC0000000000
0000000000001DFEA7765567889BCB7000008F1000000000
000000000000001468899988753200000000BE0000000000
000000000000000001000000000000000004F50000000000
0000000000000000C800000000024200004E500000000000
0000000000000000CFFDCBBBCEFFFC201881000000000000
00000000000000000268AAAAA86400003000000000000000
000000000000000000000000000000000000000000000000
000000000000000009A10000002410000000000000000000
00000000000000000CFFFEDDEFFFF6000000000000000000
00000000000003520049CEEFFCA720000000000000000000
000000000019B50000000000000000000002400000000000
0000000002FF820000000000000000000378000000000000
00000000018DFFDB9766555566789ACCA610160000000000
00000000000001357899AABAA9875310026AA10000000000
0000000000000003542211111233579BCB72000000000000
000000000000000002457778888765310000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV JAVA>> White
sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White

  rectangle "== Credit Card Approval System Boundary\n<size:12>[System]</size>" <<system_boundary>><<boundary>> as credit_card_approval_boundary  {
      rectangle "== Credit Card Approval System\n//<size:12>[Java]</size>//\n\nProcesses credit card applications and approvals" <<container>> as credit_card_approval_system {
          rectangle "== Application Service\n//<size:12>[Java]</size>//\n\nHandles credit card application submission and processing" <<container>> as application_service
          rectangle "== Credit Bureau Integration\n//<size:12>[Java]</size>//\n\nIntegration with credit bureau services" <<container>> as credit_bureau_integration
          rectangle "== Fraud Detection\n//<size:12>[Python]</size>//\n\nPerforms fraud detection on credit card applications" <<container>> as fraud_detection
          rectangle "== External Services\n//<size:12>[N/A]</size>//\n\nIntegration with external systems for verification" <<container>> as external_services
          rectangle "== Payment Gateway Integration\n//<size:12>[Java]</size>//\n\nIntegration with payment gateway for initial card payment" <<container>> as payment_gateway_integration
          rectangle "== Card Issuance\n//<size:12>[Java]</size>//\n\nHandles the issuance of approved credit cards" <<container>> as card_issuance
        
          rectangle "== Limit Component\n//<size:12>[Java]</size>//\n\nManages credit limits for approved applications" <<container>> as limit_component
          rectangle "== Consumer Component\n//<size:12>[Java]</size>//\n\nManages customer information and interactions" <<container>> as consumer_component
          rectangle "== Document Component\n//<size:12>[Java]</size>//\n\nManages application documents" <<container>> as document_component
          rectangle "== Application Component\n//<size:12>[Java]</size>//\n\nProcesses credit card applications" <<container>> as application_component
    }
}

rectangle "<$users>\n== Customer\n\nA person applying for a credit card" <<person>> as customer 

customer - -> application_service : "Applies for a credit card"
application_service - -> credit_bureau_integration : "Requests credit report and score from"
application_service - -> fraud_detection : "Performs fraud detection on application"
application_service - -> external_services : "Integration with external systems for verification"
application_service - -> payment_gateway_integration : "Processes initial card payment through"
application_service - -> card_issuance : "Issues approved credit cards through"

application_service - -> limit_component : "Manages credit limits for approved applications"
application_service - -> consumer_component : "Manages customer information and interactions"
application_service - -> document_component : "Manages application documents"
application_service - -> application_component : "Processes credit card applications"

@enduml

PlantUML version 1.2022.7(Mon Aug 22 22:31:30 IST 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: Cp1252
Language: en
Country: US
--></g></svg>