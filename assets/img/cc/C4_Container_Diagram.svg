<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1503px" preserveAspectRatio="none" style="width:1341px;height:1503px;background:#FFFFFF;" version="1.1" viewBox="0 0 1341 1503" width="1341px" zoomAndPan="magnify"><defs/><g><!--MD5=[ed91d809a2bfc4ff332efca66c1c2e6f]
cluster credit_card_system_boundary--><g id="cluster_credit_card_system_boundary"><rect fill="none" height="1490.72" rx="2.5" ry="2.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="716" x="7" y="7"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="228" x="251" y="45.6094">Credit Card System Boundary</text><text fill="#444444" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="51" x="339.5" y="61.582">[System]</text></g><!--MD5=[4e7a2dab3a9311f8279174587056795e]
cluster customer_boundary--><g id="cluster_customer_boundary"><rect fill="none" height="461" rx="2.5" ry="2.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="580" x="755" y="166.72"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="155" x="967.5" y="205.3294">Customer Boundary</text><text fill="#444444" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="51" x="1019.5" y="221.302">[System]</text></g><!--MD5=[9081ad7617db48ffe3320d057f8988e9]
entity credit_card_system--><g id="elem_credit_card_system"><rect fill="#438DD5" height="85.4063" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="216" x="481" y="276.22"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="556.5" y="298.677">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="148" x="515" y="317.9231">Credit Card System</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="193" x="491" y="333.8958">[A system for processing credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="69" x="554.5" y="348.9895">transactions]</text></g><!--MD5=[e40dd0b90ebdfcc16fe97bf774d4cfaf]
entity payment_gateway--><g id="elem_payment_gateway"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="161" x="35.5" y="257.22"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="83.5" y="279.677">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="137" x="47.5" y="298.9231">Payment Gateway</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="100" y="314.8958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="114" y="332.0657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="45.5" y="349.6751">Processes credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="61" x="85.5" y="367.2845">payments</text></g><!--MD5=[f3de39f3d10df5d2308a21770693a92f]
entity acquirer--><g id="elem_acquirer"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="172" x="51" y="920.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="104.5" y="943.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="65" x="104.5" y="962.4231">Acquirer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="121" y="978.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="135" y="995.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="66.5" y="1013.1751">Processes credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="152" x="61" y="1030.7845">payments for merchants</text></g><!--MD5=[91373802ae16a2c8f466f61c38c7d825]
entity card_network--><g id="elem_card_network"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="207" x="257.5" y="705.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="328.5" y="728.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="105" x="308.5" y="747.4231">Card Network</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="348" y="763.3958">[N/A]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="359" y="780.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="183" x="267.5" y="798.1751">Routes transactions between</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="136" x="293" y="815.7845">issuers and acquirers</text></g><!--MD5=[bb115c575b04a96deff8efcb0782c50a]
entity processor--><g id="elem_processor"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="220" x="254" y="488.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="331.5" y="511.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="79" x="324.5" y="530.4231">Processor</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="351" y="546.3958">[N/A]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="362" y="563.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="196" x="264" y="581.1751">Handles credit card transaction</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="69" x="329.5" y="598.7845">processing</text></g><!--MD5=[db10d95f6d5cf22413b6ed668fbbdbc6]
entity fraud_detection--><g id="elem_fraud_detection"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="211" x="31.5" y="1135.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="104.5" y="1158.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="122" x="76" y="1177.4231">Fraud Detection</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="44" x="115" y="1193.3958">[Python]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="135" y="1210.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="187" x="41.5" y="1228.1751">Detects fraudulent credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="77" x="98.5" y="1245.7845">transactions</text></g><!--MD5=[3938681022872452b9357901721a7bd2]
entity customer_service--><g id="elem_customer_service"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="204" x="259" y="920.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="328.5" y="943.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="136" x="293" y="962.4231">Customer Service</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="345" y="978.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="359" y="995.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="180" x="269" y="1013.1751">Assists customers with credit</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="157" x="282.5" y="1030.7845">card issues and inquiries</text></g><!--MD5=[3ceb0bfda414806be52a6d87497d4f2b]
entity merchant_portal--><g id="elem_merchant_portal"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="196" x="278" y="1135.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="343.5" y="1158.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="120" x="316" y="1177.4231">Merchant Portal</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="49" x="351.5" y="1193.3958">[Angular]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="374" y="1210.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="172" x="288" y="1228.1751">Provides merchant account</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="171" x="290.5" y="1245.7845">management and reporting</text></g><!--MD5=[bb652fc1ce3da2060c9c3c7211dc4a65]
entity reporting--><g id="elem_reporting"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="162" x="189" y="1350.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="237.5" y="1373.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="76" x="232" y="1392.4231">Reporting</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="254" y="1408.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="268" y="1425.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="138" x="199" y="1443.1751">Generates credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="119" x="210.5" y="1460.7845">transaction reports</text></g><!--MD5=[6c7a3b43a568b319c0b7c06f28312f5c]
entity banking_system--><g id="elem_banking_system"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="220" x="386" y="1350.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="463.5" y="1373.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="125" x="433.5" y="1392.4231">Banking System</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="26" x="483" y="1408.3958">[N/A]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="494" y="1425.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="200" x="396" y="1443.1751">Handles fund transfers between</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="57" x="467.5" y="1460.7845">accounts</text></g><!--MD5=[263153da100e84bd2cc2792515088f1a]
entity card_management--><g id="elem_card_management"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="211" x="233.5" y="257.22"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="306.5" y="279.677">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="139" x="269.5" y="298.9231">Card Management</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="323" y="314.8958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="337" y="332.0657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="129" x="272.5" y="349.6751">Manages credit card</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="191" x="243.5" y="367.2845">information and user accounts</text></g><!--MD5=[f661119d5f460df205a9fe050762bf11]
entity fraud_management--><g id="elem_fraud_management"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="190" x="509" y="488.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="571.5" y="511.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="147" x="530.5" y="530.4231">Fraud Management</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="588" y="546.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="602" y="563.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="166" x="519" y="581.1751">Manages fraud prevention</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="149" x="529.5" y="598.7845">rules and investigations</text></g><!--MD5=[92848cbaf65985a669855153c5362c37]
entity customer--><g id="elem_customer"><rect fill="#08427B" height="156.0469" rx="2.5" ry="2.5" style="stroke:#073B6F;stroke-width:0.5;" width="218" x="870" y="240.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="52" x="953" y="263.177">«person»</text><image height="48" width="48" x="955" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACbklEQVR4Xu1Wq04FMRS8cj+BT+AXsDgMHwAfQILGXUWQgMQQggRDgsBgSLiWgEIREhRIEAjsMum5dLtzetreXgKE7GTUbh/TOY921CyP/xRH+tPvchCU4yAox0FQjt8gaHF9b2XreGnjYGF1R/+dlfWCsP3uyeTt/aMNcPfwDHF6cDkrBcEPkhLi6OJGTylkjSDEKKFGUK2pRhDiwvvHUBe7iKDN/fOnl1esCBuQJfQX2/DOBqBbz726fWzdymfX93Ba786CYHV6XT0ggbDu1rZP6S9kaU09QUhVmiMIfZIjFgILyiwo438O2sWeIOv0iKAfU5hAAp9G2h4P6l49QYgrD3eAt9kxUfiIIC/53xcoaj1B48NLHu4QGptYmhAeI1EKoQAWZEUahmfHaFCFSuUSdLviKtNH0ZVvGRkC9lByIDSkCfVBKzdaUOM8wJbQDim+TIjZTLImIuJYGbTaZkRQIS2fkHC6u5STBcEeLIfzeSYeFfiFE8MtiID/iXMLaeWo7k4QVreaHmJvhaCQiD4v6oBUw5HCkZ2gaBUIMC199CzTORdqmgqyEqJ19vioYSZclLeHXL1hTBECNAhvMyYiiCW9sQ260VSQdSH46tVFWw7fOBLH9hGYCrI2k9SBGv4xIxAy2chKU997U4J848q+D0sgiWI9KFhQNGRiY8LnmeCvtsRenaBoxiXMq4PYoFtA+Lzpyh5mwgxPcbj8Ki2BXKXISNoo7L3cqYnzp3OI6G1KzAiycrAO/1EQyKvOAf200swLsl7+FYhe78S8oMa1IlQmGkk1EawSNU2hoJ/kICjHQVCOg6AcB0E5fgLPSUakWeua4wAAAABJRU5ErkJggg==" y="265.8138"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="75" x="941.5" y="330.4231">Customer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="977" y="348.472"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="198" x="880" y="366.0813">A person who uses credit cards</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="120" x="919" y="383.6907">to make purchases</text></g><!--MD5=[bad502f8fb138742b9948c2e5b3f55ae]
entity card_issuer--><g id="elem_card_issuer"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="158" x="835" y="488.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="881.5" y="511.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="88" x="870" y="530.4231">Card Issuer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="898" y="546.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="912" y="563.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="134" x="845" y="581.1751">Issues credit cards to</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="65" x="881.5" y="598.7845">customers</text></g><!--MD5=[5973eb14a0726e1029999c7665bf0e5a]
link customer to card_issuer--><g id="link_customer_card_issuer"><path d="M869.64,362.87 C843.17,378.8 818.16,399.76 803,426.72 C787.71,453.91 805.41,480.09 830.84,501.22 " fill="none" id="customer-to-card_issuer" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="834.99,504.57,830.5079,495.8001,831.1024,501.4257,825.4769,502.0202,834.99,504.57" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="128" x="804" y="440.177">Applies for a credit card</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="22" x="858.5" y="455.2708">with</text></g><!--MD5=[5973eb14a0726e1029999c7665bf0e5a]
link customer to card_issuer--><g id="link_customer_card_issuer"><path d="M957.43,396.94 C951.78,417.06 945.68,438.71 940,458.72 C937.74,466.66 935.37,475 933,483.26 " fill="none" id="customer-to-card_issuer-1" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="931.53,488.38,937.8486,480.8252,932.9036,483.5724,930.1564,478.6274,931.53,488.38" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="149" x="949" y="447.677">Disputes a transaction with</text></g><!--MD5=[5973eb14a0726e1029999c7665bf0e5a]
link customer to card_issuer--><g id="link_customer_card_issuer"><path d="M1088.14,385.35 C1110.81,407.31 1123.45,432.83 1107,458.72 C1082.96,496.54 1038.49,518.83 998.29,531.82 " fill="none" id="customer-to-card_issuer-2" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="993.41,533.35,1003.1983,534.4401,998.1758,531.8375,1000.7784,526.8149,993.41,533.35" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="147" x="1115" y="447.677">Requests a charge-off with</text></g><!--MD5=[ad1f870ab926c366944d010bf1728064]
link credit_card_system_boundary to payment_gateway--><g id="link_credit_card_system_boundary_payment_gateway"><path d="M226.9,73.75 C224.88,74.32 192.27,83.8 177,103.72 C144.18,146.53 129.02,206.3 122.01,251.79 " fill="none" id="credit_card_system_boundary-to-payment_gateway" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="121.24,256.99,126.5296,248.6822,121.981,252.0452,118.618,247.4966,121.24,256.99" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="94" x="178" y="132.177">Purchases using</text></g><!--MD5=[5432c19f5f583ac867fc690334cccd34]
link payment_gateway to card_network--><g id="link_payment_gateway_card_network"><path d="M95.89,380.49 C76.74,451.09 59.09,566.69 121,635.72 C130.57,646.39 171.62,638.59 185,643.72 C220.95,657.51 256.57,680.14 286.27,702.23 " fill="none" id="payment_gateway-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="290.61,705.49,285.81,696.89,286.61,702.49,281.01,703.29,290.61,705.49" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="111" x="122" y="547.677">Routes transactions</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="42" x="158" y="562.7708">through</text></g><!--MD5=[3c32576c8eb21b02f65e6439ff8778ae]
link payment_gateway to acquirer--><g id="link_payment_gateway_acquirer"><path d="M87.78,380.32 C81.94,395.2 76.5,411.3 73,426.72 C48.46,534.81 45.18,565.74 59,675.72 C69.59,759.98 95.99,854.31 115.15,915.56 " fill="none" id="payment_gateway-to-acquirer" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="116.75,920.65,117.8668,910.8647,115.2505,915.8801,110.235,913.2639,116.75,920.65" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="116" x="60" y="664.677">Transfers payment to</text></g><!--MD5=[bc6993c6931dcf182d6d8e7f590128b9]
link payment_gateway to processor--><g id="link_payment_gateway_processor"><path d="M180.78,380.41 C206.88,404.81 237.32,433.16 265,458.72 C274.29,467.3 284.09,476.31 293.77,485.18 " fill="none" id="payment_gateway-to-processor" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="297.62,488.71,293.679,479.684,293.9307,485.3353,288.2794,485.5869,297.62,488.71" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="131" x="266" y="440.177">Processes transactions</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="31" x="317.5" y="455.2708">using</text></g><!--MD5=[d15097f246e03204b0e6d6c0950cf047]
link card_issuer to card_network--><g id="link_card_issuer_card_network"><path d="M834.96,585.82 C798.59,601.47 754.78,619.95 715,635.72 C633.24,668.13 539.85,702.44 469.53,727.75 " fill="none" id="card_issuer-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="464.51,729.55,474.3332,730.2598,469.2135,727.8539,471.6195,722.7342,464.51,729.55" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="90" x="687" y="657.177">Routes payment</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="73" x="697" y="672.2708">messages to</text></g><!--MD5=[72e67d6310dfd332766db8393494fbdd]
link card_network to acquirer--><g id="link_card_network_acquirer"><path d="M290.59,828.9 C279.57,838.74 268.39,848.92 258,858.72 C238.43,877.18 217.66,897.8 198.96,916.78 " fill="none" id="card_network-to-acquirer" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="195.25,920.56,204.4136,916.9505,198.7569,916.9961,198.7113,911.3394,195.25,920.56" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="90" x="259" y="872.177">Routes payment</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="73" x="269" y="887.2708">messages to</text></g><!--MD5=[4bf2d5a608137bd884256bb114328dfe]
link card_network to customer_service--><g id="link_card_network_customer_service"><path d="M361,828.97 C361,855.94 361,887.75 361,915.3 " fill="none" id="card_network-to-customer_service" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="361,920.39,365,911.39,361,915.39,357,911.39,361,920.39" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="104" x="362" y="879.677">Routes inquiries to</text></g><!--MD5=[88340201b1355d9e9605e8cfb15cef87]
link acquirer to fraud_detection--><g id="link_acquirer_fraud_detection"><path d="M137,1043.97 C137,1070.94 137,1102.75 137,1130.3 " fill="none" id="acquirer-to-fraud_detection" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="137,1135.39,141,1126.39,137,1130.39,133,1126.39,137,1135.39" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="140" x="138" y="1094.677">Sends transaction data to</text></g><!--MD5=[516c3367bb13590286a64fc742c0ed5b]
link processor to card_network--><g id="link_processor_card_network"><path d="M363.15,611.97 C362.77,639.56 362.31,672.25 361.92,700.42 " fill="none" id="processor-to-card_network" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="361.85,705.62,365.9782,696.6781,361.9214,700.6205,357.979,696.5638,361.85,705.62" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="102" x="367" y="657.177">Routes processed</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="113" x="363" y="672.2708">transactions through</text></g><!--MD5=[4553fbe1c33e4ae680c0a1f1f03298a3]
link acquirer to merchant_portal--><g id="link_acquirer_merchant_portal"><path d="M223.37,1033.42 C253.31,1051.19 280.95,1068.17 287,1073.72 C305.18,1090.41 322,1111.34 336.01,1131.15 " fill="none" id="acquirer-to-merchant_portal" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="339.14,1135.62,337.2535,1125.9535,336.2717,1131.5245,330.7007,1130.5427,339.14,1135.62" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="132" x="316" y="1087.177">Manages payments and</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="113" x="327" y="1102.2708">transactions through</text></g><!--MD5=[3abfd2318ec91d4b63297bea3e0061b0]
link merchant_portal to reporting--><g id="link_merchant_portal_reporting"><path d="M316.12,1258.79 C308.88,1268.28 302.21,1278.39 297,1288.72 C288.21,1306.14 282.27,1326.36 278.25,1345.3 " fill="none" id="merchant_portal-to-reporting" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="277.17,1350.61,282.8889,1342.5916,278.1698,1345.711,275.0504,1340.9919,277.17,1350.61" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="122" x="298" y="1302.177">Generates transaction</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="73" x="324" y="1317.2708">reports using</text></g><!--MD5=[c51e7bb39cdc20b82da477b573aaf1a2]
link merchant_portal to banking_system--><g id="link_merchant_portal_banking_system"><path d="M411.09,1259 C416.77,1268.92 422.56,1279.1 428,1288.72 C438.48,1307.27 449.74,1327.46 460.03,1346.01 " fill="none" id="merchant_portal-to-banking_system" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="462.63,1350.68,461.7558,1340.87,460.2018,1346.3092,454.7626,1344.7552,462.63,1350.68" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="130" x="445" y="1302.177">Performs fund transfers</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="42" x="490.5" y="1317.2708">through</text></g><!--MD5=[525942f1265535e75fc51104c5e23f43]
link credit_card_system_boundary to card_management--><g id="link_credit_card_system_boundary_card_management"><path d="M227.1,73.75 C229.12,74.32 261.68,83.83 277,103.72 C309.95,146.51 325.4,206.28 332.64,251.78 " fill="none" id="credit_card_system_boundary-to-card_management" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="333.44,256.98,336.0312,247.4781,332.6829,252.0376,328.1234,248.6894,333.44,256.98" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="111" x="319" y="117.177">Manages credit card</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="139" x="305" y="132.2708">information and accounts</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="42" x="355" y="147.3645">through</text></g><!--MD5=[e28064bc39b0b4572eb3c7c8c5ff813e]
link card_management to fraud_management--><g id="link_card_management_fraud_management"><path d="M408.97,380.32 C446.26,412.61 492.11,452.32 529.99,485.12 " fill="none" id="card_management-to-fraud_management" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="534.1,488.68,529.9057,479.7689,530.3168,485.4108,524.675,485.8219,534.1,488.68" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="139" x="496" y="440.177">Shares data and rules for</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="133" x="500.5" y="455.2708">fraud prevention through</text></g><!--MD5=[e5fe9ad6d62f15c9d8d162d04b48d763]
@startuml C4_Container_Diagram

!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5

!define ICONSIZE 16

!include DEVICONS/angular.puml
!include DEVICONS/java.puml
!include DEVICONS/msql_server.puml
!include FONTAWESOME/users.puml

System_Boundary(credit_card_system_boundary, "Credit Card System Boundary") {
    Container(credit_card_system, "Credit Card System", "A system for processing credit card transactions")
    Container(payment_gateway, "Payment Gateway", "Java", "Processes credit card payments")
    Container(acquirer, "Acquirer", "Java", "Processes credit card payments for merchants")
    Container(card_network, "Card Network", "N/A", "Routes transactions between issuers and acquirers")
    Container(processor, "Processor", "N/A", "Handles credit card transaction processing")
    Container(fraud_detection, "Fraud Detection", "Python", "Detects fraudulent credit card transactions")
    Container(customer_service, "Customer Service", "Java", "Assists customers with credit card issues and inquiries")
    Container(merchant_portal, "Merchant Portal", "Angular", "Provides merchant account management and reporting")
    Container(reporting, "Reporting", "Java", "Generates credit card transaction reports")
    Container(banking_system, "Banking System", "N/A", "Handles fund transfers between accounts")
    Container(card_management, "Card Management", "Java", "Manages credit card information and user accounts")
    Container(fraud_management, "Fraud Management", "Java", "Manages fraud prevention rules and investigations")
}

System_Boundary(customer_boundary, "Customer Boundary") {
    Person(customer, "Customer", "A person who uses credit cards to make purchases", $sprite="users")
    Container(card_issuer, "Card Issuer", "Java", "Issues credit cards to customers")
}

customer - -> card_issuer : "Applies for a credit card with"
customer - -> card_issuer : "Disputes a transaction with"
customer - -> card_issuer : "Requests a charge-off with"

credit_card_system_boundary - -> payment_gateway : "Purchases using"
payment_gateway - -> card_network : "Routes transactions through"
payment_gateway - -> acquirer : "Transfers payment to"
payment_gateway - -> processor : "Processes transactions using"
card_issuer - -> card_network : "Routes payment messages to"
card_network - -> acquirer : "Routes payment messages to"
card_network - -> customer_service : "Routes inquiries to"
acquirer - -> fraud_detection : "Sends transaction data to"
processor - -> card_network : "Routes processed transactions through"

acquirer - -> merchant_portal : "Manages payments and transactions through"
merchant_portal - -> reporting : "Generates transaction reports using"
merchant_portal - -> banking_system : "Performs fund transfers through"

credit_card_system_boundary - -> card_management : "Manages credit card information and accounts through"
card_management - -> fraud_management : "Shares data and rules for fraud prevention through"

@enduml

@startuml C4_Container_Diagram























skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendFontColor #FFFFFF
skinparam LegendBackgroundColor transparent
skinparam LegendBorderColor transparent

skinparam rectangle<<legendArea>> {
    backgroundcolor transparent
    bordercolor transparent
}

skinparam rectangle {
    StereotypeFontSize 12
}

skinparam database {
    StereotypeFontSize 12
}

skinparam queue {
    StereotypeFontSize 12
}

skinparam participant {
    StereotypeFontSize 12
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam person {
    StereotypeFontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    style awesome
}

skinparam rectangle<<boundary>> {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    BorderStyle dashed
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}


























































































skinparam rectangle<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<boundary>>StereotypeFontColor transparent
skinparam rectangle<<boundary>>StereotypeFontColor transparent

skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent




































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}

skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}

skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam participant<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam sequencebox<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}

skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam participant<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent






sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}

sprite $robot [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000005BFFFFFFFFFFFFFFFFFFFFFE9100000000000
0000000000AFFFFFFFFFFFFFFFFFFFFFFFFFE30000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000699405FFFFFFC427FFFFFFFFFC427FFFFFFE009982000
008FFF705FFFFFE10006FFFFFFFE00007FFFFFE00FFFF100
00CFFF705FFFFFA00001FFFFFFF900002FFFFFE00FFFF500
00DFFF705FFFFFB00002FFFFFFFA00003FFFFFE00FFFF500
00DFFF705FFFFFF4000AFFFFFFFF3000BFFFFFE00FFFF500
00DFFF705FFFFFFFA8DFFFFFFFFFFA8DFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00CFFF705FFFFFF87777777777777777CFFFFFE00FFFF500
008FFF705FFFFFF100000000000000009FFFFFE00FFFF100
000699405FFFFFF76666666666666666CFFFFFE009982000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000000EFFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFD0000000000
00000000004CFFFFFFFFFFFFFFFFFFFFFFFF910000000000
000000000000011111111111111111111110000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $robot2 [48x48/16] {
000000000000000088888888888888880000000000000000
000000000000000AFFFFFFFFFFFFFFFFA000000000000000
00000000000000CFFFFFFFFFFFFFFFFFFC00000000000000
00000000000004EFFFFFFFFFFFFFFFFFFE40000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFFA0000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000004CFFFFFFFFFFFFFFFFFFC40000000000000
000000488888848CFFFFFFFFFFFFFFFFC848888884000000
00000CFFFFFFFFC888888888888888888CFFFFFFFFC00000
00008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80000
0000CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0000CFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFC0000
00008FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF80000
00000CFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFC00000
000000488887578888888888888888888864688884000000
000000000000000000000000000000000000000000000000
}




skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam participant<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam sequencebox<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}

skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam participant<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam sequencebox<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent



















sprite $angular [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000016500000000000000000000000
000000000000000000004AFFFFB500000000000000000000
0000000000000000039FFFFFFFFFFB500000000000000000
0000000000000039EFFFFFFEFFFFFFFE9400000000000000
0000000000028DFFFFFFFFF7AFFFFFFFFFEA400000000000
0000000016CFFFFFFFFFFFF12FFFFFFFFFFFFE9300000000
0000000BFFFFFFFFFFFFFF800BFFFFFFFFFFFFFFD0000000
0000000DFFFFFFFFFFFFFF2003FFFFFFFFFFFFFFD0000000
0000000BFFFFFFFFFFFFFA0000CFFFFFFFFFFFFFB0000000
00000009FFFFFFFFFFFFF300004FFFFFFFFFFFFF90000000
00000007FFFFFFFFFFFFC000000DFFFFFFFFFFFF70000000
00000005FFFFFFFFFFFF40043006FFFFFFFFFFFF50000000
00000003FFFFFFFFFFFD000BA000EFFFFFFFFFFF30000000
00000001FFFFFFFFFFF6002FF2007FFFFFFFFFFF10000000
00000000FFFFFFFFFFE0008FF9000EFFFFFFFFFF00000000
00000000EFFFFFFFFF8000EFFF2008FFFFFFFFFD00000000
00000000CFFFFFFFFF1006FFFF9001FFFFFFFFFB00000000
00000000AFFFFFFFF9000DFFFFF1009FFFFFFFF900000000
000000008FFFFFFFF20005555552002FFFFFFFF800000000
000000006FFFFFFFB00000000000000AFFFFFFF600000000
000000004FFFFFFF4000000000000002FFFFFFF400000000
000000002FFFFFFD0005888888888000BFFFFFF200000000
000000000FFFFFF5000EFFFFFFFFF4004FFFFFF000000000
000000000EFFFFE0006FFFFFFFFFFB000CFFFFE000000000
000000000CFFFF7000CFFFFFFFFFFF2005FFFFC000000000
000000000AFFFF1002FFFFFFFFFFFF9000DFFFA000000000
0000000008FFFB444AFFFFFFFFFFFFF3338FFF8000000000
0000000006FFFFFFFFFFFFFFFFFFFFFFFFFFFF6000000000
0000000003EFFFFFFFFFFFFFFFFFFFFFFFFFFE2000000000
000000000018FFFFFFFFFFFFFFFFFFFFFFFF800000000000
00000000000019FFFFFFFFFFFFFFFFFFFF91000000000000
000000000000003CFFFFFFFFFFFFFFFFB200000000000000
00000000000000004DFFFFFFFFFFFFC30000000000000000
0000000000000000005EFFFFFFFFE5000000000000000000
000000000000000000007FFFFFE700000000000000000000
00000000000000000000019FF91000000000000000000000
000000000000000000000002200000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV ANGULAR>> White
sprite $java [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000120000000000000000000
000000000000000000000000000080000000000000000000
0000000000000000000000000001B0000000000000000000
0000000000000000000000000005A0000000000000000000
000000000000000000000000000C70000000000000000000
000000000000000000000000006F10000000000000000000
00000000000000000000000005F700000000000000000000
0000000000000000000000006FB000000000000000000000
000000000000000000000009FB0001650000000000000000
0000000000000000000001CFA0018C200000000000000000
000000000000000000002DF9005E90000000000000000000
00000000000000000001DF8008F700000000000000000000
0000000000000000000BFB005FA000000000000000000000
0000000000000000001FF200CF3000000000000000000000
0000000000000000001FD000FF4000000000000000000000
0000000000000000000DE000EFB000000000000000000000
00000000000000000005F2008FF600000000000000000000
00000000000000000000AA001EFE00000000000000000000
000000000000000000000B4004FF40000000000000000000
0000000000000000000000A100BF20000000000000000000
00000000000000000000000400A900000011000000000000
00000000000000016730000001800000046CB10000000000
00000000000005BD30000000010000120000DC0000000000
0000000000001DFEA7765567889BCB7000008F1000000000
000000000000001468899988753200000000BE0000000000
000000000000000001000000000000000004F50000000000
0000000000000000C800000000024200004E500000000000
0000000000000000CFFDCBBBCEFFFC201881000000000000
00000000000000000268AAAAA86400003000000000000000
000000000000000000000000000000000000000000000000
000000000000000009A10000002410000000000000000000
00000000000000000CFFFEDDEFFFF6000000000000000000
00000000000003520049CEEFFCA720000000000000000000
000000000019B50000000000000000000002400000000000
0000000002FF820000000000000000000378000000000000
00000000018DFFDB9766555566789ACCA610160000000000
00000000000001357899AABAA9875310026AA10000000000
0000000000000003542211111233579BCB72000000000000
000000000000000002457778888765310000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV JAVA>> White
sprite $msql_server [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000001000000000000000000000000000
000000000000000000454300000000000000000000000000
000000000000000176100600000000000000000000000000
000000000000007C77530800000000000000000000000000
000000000000099080246B70000000000000000000000000
00000000000003B75B1283B0000000000000000000000000
00000000000000475BAE9688000000000000000000000000
000000000000000466004ABACB9520000000000000000000
000000000000000049147060005CDD940000000000000000
000000000000000009B600801963304AE810000000000000
000000000000000002FD9497A10800001BF8000000000000
000000000000000000915CFD201902896467C20000000000
000000000000000000802BB8FAAAB8200A002B2000000000
0000000000000000007394602EFA0000940001A100000000
000000000000000000A80928BE5D70056000001800000000
000000000000000000E82DB48500A6371489875200000000
0000000000000000036BF60480001CDCA620000000000000
0000000000000000084DC228038CB7200000000000000000
00000000000000003B941798CA9800000000000000000000
000000000000000196506EE9403900000000000000000000
0000000000000008769C60A0353B00000000000000000000
000000000000009DCD8900A0007B00000000000000000000
00000000000009F91B165090174B00000000000000000000
0000000000009C9059008788602B00000000000000000000
0000000000062590D1004FD1004900000000000000000000
00000000005003C7839C8C27505800000000000000000000
00000000080000EFC830760014A500000000000000000000
00000000831689ED1000D00055B200000000000000000000
00000000E6510754D206728600D000000000000000000000
00000001F10027003C6EB60002A000000000000000000000
00000000CA0080005BFE6000075000000000000000000000
000000002F86148843B037653B0000000000000000000000
0000000002DC62000A100015780000000000000000000000
000000000007D71063037740710000000000000000000000
000000000000049AB8630000800000000000000000000000
000000000000000025887656200000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV MSQL_SERVER>> White
sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White

  rectangle "== Credit Card System Boundary\n<size:12>[System]</size>" <<system_boundary>><<boundary>> as credit_card_system_boundary  {
      rectangle "== Credit Card System\n//<size:12>[A system for processing credit card transactions]</size>//" <<container>> as credit_card_system
      rectangle "== Payment Gateway\n//<size:12>[Java]</size>//\n\nProcesses credit card payments" <<container>> as payment_gateway
      rectangle "== Acquirer\n//<size:12>[Java]</size>//\n\nProcesses credit card payments for merchants" <<container>> as acquirer
      rectangle "== Card Network\n//<size:12>[N/A]</size>//\n\nRoutes transactions between issuers and acquirers" <<container>> as card_network
      rectangle "== Processor\n//<size:12>[N/A]</size>//\n\nHandles credit card transaction processing" <<container>> as processor
      rectangle "== Fraud Detection\n//<size:12>[Python]</size>//\n\nDetects fraudulent credit card transactions" <<container>> as fraud_detection
      rectangle "== Customer Service\n//<size:12>[Java]</size>//\n\nAssists customers with credit card issues and inquiries" <<container>> as customer_service
      rectangle "== Merchant Portal\n//<size:12>[Angular]</size>//\n\nProvides merchant account management and reporting" <<container>> as merchant_portal
      rectangle "== Reporting\n//<size:12>[Java]</size>//\n\nGenerates credit card transaction reports" <<container>> as reporting
      rectangle "== Banking System\n//<size:12>[N/A]</size>//\n\nHandles fund transfers between accounts" <<container>> as banking_system
      rectangle "== Card Management\n//<size:12>[Java]</size>//\n\nManages credit card information and user accounts" <<container>> as card_management
      rectangle "== Fraud Management\n//<size:12>[Java]</size>//\n\nManages fraud prevention rules and investigations" <<container>> as fraud_management
}

  rectangle "== Customer Boundary\n<size:12>[System]</size>" <<system_boundary>><<boundary>> as customer_boundary  {
    rectangle "<$users>\n== Customer\n\nA person who uses credit cards to make purchases" <<person>> as customer 
      rectangle "== Card Issuer\n//<size:12>[Java]</size>//\n\nIssues credit cards to customers" <<container>> as card_issuer
}

customer - -> card_issuer : "Applies for a credit card with"
customer - -> card_issuer : "Disputes a transaction with"
customer - -> card_issuer : "Requests a charge-off with"

credit_card_system_boundary - -> payment_gateway : "Purchases using"
payment_gateway - -> card_network : "Routes transactions through"
payment_gateway - -> acquirer : "Transfers payment to"
payment_gateway - -> processor : "Processes transactions using"
card_issuer - -> card_network : "Routes payment messages to"
card_network - -> acquirer : "Routes payment messages to"
card_network - -> customer_service : "Routes inquiries to"
acquirer - -> fraud_detection : "Sends transaction data to"
processor - -> card_network : "Routes processed transactions through"

acquirer - -> merchant_portal : "Manages payments and transactions through"
merchant_portal - -> reporting : "Generates transaction reports using"
merchant_portal - -> banking_system : "Performs fund transfers through"

credit_card_system_boundary - -> card_management : "Manages credit card information and accounts through"
card_management - -> fraud_management : "Shares data and rules for fraud prevention through"

@enduml

PlantUML version 1.2022.7(Mon Aug 22 22:31:30 IST 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: Cp1252
Language: en
Country: US
--></g></svg>