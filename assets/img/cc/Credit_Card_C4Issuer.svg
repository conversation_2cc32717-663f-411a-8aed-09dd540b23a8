<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="466px" preserveAspectRatio="none" style="width:974px;height:466px;background:#FFFFFF;" version="1.1" viewBox="0 0 974 466" width="974px" zoomAndPan="magnify"><defs/><g><!--MD5=[7a438794d04ffa1e19af9d05481d3703]
cluster processor_boundary--><g id="cluster_processor_boundary"><rect fill="none" height="453.72" rx="2.5" ry="2.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="961" x="7" y="7"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="159" x="408" y="45.6094">Processor Boundary</text><text fill="#444444" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="51" x="462" y="61.582">[System]</text></g><!--MD5=[76976b5bc05be6a5fc6fae32b92c7f1b]
cluster processor--><g id="cluster_processor"><rect fill="#438DD5" height="261" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:1.0;" width="897" x="39" y="167.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="455" y="182.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="79" x="448" y="201.4231">Processor</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" font-weight="bold" lengthAdjust="spacing" textLength="29" x="473" y="217.3958">[N/A]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="4" x="485.5" y="234.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="303" x="336" y="252.1751">Handles credit card transaction processing</text></g><!--MD5=[4c8b68244c4c53aaa007eabd84727103]
entity authorization--><g id="elem_authorization"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="155" x="55.5" y="280.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="100.5" y="303.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="103" x="81.5" y="322.4231">Authorization</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="117" y="338.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="131" y="355.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="131" x="65.5" y="373.1751">Performs transaction</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81" x="92.5" y="390.7845">authorization</text></g><!--MD5=[74dbe7c8e654ebbbd7c734ac26338495]
entity transaction_handler--><g id="elem_transaction_handler"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="197" x="245.5" y="280.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="311.5" y="303.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="155" x="266.5" y="322.4231">Transaction Handler</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="328" y="338.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="342" y="355.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="173" x="255.5" y="373.1751">Handles transaction routing</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="97" x="295.5" y="390.7845">and processing</text></g><!--MD5=[59dd643a237a0a708ea83f758bfb1d1d]
entity reconciliation--><g id="elem_reconciliation"><rect fill="#438DD5" height="140.75" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="197" x="477.5" y="271.72"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="543.5" y="294.177">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="109" x="521.5" y="313.4231">Reconciliation</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="560" y="329.3958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="574" y="346.5657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="131" x="508.5" y="364.1751">Performs transaction</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="173" x="487.5" y="381.7845">reconciliation, clearing, and</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="65" x="543.5" y="399.3938">settlement</text></g><!--MD5=[bb652fc1ce3da2060c9c3c7211dc4a65]
entity reporting--><g id="elem_reporting"><rect fill="#438DD5" height="105.5313" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="210" x="710" y="289.22"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="782.5" y="311.677">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="76" x="777" y="330.9231">Reporting</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="799" y="346.8958">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="813" y="364.0657"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="190" x="720" y="381.6751">Generates transaction reports</text></g><!--MD5=[317d7cebdc941485edd3bd9a06f64857]
link processor_boundary to authorization--><g id="link_processor_boundary_authorization"><path d="M459.87,73.72 C454.58,73.78 284.98,75.94 247,103.72 C190.92,144.74 161.26,220.71 146.4,275.71 " fill="none" id="processor_boundary-to-authorization" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="145.1,280.63,151.2599,272.9452,146.3732,275.7948,143.5236,270.9081,145.1,280.63" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="120" x="248" y="124.677">Handles authorization</text></g><!--MD5=[170fefd30575a0dd8fa6bd3e7c7da275]
link processor_boundary to transaction_handler--><g id="link_processor_boundary_transaction_handler"><path d="M459.87,73.74 C457.2,74.06 414.26,79.66 395,103.72 C356.21,152.18 345.17,223.43 342.79,275.25 " fill="none" id="processor_boundary-to-transaction_handler" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="342.57,280.51,346.9275,271.6776,342.7704,275.514,338.934,271.3569,342.57,280.51" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="118" x="396" y="124.677">Handles transactions</text></g><!--MD5=[866ba75a6795ea42761e8de04d630f2f]
link processor_boundary to reconciliation--><g id="link_processor_boundary_reconciliation"><path d="M460.12,73.74 C462.53,74.17 501.35,81.35 519,103.72 C555.44,149.91 569.12,215.78 574.01,266.45 " fill="none" id="processor_boundary-to-reconciliation" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="574.49,271.61,577.6491,262.2815,574.0322,266.631,569.6827,263.0141,574.49,271.61" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="130" x="537" y="117.177">Performs reconciliation,</text><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="132" x="537.5" y="132.2708">clearing, and settlement</text></g><!--MD5=[d3c4c7cf4f4cd6262215b23b360a1a5d]
link processor_boundary to reporting--><g id="link_processor_boundary_reporting"><path d="M460.13,73.72 C465.54,73.81 638.91,76.68 679,103.72 C742.09,146.28 779.47,228.55 798.62,284.18 " fill="none" id="processor_boundary-to-reporting" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="800.3,289.14,801.2043,279.3327,798.6974,284.4038,793.6264,281.8969,800.3,289.14" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="100" x="713" y="124.677">Generates reports</text></g><!--MD5=[b495caf4f99eed9032da38fd79b787dc]
@startuml C4_Container_Diagram

!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons

!define ICONSIZE 16

!include DEVICONS/java.puml

System_Boundary(processor_boundary, "Processor Boundary") {
    Container(processor, "Processor", "N/A", "Handles credit card transaction processing") {
        Container(authorization, "Authorization", "Java", "Performs transaction authorization")
        Container(transaction_handler, "Transaction Handler", "Java", "Handles transaction routing and processing")
        Container(reconciliation, "Reconciliation", "Java", "Performs transaction reconciliation, clearing, and settlement")
        Container(reporting, "Reporting", "Java", "Generates transaction reports")
    }
}

processor_boundary - -> authorization : "Handles authorization"
processor_boundary - -> transaction_handler : "Handles transactions"
processor_boundary - -> reconciliation : "Performs reconciliation, clearing, and settlement"
processor_boundary - -> reporting : "Generates reports"

@enduml

@startuml C4_Container_Diagram























skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendFontColor #FFFFFF
skinparam LegendBackgroundColor transparent
skinparam LegendBorderColor transparent

skinparam rectangle<<legendArea>> {
    backgroundcolor transparent
    bordercolor transparent
}

skinparam rectangle {
    StereotypeFontSize 12
}

skinparam database {
    StereotypeFontSize 12
}

skinparam queue {
    StereotypeFontSize 12
}

skinparam participant {
    StereotypeFontSize 12
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam person {
    StereotypeFontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    style awesome
}

skinparam rectangle<<boundary>> {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    BorderStyle dashed
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}


























































































skinparam rectangle<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<boundary>>StereotypeFontColor transparent
skinparam rectangle<<boundary>>StereotypeFontColor transparent

skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent




































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}

skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}

skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam participant<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam sequencebox<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}

skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam participant<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent






sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}

sprite $robot [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000005BFFFFFFFFFFFFFFFFFFFFFE9100000000000
0000000000AFFFFFFFFFFFFFFFFFFFFFFFFFE30000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000699405FFFFFFC427FFFFFFFFFC427FFFFFFE009982000
008FFF705FFFFFE10006FFFFFFFE00007FFFFFE00FFFF100
00CFFF705FFFFFA00001FFFFFFF900002FFFFFE00FFFF500
00DFFF705FFFFFB00002FFFFFFFA00003FFFFFE00FFFF500
00DFFF705FFFFFF4000AFFFFFFFF3000BFFFFFE00FFFF500
00DFFF705FFFFFFFA8DFFFFFFFFFFA8DFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00CFFF705FFFFFF87777777777777777CFFFFFE00FFFF500
008FFF705FFFFFF100000000000000009FFFFFE00FFFF100
000699405FFFFFF76666666666666666CFFFFFE009982000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000000EFFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFD0000000000
00000000004CFFFFFFFFFFFFFFFFFFFFFFFF910000000000
000000000000011111111111111111111110000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $robot2 [48x48/16] {
000000000000000088888888888888880000000000000000
000000000000000AFFFFFFFFFFFFFFFFA000000000000000
00000000000000CFFFFFFFFFFFFFFFFFFC00000000000000
00000000000004EFFFFFFFFFFFFFFFFFFE40000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFFA0000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000004CFFFFFFFFFFFFFFFFFFC40000000000000
000000488888848CFFFFFFFFFFFFFFFFC848888884000000
00000CFFFFFFFFC888888888888888888CFFFFFFFFC00000
00008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80000
0000CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0000CFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFC0000
00008FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF80000
00000CFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFC00000
000000488887578888888888888888888864688884000000
000000000000000000000000000000000000000000000000
}




skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam participant<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam sequencebox<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}

skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam participant<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam sequencebox<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent



















sprite $java [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000120000000000000000000
000000000000000000000000000080000000000000000000
0000000000000000000000000001B0000000000000000000
0000000000000000000000000005A0000000000000000000
000000000000000000000000000C70000000000000000000
000000000000000000000000006F10000000000000000000
00000000000000000000000005F700000000000000000000
0000000000000000000000006FB000000000000000000000
000000000000000000000009FB0001650000000000000000
0000000000000000000001CFA0018C200000000000000000
000000000000000000002DF9005E90000000000000000000
00000000000000000001DF8008F700000000000000000000
0000000000000000000BFB005FA000000000000000000000
0000000000000000001FF200CF3000000000000000000000
0000000000000000001FD000FF4000000000000000000000
0000000000000000000DE000EFB000000000000000000000
00000000000000000005F2008FF600000000000000000000
00000000000000000000AA001EFE00000000000000000000
000000000000000000000B4004FF40000000000000000000
0000000000000000000000A100BF20000000000000000000
00000000000000000000000400A900000011000000000000
00000000000000016730000001800000046CB10000000000
00000000000005BD30000000010000120000DC0000000000
0000000000001DFEA7765567889BCB7000008F1000000000
000000000000001468899988753200000000BE0000000000
000000000000000001000000000000000004F50000000000
0000000000000000C800000000024200004E500000000000
0000000000000000CFFDCBBBCEFFFC201881000000000000
00000000000000000268AAAAA86400003000000000000000
000000000000000000000000000000000000000000000000
000000000000000009A10000002410000000000000000000
00000000000000000CFFFEDDEFFFF6000000000000000000
00000000000003520049CEEFFCA720000000000000000000
000000000019B50000000000000000000002400000000000
0000000002FF820000000000000000000378000000000000
00000000018DFFDB9766555566789ACCA610160000000000
00000000000001357899AABAA9875310026AA10000000000
0000000000000003542211111233579BCB72000000000000
000000000000000002457778888765310000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV JAVA>> White

  rectangle "== Processor Boundary\n<size:12>[System]</size>" <<system_boundary>><<boundary>> as processor_boundary  {
      rectangle "== Processor\n//<size:12>[N/A]</size>//\n\nHandles credit card transaction processing" <<container>> as processor {
          rectangle "== Authorization\n//<size:12>[Java]</size>//\n\nPerforms transaction authorization" <<container>> as authorization
          rectangle "== Transaction Handler\n//<size:12>[Java]</size>//\n\nHandles transaction routing and processing" <<container>> as transaction_handler
          rectangle "== Reconciliation\n//<size:12>[Java]</size>//\n\nPerforms transaction reconciliation, clearing, and settlement" <<container>> as reconciliation
          rectangle "== Reporting\n//<size:12>[Java]</size>//\n\nGenerates transaction reports" <<container>> as reporting
    }
}

processor_boundary - -> authorization : "Handles authorization"
processor_boundary - -> transaction_handler : "Handles transactions"
processor_boundary - -> reconciliation : "Performs reconciliation, clearing, and settlement"
processor_boundary - -> reporting : "Generates reports"

@enduml

PlantUML version 1.2022.7(Mon Aug 22 22:31:30 IST 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: Cp1252
Language: en
Country: US
--></g></svg>