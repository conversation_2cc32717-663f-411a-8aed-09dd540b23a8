<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="765px" preserveAspectRatio="none" style="width:1013px;height:765px;background:#FFFFFF;" version="1.1" viewBox="0 0 1013 765" width="1013px" zoomAndPan="magnify"><defs/><g><!--MD5=[4ac8fac119323d93c31a7907edb7f806]
cluster credit_card_fraud_detection_boundary--><g id="cluster_credit_card_fraud_detection_boundary"><rect fill="none" height="533" rx="2.5" ry="2.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="1000" x="7" y="226"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="354" x="330" y="264.6094">Credit Card Fraud Detection System Boundary</text><text fill="#444444" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="51" x="481.5" y="280.582">[System]</text></g><!--MD5=[ffb0ff7dcb52b19eaa955401864f4631]
cluster credit_card_fraud_detection--><g id="cluster_credit_card_fraud_detection"><rect fill="#438DD5" height="427" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:1.0;" width="952" x="31" y="308"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="474.5" y="322.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="213" x="400.5" y="341.7031">Credit Card Fraud Detection</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" font-weight="bold" lengthAdjust="spacing" textLength="35" x="489.5" y="357.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="4" x="505" y="374.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="273" x="370.5" y="392.4551">Detects and prevents credit card fraud</text></g><!--MD5=[e16659c22ba103f2faaa8b6383aeecae]
entity data_collection--><g id="elem_data_collection"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="177" x="301.5" y="412"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="357.5" y="434.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="116" x="332" y="453.7031">Data Collection</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="374" y="469.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="388" y="486.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="311.5" y="504.4551">Gathers transaction and</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="90" x="345" y="522.0645">customer data</text></g><!--MD5=[4cc5521e506aed7796125cddef0fc646]
entity rule_engine--><g id="elem_rule_engine"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="198" x="514" y="412"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="580.5" y="434.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="93" x="566.5" y="453.7031">Rule Engine</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="597" y="469.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="611" y="486.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="174" x="524" y="504.4551">Applies predefined rules for</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="95" x="565.5" y="522.0645">fraud detection</text></g><!--MD5=[284f1f4be4ae93a95effe34df0b9479a]
entity machine_learning--><g id="elem_machine_learning"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="220" x="747" y="412"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="824.5" y="434.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="195" x="759.5" y="453.7031">Machine Learning Models</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="44" x="835" y="469.6758">[Python]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="855" y="486.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="196" x="757" y="504.4551">Applies ML algorithms for fraud</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="58" x="828" y="522.0645">detection</text></g><!--MD5=[c8d94a6a5504921d4c20a65c5463cc6e]
entity risk_scoring--><g id="elem_risk_scoring"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="161" x="309.5" y="596"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="357.5" y="618.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="98" x="341" y="637.7031">Risk Scoring</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="374" y="653.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="388" y="670.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="137" x="319.5" y="688.4551">Assigns risk scores to</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="77" x="351.5" y="706.0645">transactions</text></g><!--MD5=[28528f68de3ac611793143e8ea6d2f3d]
entity alert_generation--><g id="elem_alert_generation"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="209" x="505.5" y="596"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="577.5" y="618.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="123" x="548.5" y="637.7031">Alert Generation</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="594" y="653.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="608" y="670.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="185" x="515.5" y="688.4551">Generates alerts for potential</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="74" x="573" y="706.0645">fraud cases</text></g><!--MD5=[4d2c0729cbbac53ebb6e815e41df7fe5]
entity case_management--><g id="elem_case_management"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="219" x="47.5" y="412"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="124.5" y="434.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="141" x="86.5" y="453.7031">Case Management</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="141" y="469.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="155" y="486.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="199" x="57.5" y="504.4551">Handles fraud investigation and</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="62" x="126" y="522.0645">resolution</text></g><!--MD5=[d924767649754131b82a5b22b3e3d9e5]
entity reporting_analytics--><g id="elem_reporting_analytics"><rect fill="#438DD5" height="123.1406" rx="2.5" ry="2.5" style="stroke:#3C7FC0;stroke-width:0.5;" width="203" x="749.5" y="596"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="818.5" y="618.457">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="183" x="759.5" y="637.7031">Reporting and Analytics</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="32" x="835" y="653.6758">[Java]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="849" y="670.8457"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153" x="772.5" y="688.4551">Generates fraud-related</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="132" x="785" y="706.0645">reports and analytics</text></g><!--MD5=[21e0f048221bf096eee0fa63e7cd3694]
entity fraud_analyst--><g id="elem_fraud_analyst"><rect fill="#08427B" height="156.0469" rx="2.5" ry="2.5" style="stroke:#073B6F;stroke-width:0.5;" width="206" x="54" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="52" x="131" y="29.457">«person»</text><image height="48" width="48" x="133" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=" y="32.0938"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="106" x="104" y="96.7031">Fraud Analyst</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4" x="155" y="114.752"> </text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="182" x="64" y="132.3613">Responsible for investigating</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="74" x="120" y="149.9707">fraud cases</text></g><!--MD5=[bfd100b68fed014d77536b717882cac0]
link fraud_analyst to case_management--><g id="link_fraud_analyst_case_management"><path d="M157,163.29 C157,234.57 157,339.25 157,406.65 " fill="none" id="fraud_analyst-to-case_management" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="157,411.84,161,402.84,157,406.84,153,402.84,157,411.84" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="134" x="158" y="206.457">Investigates fraud cases</text></g><!--MD5=[ad36a95c8b449c6b854adc2cd30218a7]
@startuml C4_Container_Diagram

!include  https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5

!define ICONSIZE 16

!include DEVICONS/java.puml
!include DEVICONS/database.puml

System_Boundary(credit_card_fraud_detection_boundary, "Credit Card Fraud Detection System Boundary") {
    Container(credit_card_fraud_detection, "Credit Card Fraud Detection", "Java", "Detects and prevents credit card fraud") {
        Container(data_collection, "Data Collection", "Java", "Gathers transaction and customer data")
        Container(rule_engine, "Rule Engine", "Java", "Applies predefined rules for fraud detection")
        Container(machine_learning, "Machine Learning Models", "Python", "Applies ML algorithms for fraud detection")
        Container(risk_scoring, "Risk Scoring", "Java", "Assigns risk scores to transactions")
        Container(alert_generation, "Alert Generation", "Java", "Generates alerts for potential fraud cases")
        Container(case_management, "Case Management", "Java", "Handles fraud investigation and resolution")
        Container(reporting_analytics, "Reporting and Analytics", "Java", "Generates fraud-related reports and analytics")
    }
}

Person(fraud_analyst, "Fraud Analyst", "Responsible for investigating fraud cases")

fraud_analyst - -> case_management : "Investigates fraud cases"

@enduml

@startuml C4_Container_Diagram























skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendFontColor #FFFFFF
skinparam LegendBackgroundColor transparent
skinparam LegendBorderColor transparent

skinparam rectangle<<legendArea>> {
    backgroundcolor transparent
    bordercolor transparent
}

skinparam rectangle {
    StereotypeFontSize 12
}

skinparam database {
    StereotypeFontSize 12
}

skinparam queue {
    StereotypeFontSize 12
}

skinparam participant {
    StereotypeFontSize 12
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam person {
    StereotypeFontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    style awesome
}

skinparam rectangle<<boundary>> {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    BorderStyle dashed
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}


























































































skinparam rectangle<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<boundary>>StereotypeFontColor transparent
skinparam rectangle<<boundary>>StereotypeFontColor transparent

skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent




































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}

skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}

skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam participant<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam sequencebox<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}

skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam participant<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}


skinparam rectangle<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<system_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<system_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<system_boundary>>StereotypeFontColor transparent
skinparam rectangle<<system_boundary>>StereotypeFontColor transparent


skinparam rectangle<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<enterprise_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<enterprise_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<enterprise_boundary>>StereotypeFontColor transparent
skinparam rectangle<<enterprise_boundary>>StereotypeFontColor transparent






sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}

sprite $robot [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000005BFFFFFFFFFFFFFFFFFFFFFE9100000000000
0000000000AFFFFFFFFFFFFFFFFFFFFFFFFFE30000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000699405FFFFFFC427FFFFFFFFFC427FFFFFFE009982000
008FFF705FFFFFE10006FFFFFFFE00007FFFFFE00FFFF100
00CFFF705FFFFFA00001FFFFFFF900002FFFFFE00FFFF500
00DFFF705FFFFFB00002FFFFFFFA00003FFFFFE00FFFF500
00DFFF705FFFFFF4000AFFFFFFFF3000BFFFFFE00FFFF500
00DFFF705FFFFFFFA8DFFFFFFFFFFA8DFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00DFFF705FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE00FFFF500
00CFFF705FFFFFF87777777777777777CFFFFFE00FFFF500
008FFF705FFFFFF100000000000000009FFFFFE00FFFF100
000699405FFFFFF76666666666666666CFFFFFE009982000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFFFE000000000
000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFFFC000000000
000000000EFFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFD0000000000
00000000004CFFFFFFFFFFFFFFFFFFFFFFFF910000000000
000000000000011111111111111111111110000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $robot2 [48x48/16] {
000000000000000088888888888888880000000000000000
000000000000000AFFFFFFFFFFFFFFFFA000000000000000
00000000000000CFFFFFFFFFFFFFFFFFFC00000000000000
00000000000004EFFFFFFFFFFFFFFFFFFE40000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFFA0000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000008FF8FFFFFFFFFFFFFFFFFFFF8FF80000000000
00000000000888FFFFFFFFFFFFFFFFFFFF88800000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000008FFFFFFFFFFFFFFFFFFFF80000000000000
00000000000004CFFFFFFFFFFFFFFFFFFC40000000000000
000000488888848CFFFFFFFFFFFFFFFFC848888884000000
00000CFFFFFFFFC888888888888888888CFFFFFFFFC00000
00008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF80000
0000CFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC0000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0008FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF8000
0000CFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFC0000
00008FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF80000
00000CFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFC00000
000000488887578888888888888888888864688884000000
000000000000000000000000000000000000000000000000
}




skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam participant<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam sequencebox<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam participant<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam sequencebox<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam participant<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam sequencebox<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}

skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam participant<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam sequencebox<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}


skinparam rectangle<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam database<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam queue<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam person<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam actor<<container_boundary>> {
    FontColor transparent
    BackgroundColor transparent
    BorderColor #444444
}
skinparam participant<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam sequencebox<<container_boundary>> {
    FontColor #444444
    BackgroundColor transparent
    BorderColor #444444
}
skinparam package<<container_boundary>>StereotypeFontColor transparent
skinparam rectangle<<container_boundary>>StereotypeFontColor transparent



















sprite $java [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000120000000000000000000
000000000000000000000000000080000000000000000000
0000000000000000000000000001B0000000000000000000
0000000000000000000000000005A0000000000000000000
000000000000000000000000000C70000000000000000000
000000000000000000000000006F10000000000000000000
00000000000000000000000005F700000000000000000000
0000000000000000000000006FB000000000000000000000
000000000000000000000009FB0001650000000000000000
0000000000000000000001CFA0018C200000000000000000
000000000000000000002DF9005E90000000000000000000
00000000000000000001DF8008F700000000000000000000
0000000000000000000BFB005FA000000000000000000000
0000000000000000001FF200CF3000000000000000000000
0000000000000000001FD000FF4000000000000000000000
0000000000000000000DE000EFB000000000000000000000
00000000000000000005F2008FF600000000000000000000
00000000000000000000AA001EFE00000000000000000000
000000000000000000000B4004FF40000000000000000000
0000000000000000000000A100BF20000000000000000000
00000000000000000000000400A900000011000000000000
00000000000000016730000001800000046CB10000000000
00000000000005BD30000000010000120000DC0000000000
0000000000001DFEA7765567889BCB7000008F1000000000
000000000000001468899988753200000000BE0000000000
000000000000000001000000000000000004F50000000000
0000000000000000C800000000024200004E500000000000
0000000000000000CFFDCBBBCEFFFC201881000000000000
00000000000000000268AAAAA86400003000000000000000
000000000000000000000000000000000000000000000000
000000000000000009A10000002410000000000000000000
00000000000000000CFFFEDDEFFFF6000000000000000000
00000000000003520049CEEFFCA720000000000000000000
000000000019B50000000000000000000002400000000000
0000000002FF820000000000000000000378000000000000
00000000018DFFDB9766555566789ACCA610160000000000
00000000000001357899AABAA9875310026AA10000000000
0000000000000003542211111233579BCB72000000000000
000000000000000002457778888765310000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV JAVA>> White
sprite $database [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000124566665421000000000000000000
0000000000000038BEFFFFFFFFFFFFEB8300000000000000
0000000000018EFFFFFFFFFFFFFFFFFFFFE8100000000000
00000000005EFFFFD97421000012479EFFFFE40000000000
0000000002FFFFD400000000000000004DFFFF2000000000
0000000007FFFF70000000000000000007FFFF7000000000
0000000007FFFFF820000000000000028FFFFF7000000000
0000000007FFFFFFFDB8654334568BDFFFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000005FFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000000
0000000000CFFFFFFFFFFFFFFFFFFFFFFFFFFC0000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF900000000000
00000000000017CFFFFFFFFFFFFFFFFFFC71000000000000
0000000004B1000147ACDEFFFFEDCA7410001B4000000000
0000000007FF810000000000000000000018FF7000000000
0000000007FFFFC852000000000000258CFFFF7000000000
0000000007FFFFFFFFFDCBA99ABCDFFFFFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000004FFFFFFFFFFFFFFFFFFFFFFFFFFFF3000000000
00000000008FFFFFFFFFFFFFFFFFFFFFFFFFF70000000000
000000000003BFFFFFFFFFFFFFFFFFFFFFFB300000000000
000000000120027BEFFFFFFFFFFFFFFEB720021000000000
0000000006F600000146899AA998641000006F6000000000
0000000007FFD7200000000000000000027DFF7000000000
0000000007FFFFFEB85321000012358BEFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFF7000000000
0000000006FFFFFFFFFFFFFFFFFFFFFFFFFFFF6000000000
0000000001FFFFFFFFFFFFFFFFFFFFFFFFFFFE1000000000
00000000003DFFFFFFFFFFFFFFFFFFFFFFFFD30000000000
0000000000005CFFFFFFFFFFFFFFFFFFFFC5000000000000
00000000000000159CEFFFFFFFFFFEC95100000000000000
000000000000000000002344443200000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<DEV DATABASE>> White

  rectangle "== Credit Card Fraud Detection System Boundary\n<size:12>[System]</size>" <<system_boundary>><<boundary>> as credit_card_fraud_detection_boundary  {
      rectangle "== Credit Card Fraud Detection\n//<size:12>[Java]</size>//\n\nDetects and prevents credit card fraud" <<container>> as credit_card_fraud_detection {
          rectangle "== Data Collection\n//<size:12>[Java]</size>//\n\nGathers transaction and customer data" <<container>> as data_collection
          rectangle "== Rule Engine\n//<size:12>[Java]</size>//\n\nApplies predefined rules for fraud detection" <<container>> as rule_engine
          rectangle "== Machine Learning Models\n//<size:12>[Python]</size>//\n\nApplies ML algorithms for fraud detection" <<container>> as machine_learning
          rectangle "== Risk Scoring\n//<size:12>[Java]</size>//\n\nAssigns risk scores to transactions" <<container>> as risk_scoring
          rectangle "== Alert Generation\n//<size:12>[Java]</size>//\n\nGenerates alerts for potential fraud cases" <<container>> as alert_generation
          rectangle "== Case Management\n//<size:12>[Java]</size>//\n\nHandles fraud investigation and resolution" <<container>> as case_management
          rectangle "== Reporting and Analytics\n//<size:12>[Java]</size>//\n\nGenerates fraud-related reports and analytics" <<container>> as reporting_analytics
    }
}

rectangle "<$person>\n== Fraud Analyst\n\nResponsible for investigating fraud cases" <<person>> as fraud_analyst 

fraud_analyst - -> case_management : "Investigates fraud cases"

@enduml

PlantUML version 1.2022.7(Mon Aug 22 22:31:30 IST 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: Cp1252
Language: en
Country: US
--></g></svg>