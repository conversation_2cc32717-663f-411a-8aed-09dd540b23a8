@import url("https://fonts.googleapis.com/css2?family=Rajdhani:wght@400&family=Roboto:wght@400&display=swap");
:root {
  --background-content: #0e1012;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Roboto";
  font-size: medium;
  font-weight: 400;
  background: #dfe9ff;
}

.menu-header {
  font-family: "Rajdhani", sans-serif;
}

.page-style {
  display: grid;
  grid-gap: 1px;
  grid-template-columns: auto;
  grid-template-areas: "header-area" "content-area";
  grid-template-rows: auto 1fr;
  grid-auto-columns: -webkit-min-content;
  grid-auto-columns: min-content;
}

.page-style div:first-child:not(:empty) {
  max-width: auto;
}

header {
  grid-area: header-area;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 10;
  height: 60px;
}

main {
  grid-area: content-area;
  padding-top: 60px;
  padding-bottom: 40px;
  position: relative;
  height: calc(100vh - 100px);
  vertical-align: middle;
}

.sidebar {
  grid-area: sidebar-area;
  display: none;
}
@media (min-width: 768px) {
  .sidebar {
    display: block;
  }
}

.toggle-password-icon {
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: black;
}

#toggle-password {
  font-size: 14px;
}

.container-login-button {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding-top: 20px;
}

.container_custom {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background-content);
  width: 50%;
  text-align: center;
  z-index: inherit;
  padding: 10px;
}

.container1x4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background-content);
  width: 25%;
  height: 25%;
  text-align: center;
  z-index: inherit;
  padding: 10px;
}

.container1x2 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background-content);
  width: 80%;
  height: auto;
  text-align: center;
  z-index: inherit;
  padding: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  /* Use flexbox */
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  /* Arrange children vertically */
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  /* Center items horizontally */
}

/* Extra small devices (phones, 576px and down) */
@media (max-width: 576px) {
  .container1x2 {
    width: 90%;
    padding: 5px;
  }
}
/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container1x2 {
    width: 80%;
    padding: 10px;
  }
}
/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container1x2 {
    width: 70%;
    padding: 15px;
  }
}
/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container1x2 {
    width: 60%;
    padding: 20px;
  }
}
/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container1x2 {
    width: 50%;
    padding: 20px;
  }
}
.container3x4 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background-content);
  width: 75%;
  height: 75%;
  text-align: center;
  z-index: inherit;
  padding: 10px;
}

@media (max-width: 576px) {
  .container1x4,
  .container1x2,
  .container3x4 {
    width: 100%;
  }
}
.format_button {
  font-family: "Rajdhani", sans-serif;
  font-size: small;
  font-weight: 700;
  line-height: 1.5;
  color: #fff;
  text-transform: uppercase;
  width: 225px;
  height: 50px;
  border-radius: 25px;
  background: #57b846;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 25px;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}

.format_button:hover {
  cursor: pointer;
}

.format_button:disabled,
.format_button[disabled=disabled] {
  background-color: rgb(89, 88, 88);
  text-decoration: line-through;
}