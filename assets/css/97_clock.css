/* CREDIT: https://codepen.io/vaskopetrov/pen/yVEXjz */

.clock-header {  
  margin-top: -40px;  
}

.clock {
    background: #ececec;
    width: 300px;
    height: 300px;
    margin: 8% auto 0;
    border-radius: 50%;
    border: 14px solid #333;
    position: relative;
    box-shadow: 0 2vw 4vw -1vw rgba(0,0,0,0.8);    
  }
  
  .dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #ccc;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    position: absolute;
    z-index: 5;
    box-shadow: 0 2px 4px -1px black;
  }
  
  .hour-hand {
    position: absolute;
    z-index: 5;
    width: 4px;
    height: 65px;
    background: #333;
    top: 65px;
    transform-origin: 50% 72px;
    left: 50%;
    margin-left: -2px;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
  }
  
  .minute-hand {
    position: absolute;
    z-index: 6;
    width: 4px;
    height: 100px;
    background: #666;
    top: 30px;
    left: 50%;
    margin-left: -2px;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    transform-origin: 50% 105px;
  }
  
  .second-hand {
    position: absolute;
    z-index: 7;
    width: 2px;
    height: 120px;
    background: gold;
    top: 9px;
    lefT: 50%;
    margin-left: -1px;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
    transform-origin: 50% 125px;
  }
  
  .format-span {
    display: inline-block;
    position: absolute;
    color: #333;
    font-size: 22px;
    font-family: 'Poiret One';
    font-weight: 700;
    z-index: 4;
  }
  
  .h12 {
    top: 30px;
    left: 50%;
    margin-left: -9px;
  }
  .h3 {
    top: 125px;
    right: 30px;
  }
  .h6 {
    bottom: 15px;
    left: 50%;
    margin-left: -5px;
  }
  .h9 {
    left: 30px;
    top: 120px;
  }
  
  .diallines {
    position: absolute;
    z-index: 2;
    width: 2px;
    height: 15px;
    background: #666;
    left: 50%;
    margin-left: -1px;
    transform-origin: 50% 137px;
  }
  
  .diallines:nth-of-type(5n) {
    position: absolute;
    z-index: 2;
    width: 4px;
    height: 25px;
    background: #666;
    left: 50%;
    margin-left: -1px;
    transform-origin: 50% 137px;
  }
  
  .info {
    position: absolute;
    width: 120px;
    height: 20px;
    border-radius: 7px;
    background: #ccc;
    text-align: center;
    line-height: 20px;
    color: #000;
    font-size: 11px;
    top: 200px;
    left: 50%;
    margin-left: -60px;
    font-family: "Poiret One";
    font-weight: 700;
    z-index: 3;
    letter-spacing: 3px;
    margin-left: -60px;
    left: 50%;
  }
  .date {
      top: 80px;
    }

  .day {
      top: 175px;
  }

  .country {
    top: 205px;
  }
  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
}

.minutes-container, .hours-container, .seconds-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}



.minutes-container {
    animation: rotate 3600s infinite steps(60);
    /* transition: transform 0.3s cubic-bezier(.4,2.08,.55,.44);     */
}

.seconds-container {
    animation: rotate 60s infinite steps(60);    
    /* transition: transform 0.2s cubic-bezier(.4,2.08,.55,.44); */
}

.time{
  position: absolute;
  top: 120px;
  left: 18%;
  border: 1px solid #fff8dc;
  background-color: #fff;
  padding: 2px;
  display: flex;
  box-shadow: inset 0px 2px 5px rgba(0,0,0,.4);
  border-radius: 5px;
  min-width: 70px;
  height: 25px; 
  vertical-align: middle;
  z-index: 5;
}

.time small{
  color:red;
  transition: all 0.05s;
  transition-timing-function: cubic-bezier(0.1, 2.7, 0.58, 1);
}