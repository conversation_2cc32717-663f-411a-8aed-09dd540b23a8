/* Modern Version Page Styles */

:root {
    --version-primary: #1890ff;
    --version-primary-hover: #40a9ff;
    --version-secondary: #52c41a;
    --version-secondary-hover: #73d13d;
    --version-danger: #ff4d4f;
    --version-danger-hover: #ff7875;
    --version-warning: #faad14;
    --version-warning-hover: #ffc53d;
    --version-dark: #1f1f1f;
    --version-dark-light: #2f2f2f;
    --version-gray: #8c8c8c;
    --version-light-gray: #f5f5f5;
    --version-border: #d9d9d9;
    --version-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --version-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.25);
    --version-radius: 8px;
    --version-radius-small: 4px;
    --sidebar-width: 380px;
    --header-height: 80px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Layout */
.modern-version-layout {
    min-height: calc(100vh - 60px);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1; /* Ensure main layout stays below header menus */
}

/* Header Section */
.version-header {
    position: sticky;
    top: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--version-border);
    box-shadow: var(--version-shadow);
    padding: 20px 0;
    z-index: 50; /* Lower than main header (2000) to avoid conflicts */
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon {
    font-size: 32px;
    color: var(--version-primary);
}

.header-title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: var(--version-dark);
    letter-spacing: 0.5px;
}

.header-subtitle {
    margin: 4px 0 0 0;
    font-size: 16px;
    color: var(--version-gray);
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Main Content */
.version-content {
    flex: 1;
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    width: 100%;
    gap: 24px;
    padding: 24px;
    transition: var(--transition);
}

/* Modern Sidebar */
.modern-sidebar {
    width: var(--sidebar-width);
    background: white;
    border-radius: var(--version-radius);
    box-shadow: var(--version-shadow);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + 24px);
    transition: var(--transition);
    z-index: 5; /* Lower to avoid conflicts with main header menus */
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--version-border);
    background: var(--version-light-gray);
    border-radius: var(--version-radius) var(--version-radius) 0 0;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--version-radius-small);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(24, 144, 255, 0.1);
}

.toggle-icon {
    font-size: 20px;
    color: var(--version-primary);
}

.sidebar-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--version-dark);
}

/* Filter Sections */
.filter-section {
    padding: 24px;
    border-bottom: 1px solid var(--version-border);
}

.filter-section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.section-icon {
    font-size: 20px;
    color: var(--version-primary);
}

.section-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--version-dark);
}

/* Control Groups */
.control-group {
    margin-bottom: 20px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--version-dark);
}

.help-text {
    margin: 4px 0 8px 0;
    font-size: 12px;
    color: var(--version-gray);
    font-style: italic;
}

/* Modern Form Controls */
.modern-dropdown {
    width: 100%;
    border-radius: var(--version-radius-small) !important;
    border: 1px solid var(--version-border) !important;
    min-height: 40px !important;
}

.modern-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--version-border);
    border-radius: var(--version-radius-small);
    font-size: 14px;
    transition: var(--transition);
}

.modern-input:focus {
    outline: none;
    border-color: var(--version-primary);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-input-group {
    display: flex;
    gap: 8px;
}

.search-input-group .modern-input {
    flex: 1;
}

/* Modern Buttons */
.modern-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: var(--version-radius-small);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    min-height: 40px;
    justify-content: center;
}

.modern-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.modern-button.small {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 32px;
}

.modern-button.primary {
    background: var(--version-primary);
    color: white;
}

.modern-button.primary:hover:not(:disabled) {
    background: var(--version-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--version-shadow-hover);
}

.modern-button.secondary {
    background: var(--version-gray);
    color: white;
}

.modern-button.secondary:hover:not(:disabled) {
    background: #595959;
    transform: translateY(-1px);
    box-shadow: var(--version-shadow-hover);
}

.modern-button.success {
    background: var(--version-secondary);
    color: white;
}

.modern-button.success:hover:not(:disabled) {
    background: var(--version-secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--version-shadow-hover);
}

.button-icon {
    font-size: 16px;
}

/* Version State Details */
.version-state-details {
    margin-bottom: 20px;
}

.modern-summary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: var(--version-light-gray);
    border-radius: var(--version-radius-small);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.modern-summary:hover {
    background: rgba(24, 144, 255, 0.1);
}

.summary-icon {
    font-size: 16px;
    color: var(--version-primary);
}

.state-controls {
    padding: 16px 0 0 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Statistics */
.stats-container {
    margin-bottom: 16px;
}

.stats-alert {
    margin-bottom: 0 !important;
    font-size: 13px !important;
    padding: 12px !important;
    border-radius: var(--version-radius-small) !important;
}

/* Main Display Area */
.main-display-area {
    flex: 1;
    background: white;
    border-radius: var(--version-radius);
    box-shadow: var(--version-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: var(--version-light-gray);
    border-bottom: 1px solid var(--version-border);
}

.tab-header {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--version-gray);
    border-radius: var(--version-radius-small);
    transition: var(--transition);
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    color: var(--version-primary);
    background: rgba(24, 144, 255, 0.05);
}

.tab-button.active {
    color: var(--version-primary);
    background: white;
    border-bottom-color: var(--version-primary);
    font-weight: 600;
    box-shadow: var(--version-shadow);
}

.tab-icon {
    font-size: 16px;
}

.export-section {
    display: flex;
    gap: 12px;
}

.modern-toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: none;
    background: var(--version-light-gray);
    border-radius: var(--version-radius-small);
    cursor: pointer;
    transition: var(--transition);
    margin-right: 12px;
}

.modern-toggle-button:hover {
    background: rgba(24, 144, 255, 0.1);
}

.modern-toggle-button .toggle-icon {
    font-size: 18px;
    color: var(--version-primary);
}

/* Content Layers */
.content-layers {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.content-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    z-index: 0;
}

.content-layer:not(.active) {
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
}

.content-layer.active {
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 1;
}

.layer-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 24px;
    border-bottom: 1px solid var(--version-border);
    background: var(--version-light-gray);
}

.layer-icon {
    font-size: 24px;
    color: var(--version-primary);
}

.layer-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--version-dark);
}

.layer-content {
    flex: 1;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--version-gray);
    font-size: 16px;
}

/* Table Styles */
.table-container {
    padding: 0;
    display: block;
}

.table-wrapper {
    overflow-x: auto;
    max-height: calc(100vh - 300px);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.table-header-row th {
    background: var(--version-dark);
    color: white;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 3; /* Lower to avoid conflicts with main header menus */
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header-row th.sortable {
    cursor: pointer;
    user-select: none;
}

.table-header-row th.sortable:hover {
    background: var(--version-dark-light);
}

.sort-indicator {
    margin-left: 8px;
    font-size: 12px;
    opacity: 0.7;
    transition: var(--transition);
}

.table-header-row th.sortable:hover .sort-indicator {
    opacity: 1;
}

.modern-table tbody tr {
    border-bottom: 1px solid var(--version-border);
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: var(--version-light-gray);
}

.modern-table tbody td {
    padding: 12px 8px;
    vertical-align: top;
}

.empty-row {
    height: 200px;
}

.empty-message {
    text-align: center;
    color: var(--version-gray);
    font-style: italic;
    vertical-align: middle;
}

/* Mobile Responsive */
@media (max-width: 1200px) {
    .version-content {
        flex-direction: column;
        gap: 16px;
    }
    
    .modern-sidebar {
        width: 100%;
        position: static;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .tab-header {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .tab-button {
        font-size: 12px;
        padding: 8px 12px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .title-section {
        flex-direction: column;
        gap: 8px;
    }
    
    .header-title {
        font-size: 24px;
    }
    
    .version-content {
        padding: 16px;
    }
    
    .tab-navigation {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .tab-header {
        justify-content: center;
    }
    
    .export-section {
        justify-content: center;
    }
    
    .table-wrapper {
        max-height: calc(100vh - 400px);
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 16px;
    }
    
    .version-content {
        padding: 12px;
    }
    
    .filter-section {
        padding: 16px;
    }
    
    .layer-header {
        padding: 16px;
    }
    
    .layer-content {
        padding: 16px;
    }
    
    .modern-table {
        font-size: 12px;
    }
    
    .table-header-row th,
    .modern-table tbody td {
        padding: 8px 4px;
    }
}
