.fade-txt-message {
    /* 
    CREDIT: https://stackoverflow.com/questions/21388402/fade-out-after-div-content-has-been-shown-using-css
    animation: animation-name animation-duration animation-timing-function 
    animation-delay animation-iteration-count animation-direction;

    As per warning given in Edge browser, animation should be listed after -webkit-animation
    */
    -webkit-animation: fadeOut 20s 1;
    -webkit-animation-delay:15s; /* Safari and Chrome */
    -webkit-animation-fill-mode: forwards;
    animation: fadeOut  20s 1;    
    animation-fill-mode: forwards;    
    animation-delay:15s;    
}

@keyframes fadeOut  {
    from {opacity :1;}
    to {opacity :0;}
}

@-webkit-keyframes fadeOut  {
    from {opacity :1;}
    to {opacity :0;}
}