/* Modern Logout Menu and <PERSON> Styles */

/* User Menu Container */
.user-menu-container {
    position: relative;
    display: inline-block;
}

.user-menu-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.user-menu-trigger:hover {
    background: rgba(0, 0, 0, 0.05);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e1e5e9;
}

.user-name {
    font-weight: 500;
    font-size: 14px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dropdown-icon {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.user-menu-trigger.active .dropdown-icon {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e1e5e9;
    min-width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

.user-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
}

.user-dropdown-header .user-avatar {
    width: 48px;
    height: 48px;
}

.user-info {
    flex: 1;
}

.user-display-name {
    font-weight: 600;
    font-size: 16px;
    color: #1a1a1a;
    margin-bottom: 2px;
}

.user-email {
    font-size: 13px;
    color: #666;
    opacity: 0.8;
}

.dropdown-divider {
    margin: 0;
    border: none;
    height: 1px;
    background: #e1e5e9;
}

.dropdown-menu-items {
    padding: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #1890ff;
}

.dropdown-item.logout-item:hover {
    background: #fff2f0;
    color: #ff4d4f;
}

.item-icon {
    font-size: 18px;
    opacity: 0.7;
}

.dropdown-item:hover .item-icon {
    opacity: 1;
}

/* Modern Logout Page */
.modern-logout-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.logout-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.logout-content {
    background: white;
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 480px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    animation: slideUp 0.8s ease-out;
    position: relative;
    z-index: 1;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logout-icon-container {
    margin-bottom: 2rem;
    animation: iconPulse 2s ease-in-out infinite;
}

.logout-icon {
    font-size: 4rem;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.logout-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 1rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.logout-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logout-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.logout-primary-button,
.logout-secondary-button {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: inline-block;
}

.logout-primary-button {
    background: #1890ff;
    color: white;
}

.logout-primary-button:hover {
    background: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
}

.logout-secondary-button {
    background: transparent;
    color: #666;
    border-color: #d9d9d9;
}

.logout-secondary-button:hover {
    border-color: #1890ff;
    color: #1890ff;
    transform: translateY(-2px);
}

.logout-footer {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.logout-security-note {
    font-size: 0.9rem;
    color: #999;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.logout-countdown {
    font-size: 0.9rem;
    color: #1890ff;
    font-weight: 500;
    padding: 8px 16px;
    background: rgba(24, 144, 255, 0.1);
    border-radius: 6px;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logout-content {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .logout-title {
        font-size: 1.5rem;
    }
    
    .logout-subtitle {
        font-size: 1rem;
    }
    
    .logout-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .logout-primary-button,
    .logout-secondary-button {
        width: 100%;
        max-width: 200px;
    }
    
    .user-dropdown-menu {
        min-width: 260px;
        right: -20px;
    }
}

@media (max-width: 480px) {
    .logout-icon {
        font-size: 3rem;
    }
    
    .logout-title {
        font-size: 1.3rem;
    }
    
    .user-name {
        display: none;
    }
    
    .user-dropdown-menu {
        min-width: 240px;
        right: -40px;
    }
}
