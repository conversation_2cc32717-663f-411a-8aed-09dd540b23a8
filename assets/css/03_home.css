/*
.mb-30 {
    margin-bottom: 30px;
  }
*/

  .card {
    padding: 16px 24px;
    background: #ffffff;
    height: 100%;
    position: relative;
    border: none;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    border: 2px solid transparent;
    transition: all 0.3s ease-in-out;
    z-index: 5;
  }
  .card h3.card-title {
    font-weight: 700;
    font-size: 1.3rem;
    color: #00106a;
  }
  .card p {
    color: #989dc5;
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 72px;
  }
  .card .card-link {
    position: absolute;
    bottom: 18px;
  }
  .card:hover {
    transform: translateY(-15px);
    box-shadow: 0 1rem 1.5rem rgba(0, 0, 0, 0.15);
    cursor: pointer;
  }
  .card .card-icon {
    width: 60px;
    margin-bottom: 8px;
    position: relative;
    top: 0;
    left: -12px;
  }
  
  .card label {
    color: #989dc5;
    font-family: inherit;
    font-size: medium;
    font-weight: 400;    
    line-height: 1.5;    
  }