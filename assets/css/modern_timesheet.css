/* Modern Timesheet Layout Styles */

:root {
    --timesheet-primary: #1890ff;
    --timesheet-primary-hover: #40a9ff;
    --timesheet-secondary: #52c41a;
    --timesheet-secondary-hover: #73d13d;
    --timesheet-danger: #ff4d4f;
    --timesheet-danger-hover: #ff7875;
    --timesheet-warning: #faad14;
    --timesheet-warning-hover: #ffc53d;
    --timesheet-dark: #1f1f1f;
    --timesheet-dark-light: #2f2f2f;
    --timesheet-gray: #8c8c8c;
    --timesheet-light-gray: #f5f5f5;
    --timesheet-border: #d9d9d9;
    --timesheet-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --timesheet-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.25);
    --timesheet-radius: 8px;
    --timesheet-radius-small: 4px;
    --sidebar-width: 380px;
    --header-height: 70px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Layout */
.modern-timesheet-layout {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    height: calc(100vh - 60px); /* Account for main header */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.modern-timesheet-layout.sidebar-collapsed {
    grid-template-columns: 0 1fr;
}

/* Sidebar Styles */
.modern-sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--timesheet-border);
    overflow-y: auto;
    overflow-x: hidden;
    transition: var(--transition);
    box-shadow: var(--timesheet-shadow);
    z-index: 10;
}

.modern-sidebar.hidden {
    transform: translateX(-100%);
    opacity: 0;
}

.sidebar-header {
    padding: 20px;
    background: linear-gradient(135deg, var(--timesheet-primary), var(--timesheet-primary-hover));
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-icon {
    font-size: 24px;
    opacity: 0.9;
}

.sidebar-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-section {
    padding: 20px;
    border-bottom: 1px solid var(--timesheet-border);
}

.sidebar-section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 16px;
}

.section-icon {
    font-size: 20px;
    color: var(--timesheet-primary);
}

.section-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--timesheet-dark);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--timesheet-dark);
    margin-bottom: 4px;
}

/* Modern Checkbox */
.modern-checkbox {
    margin: 0;
}

.modern-checkbox .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--timesheet-radius-small);
    transition: var(--transition);
    cursor: pointer;
}

.modern-checkbox .checkbox-label:hover {
    background: var(--timesheet-light-gray);
}

.checkbox-icon {
    font-size: 16px;
    color: var(--timesheet-primary);
}

/* Modern Date Picker */
.modern-date-picker {
    border-radius: var(--timesheet-radius-small);
    border: 1px solid var(--timesheet-border);
    transition: var(--transition);
}

.modern-date-picker:focus-within {
    border-color: var(--timesheet-primary);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Modern Dropdown */
.modern-dropdown .Select-control {
    border-radius: var(--timesheet-radius-small);
    border: 1px solid var(--timesheet-border);
    transition: var(--transition);
    min-height: 40px;
}

.modern-dropdown .Select-control:hover {
    border-color: var(--timesheet-primary-hover);
}

.modern-dropdown .is-focused .Select-control {
    border-color: var(--timesheet-primary);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Modern Buttons */
.modern-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: var(--timesheet-radius-small);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    min-height: 40px;
    justify-content: center;
}

.modern-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.modern-button.primary {
    background: var(--timesheet-primary);
    color: white;
}

.modern-button.primary:hover:not(:disabled) {
    background: var(--timesheet-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--timesheet-shadow-hover);
}

.modern-button.secondary {
    background: var(--timesheet-gray);
    color: white;
}

.modern-button.secondary:hover:not(:disabled) {
    background: #595959;
    transform: translateY(-1px);
    box-shadow: var(--timesheet-shadow-hover);
}

.modern-button.success {
    background: var(--timesheet-secondary);
    color: white;
}

.modern-button.success:hover:not(:disabled) {
    background: var(--timesheet-secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--timesheet-shadow-hover);
}

.button-icon {
    font-size: 16px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

/* Modern Upload */
.modern-upload {
    border: 2px dashed var(--timesheet-border);
    border-radius: var(--timesheet-radius);
    padding: 24px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--timesheet-light-gray);
}

.modern-upload:hover {
    border-color: var(--timesheet-primary);
    background: rgba(24, 144, 255, 0.05);
}

.upload-icon {
    font-size: 32px;
    color: var(--timesheet-primary);
    margin-bottom: 8px;
}

.upload-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--timesheet-dark);
    margin-bottom: 4px;
}

.upload-subtext {
    font-size: 12px;
    color: var(--timesheet-gray);
}

.upload-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    justify-content: center;
}

/* Modern Alert */
.modern-alert {
    border-radius: var(--timesheet-radius-small);
    border: none;
    box-shadow: var(--timesheet-shadow);
}

/* Main Content Area */
.modern-main-content {
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
    position: relative;
}

.modern-main-content.expanded {
    grid-column: 1 / -1;
}

/* Content Header */
.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid var(--timesheet-border);
    box-shadow: var(--timesheet-shadow);
    z-index: 5;
    min-height: var(--header-height);
}

.modern-toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--timesheet-border);
    border-radius: var(--timesheet-radius-small);
    background: white;
    cursor: pointer;
    transition: var(--transition);
}

.modern-toggle-button:hover {
    background: var(--timesheet-light-gray);
    border-color: var(--timesheet-primary);
}

.modern-toggle-button.active {
    background: var(--timesheet-primary);
    border-color: var(--timesheet-primary);
    color: white;
}

.toggle-icon {
    font-size: 20px;
    transition: var(--transition);
}

.modern-toggle-button.active .toggle-icon {
    transform: rotate(180deg);
}

.page-title-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    margin-left: 20px;
}

.title-icon {
    font-size: 28px;
    color: var(--timesheet-primary);
}

.page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--timesheet-dark);
    letter-spacing: 0.5px;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Content Body */
.content-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background: var(--timesheet-light-gray);
    border-bottom: 1px solid var(--timesheet-border);
    padding: 0 24px;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--timesheet-gray);
    border-bottom: 3px solid transparent;
    transition: var(--transition);
}

.tab-button:hover {
    color: var(--timesheet-primary);
    background: rgba(24, 144, 255, 0.05);
}

.tab-button.active {
    color: var(--timesheet-primary);
    border-bottom-color: var(--timesheet-primary);
    background: white;
}

.tab-icon {
    font-size: 16px;
}

/* Tab Content */
.tab-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.tab-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tab-panel.active {
    opacity: 1;
    visibility: visible;
}

.panel-header {
    padding: 24px;
    background: white;
    border-bottom: 1px solid var(--timesheet-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--timesheet-dark);
}

.panel-description {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: var(--timesheet-gray);
}

/* Data Grid Container */
.data-grid-container {
    flex: 1;
    padding: 24px;
    overflow: hidden;
}

.modern-grid {
    height: 100%;
    border-radius: var(--timesheet-radius);
    overflow: hidden;
    box-shadow: var(--timesheet-shadow);
}

/* Chart Container */
.chart-container {
    flex: 1;
    padding: 24px;
    overflow: hidden;
}

.chart-container > div {
    height: 100%;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .modern-timesheet-layout {
        grid-template-columns: 1fr;
        position: relative;
    }
    
    .modern-sidebar {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
    }
    
    .modern-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .content-header {
        padding: 16px;
    }
    
    .page-title {
        font-size: 20px;
    }
    
    .tab-navigation {
        padding: 0 16px;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .tab-navigation::-webkit-scrollbar {
        display: none;
    }
    
    .tab-button {
        white-space: nowrap;
        padding: 12px 16px;
    }
    
    .panel-header {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .data-grid-container,
    .chart-container {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .modern-sidebar {
        width: 100vw;
    }
    
    .sidebar-section {
        padding: 16px;
    }
    
    .content-header {
        padding: 12px;
    }
    
    .page-title-section {
        margin-left: 12px;
    }
    
    .page-title {
        font-size: 18px;
    }
    
    .header-actions .modern-button {
        padding: 8px 12px;
        font-size: 12px;
    }
}
