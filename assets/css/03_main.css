/*
main

Large, **in charge**, and centered. (this is the description of your component. you can use markdown in here.)

Markup (define the markup to be used in your styleguide):
<h1 class="post-title">A Post Title</h1>

Styleguide Components.article.post-title 
(ꜛ this controls the organization of your style guide. Here, I'm filing this component inside Components / Article / Post Title)


Button

Your standard button suitable for clicking.

:hover    - Highlights when hovering.
:disabled - Dims the button when disabled.
.primary  - Indicates button is the primary action.
.smaller  - A little bit smaller now.

Styleguide 3.0
*/

:root {
    --header-height: 60px;
    --panel-width: 350px;
    --gutter: 40px;
}

.page-grid {
    position: relative;
    display: grid;
    height: calc(100vh - var(--header-height));
    grid-template-columns: var(--panel-width) 1fr;        
    background-color: #A4A4A4;        
}

@media (max-width: 768px) {
    .page-grid {
        grid-template-columns: 1fr; /* Make single column on smaller screens */
    }
}

.page-grid > div {
    /* background-color: rgba(255, 255, 255, 0.8); */
    background-color: #1b1f34;     
    text-align: center;
    /* padding: 10px 0;  */
    /* margin: 5px 0 5px 5px; */
    margin-right: 5px;
    margin-bottom: 10px;
    margin-top: 5px;
    margin-left: 5px;
}

/*
.page-grid > div:nth-child(1) {
    display: none;    
}

.page-grid > div:nth-child(2) {
    grid-column: 1 / 3;    
}

*/

.side-menu {    
    position: relative;    
    z-index: 5;    
    margin-top: 2px;   
    display: grid;     
    grid-template-rows: repeat(18, auto-fit);    
    justify-content: center;    
    overflow-y: auto;
} 

@media (max-width: 768px) {
    .side-menu {
        grid-template-rows: repeat(10, auto-fit); /* Adjust number of rows on smaller screens */
    }
}


.side-menu.hidden {    
    display: none;
}

.side-menu > div {
    /* background-color: rgba(255, 255, 255, 0.8); */
    /* padding: 3px 0; */
    padding-bottom: 2px;    
}

.side-menu > label {    
    display: flex;
    align-self: flex-end;
    justify-content: center;
    color: white;       
    font-family: "Rajadhani";
    text-transform: uppercase;
    font-size: x-small;
    font-weight: 400;       
}

.details {
    color: white;
}

.format_summary {
    color: white;
    font-family: "Rajadhani";
    text-transform: uppercase;
    font-size: 14px;
    font-weight: normal; 
}

.display-area {       
    color: white;
    display: grid;        
    grid-template-columns: var(--gutter) 1fr var(--gutter);
    grid-template-rows: 40px 1fr 40px;
    grid-template-areas:
    "c1 c2 c3",
    "c4 c5 c6",
    "c7 c8 c9";
    align-content: start;   
    overflow-x: hidden;     
}

@media (max-width: 768px) {
    .display-area {
        grid-template-columns: 1fr; /* Single column on smaller screens */
        grid-template-rows: auto;
        grid-template-areas:
        "c1"
        "c2"
        "c3"
        "c4"
        "c5"
        "c6"
        "c7"
        "c8"
        "c9";
    }
}

.display-area.expand-grid {
    grid-column: 1 / 3;  
}


.format-button {
    grid-area: "c1";
    position: relative;
    top: 5px;
    border-radius: 4px;
    background-color: #5ca1e1;
    border: none;
    color: #fff;
    text-align: center;            
    transition: all 0.5s;
    cursor: pointer;    
    box-shadow: 0 10px 20px -8px rgba(0, 0, 0,.7);    
}

/* https://stackoverflow.com/questions/14736496/use-font-awesome-icons-in-css */
.format-button::before {
    content: "\f100";
    font-family: "FontAwesome";
    font-weight: 400;
}

.format-button.active::before {
    content: "\f101";
    font-family: "FontAwesome";
    font-weight: 400;    
}

.format-button:hover {
    padding-right: 8px;
    padding-left:8px;
}

.format-serach-button {
    top: 5px;
    border-radius: 4px;
    margin-left: 5px;        
    background-color: #5ca1e1;
    border: none;
    color: #fff;
    text-align: center;            
    transition: all 0.5s;
    cursor: pointer;    
    box-shadow: 0 10px 20px -8px rgba(0, 0, 0,.7);  
}

.format-serach-button::before {
    content: "\f002";
    font-family: "FontAwesome";
    font-weight: 400;
}

.format-export-button {
    top: 5px;
    border-radius: 4px;
    margin-right: 5px;        
    margin-top: 5px;
    background-color: #5ca1e1;
    border: none;
    color: #fff;
    text-align: center;            
    transition: all 0.5s;
    cursor: pointer;    
    box-shadow: 0 10px 20px -8px rgba(0, 0, 0,.7);
}

.format-export-button::before {
    content: "\f56e";
    font-family: "FontAwesome";
    font-weight: 400;
}


.header-menu {
    grid-area: "c2";
    position: relative;
    
}

.header-right-side {
    grid-area: "c3";
    position: relative;
 
}

.middle-vacant {
    grid-area: "c4";
    position: relative;       
}

.main-area {
    grid-area: "c5";
    position: relative;
    border: 1px solid red;
}

.main-area .layer {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    visibility: hidden;
    display: none;    
}

.main-area .layer.show {
    visibility: visible;    
    animation: backInLeft;    
    animation-duration: 1s;    
   display: initial;
}


.main-area .layer.one {
    background: #dc6c5f;
    display: grid;
    grid-template-rows: 3em auto auto;
}
  
.main-area .layer.two {            
    background: gray;
    color: white;    
    overflow-x: auto;    
}

.main-area .layer.three {    
    background-color: gray;        
    color: white;    
    overflow-x: auto; 
}

.main-area .layer.four {
    height: 100%;    
    background: gray; 
    color: snow;   
    overflow-x: auto;     
    display: grid;
    grid-template-columns: 1fr repeat(28, 2em) 5em;    
    grid-template-rows: 3em repeat(auto-fill, minmax(3em, 1fr));        
}

.main-area .layer.five {   
    background-color: gray;   
    color: white;    
    overflow-x: auto; 
}

.main-area .layer.six {    
    background-color: transparent;   
    overflow-x: auto; 
    color: whitesmoke;
}

.main-area .layer.seven {    
    background-color: transparent;   
    overflow-x: auto; 
    color: whitesmoke;
    font-size: 12px;
    font-family: Arial, Helvetica, sans-serif;
}

.main-area .layer.eight {    
    background-color:  #54a9ba;   
    overflow-y: auto; 
    color: white;
}

.display-area-3level .layer.ten {  
    display: grid !important;  
    background-color: transparent;   
    overflow-x: auto; 
    color: whitesmoke;
    font-size: 12px;
    font-family: Arial, Helvetica, sans-serif;
    grid-template-columns: auto auto;    
    grid-row: auto auto;
    grid-column-gap: 5px;
    grid-row-gap: 5px;    
    margin: 2px auto;
    box-sizing: border-box; 
}

.box {
    display: flexbox;
    position: relative;  
    background-color: olivedrab;
    padding:20px;
    border-radius:10px;
    color:#fff;
    align-items:center;
    justify-content:center;
    font-size:40px;
    font-family:sans-serif; 
    width: 100%;   
    height: 100%;
}

.days-grid {
    height: 100%;
    position: relative;    
    grid-column-gap: 1px;
    grid-row-gap: 1px;    
    font-size: 12px;
    border: 1px solid white;
    font-size: 13px;    
    font-family: "Roboto";    
}

.highlightrow {
    background-color: #FF8000;    
}

.weekend {
    background-color: #BDBDBD;
}

.main-area .layer.four > div:nth-child(30n+1) {
    text-align: left;    
    text-transform: capitalize;    
    padding-left: 5px;
    padding-top: 5px;    
}

.side-vacant {
    grid-area: "c6";
    position: relative;      
}

.bottom-left {
    grid-area: "c7";
    position: relative;    
}

.bottom-middle {
    grid-area: "c8";
    position: relative;
    
}

.bottom-right {
    grid-area: "c9";
    position: relative;    
}

.grid4x4 {  
    display: grid !important;  
    background-color: transparent;   
    overflow-x: auto; 
    color: whitesmoke;
    font-size: 12px;
    font-family: Arial, Helvetica, sans-serif;
    grid-template-columns: auto auto;    
    grid-row: auto auto;
    grid-column-gap: 5px;
    grid-row-gap: 5px;    
    margin: 2px auto;
    box-sizing: border-box; 
}

.swiper {
    width: 95vw;
    height: 90vh;
}

.container-swiper {      
    display: flex;
    /* bottom: 0;
    left: 0;
    right: 0;  */
    /* transform: translateY(20%); */
    
    /* align-items: baseline; */
    justify-content: center;
    /* padding: 20px; */
    text-align: center;
    /* opacity: 0;
    visibility: hidden; */
    color: white;
    /* background: rgba(0, 0, 0, 0.5); */
    background: #6697c0;
    transition: all 0.4s;
    border: 2px solid red;
    overflow-y: scroll;    
}

.swiper-button-prev {
    color: white;	
	background: rgba(255,255,255,0.2);
	position: absolute;
	padding: 1rem;
	font-size: 3rem;
	height: 10rem;
	vertical-align: middle;
	line-height: 8rem;
	top: 50%;
	transform: translate3d(0, -50%, 0);
	cursor: pointer;  
    left: 0;    
    border-top-right-radius: 50%;
	border-bottom-right-radius: 50%;     
}

.swiper-button-next {
    right: 100%;
	border-top-left-radius: 50%;
	border-bottom-left-radius: 50%;    
    color: white;	
	background: rgba(255,255,255,0.2);
	position: absolute;
	padding: 1rem;
	font-size: 3rem;
	height: 10rem;
	vertical-align: middle;
	line-height: 8rem;
	top: 50%;
	transform: translate3d(0, -50%, 0);
	cursor: pointer;    
    right: 0px;    
}

.swiper-pagination {
	position: absolute;
	bottom: 0;
	right: 0;
    transform: translate(-50%, -50%);
    padding-bottom: 2px;
    
	width: auto !important;
	left: auto !important;
	margin: 0;
}

.swiper-pagination-bullet {
    color: black;
    position: relative;   
    padding: 8px 8px;         
    font-size: medium;
}

.swiper-pagination-bullet-active {
	color:#fff;
	background: #dc6c5f;    
    
}

.format-caption {    
    color: white;    
    justify-content: center; 
}

.mermaid-diagram {
    position: relative;
    border: 2px solid white;
    vertical-align: middle;
    justify-content: center;       
}

.align-middle {
    position: absolute;    
    top: 50%;
    left: 50%;    
    transform: translate(-50%, -50%);
    width: 90%;    
    /* overflow: auto;         */
}

/* Creates left and right arrows */
/*
.middle-vacant::after {
    content: "\276C";	
	border-top-right-radius: 50%;
	border-bottom-right-radius: 50%;      
}

.middle-vacant::after {
    color: white;	
	background: rgba(255,255,255,0.2);
	position: absolute;
	padding: 1rem;
	font-size: 3rem;
	height: 10rem;
	vertical-align: middle;
	line-height: 8rem;
	top: 50%;
	transform: translate3d(0, -50%, 0);
	cursor: pointer;  
    left: 0; 
}

.side-vacant::before {
    content: "\276D";
	right: 100%;
	border-top-left-radius: 50%;
	border-bottom-left-radius: 50%;
}

.side-vacant::before
{
    color: white;	
	background: rgba(255,255,255,0.2);
	position: absolute;
	padding: 1rem;
	font-size: 3rem;
	height: 10rem;
	vertical-align: middle;
	line-height: 8rem;
	top: 50%;
	transform: translate3d(0, -50%, 0);
	cursor: pointer;    
    right: 0px;
}

*/