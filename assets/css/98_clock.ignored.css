.post-content img, .post-content .demo-container {
    border-radius: 0.5em;
    margin: 2em -35% 2em 0;
}

.demo-container.clocks {
    background: rgba(200,200,200,.9);
    padding: 4em 0 2em 1em;
}

.demo-container.clocks.single {
    padding: 2em;
    display: inline-block;
}

.demo-container.clocks.single .clock {
    height: 20em;
    width: 20em;
    padding: 0;
    margin: 0;
}

.clock.simple {
    background: #fff url(../img/ios_clock.svg) no-repeat center;
    background-size: 88%;
}

.clock.show {
    opacity: 1;
    -webkit-transition: all 2.5s cubic-bezier(.12,1.03,.34,1);
    transition: all 2.5s cubic-bezier(.12,1.03,.34,1);
}

.clock {
    border-radius: 50%;
    background: radial-gradient(#000,#000 0.1em,#fff 0.1em,#fff),#fff;
    display: inline-block;
    padding-bottom: 31.333%;
    position: relative;
    width: 31.333%;
    opacity: 0;
}

.clock::after {
    background: red;
    border-radius: 50%;
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 4%;
    height: 4%;
    z-index: 10;
}

.clock.simple:after {
    background: #000;
    border-radius: 50%;
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 5%;
    height: 5%;
    z-index: 10; 
}


.minutes-container, .hours-container, .seconds-container {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.hours {
    background: #000;
    height: 25%;
    left: 48.75%;
    position: absolute;
    top: 25%;
    transform-origin: 50% 100%;
    width: 2.5%;
}

.minutes {
    background: #000;
    height: 40%;
    left: 49%;
    position: absolute;
    top: 10%;
    transform-origin: 50% 100%;
    width: 2%;
}

.seconds {  
    background: #000;
    height: 45%;
    left: 49.5%;
    position: absolute;
    top: 14%;
    transform-origin: 50% 80%;
    width: 1%;
    z-index: 8;
}

@keyframes rotate {
    100% {
      transform: rotateZ(360deg);
    }
}

.hours-container {
    animation: rotate 3600s infinite steps(5);
}

.minutes-container {
    animation: rotate 3600s infinite steps(60);
    /* transition: transform 0.3s cubic-bezier(.4,2.08,.55,.44);     */
}

.seconds-container {
    animation: rotate 60s infinite steps(60);    
    /* transition: transform 0.2s cubic-bezier(.4,2.08,.55,.44); */
}