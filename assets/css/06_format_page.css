.single-page-layout {
    background-color: gray !important;    
    color: white;
    width: 30%;
    align-content: center;
    margin-top: 10%;
    margin-left: 25%;
    
}

.error {
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(255, 130, 45, 0.85);
    font-family: 'Montserrat', sans-serif;
    color: #fff;
    text-align: center;
    padding: 16px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%)
}

.error h1 {
    margin: -10px 0 -30px;
    font-size: calc(17vw + 40px);
    opacity: .8;
    letter-spacing: -17px;
}
  
.error p {
    opacity: .8;
    font-size: 20px;
    margin: 8px 0 38px 0;
    font-weight: bold
}

.no-content {
    position: relative;
    background: lightgray;
    text-align: center;
    font-family: "Roboto";
    font-weight: bold;
    font-size: small;
    color: brown;
    margin-top: 20%;
    padding: 20px;
}

.no-content::before {
    position: absolute; 
}