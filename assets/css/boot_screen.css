/* Boot Screen Styles */
.boot-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.boot-screen.boot-screen-hidden {
    opacity: 0;
    visibility: hidden;
}

.boot-screen-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0.2) 100%);
    pointer-events: none;
}

.boot-screen-content {
    text-align: center;
    color: white;
    z-index: 1;
    animation: bootFadeIn 1s ease-out;
}

.boot-logo-container {
    margin-bottom: 2rem;
    animation: bootPulse 2s ease-in-out infinite;
}

.boot-logo-svg {
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
}

.boot-logo-bg {
    animation: bootRotate 3s linear infinite;
    transform-origin: 60px 60px;
}

.boot-logo-circle {
    animation: bootScale 2s ease-in-out infinite alternate;
}

.boot-logo-vertical,
.boot-logo-horizontal {
    animation: bootGlow 1.5s ease-in-out infinite alternate;
}

.boot-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    animation: bootSlideUp 1s ease-out 0.3s both;
}

.boot-loading-container {
    margin-bottom: 2rem;
    animation: bootSlideUp 1s ease-out 0.6s both;
}

.boot-loading-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.boot-loading-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.boot-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: white;
    animation: bootDotBounce 1.4s ease-in-out infinite both;
}

.boot-dot:nth-child(1) { animation-delay: -0.32s; }
.boot-dot:nth-child(2) { animation-delay: -0.16s; }
.boot-dot:nth-child(3) { animation-delay: 0s; }

.boot-progress-container {
    width: 300px;
    height: 4px;
    background-color: rgba(255,255,255,0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
    animation: bootSlideUp 1s ease-out 0.9s both;
}

.boot-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #40A9FF, #1890FF);
    border-radius: 2px;
    width: 0%;
    animation: bootProgress 3s ease-out forwards;
    box-shadow: 0 0 10px rgba(64, 169, 255, 0.5);
}

/* Hide login form initially */
.login-form-hidden {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease-in, visibility 0.5s ease-in;
}

.login-form-visible {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.5s ease-in, visibility 0.5s ease-in;
}

/* Animations */
@keyframes bootFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bootSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bootPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bootRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bootScale {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.1);
    }
}

@keyframes bootGlow {
    from {
        filter: drop-shadow(0 0 5px rgba(255,255,255,0.5));
    }
    to {
        filter: drop-shadow(0 0 15px rgba(255,255,255,0.8));
    }
}

@keyframes bootDotBounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bootProgress {
    0% {
        width: 0%;
    }
    30% {
        width: 30%;
    }
    60% {
        width: 60%;
    }
    90% {
        width: 85%;
    }
    100% {
        width: 100%;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .boot-title {
        font-size: 2rem;
    }
    
    .boot-logo-svg {
        width: 80px;
        height: 80px;
    }
    
    .boot-progress-container {
        width: 250px;
    }
}

@media (max-width: 480px) {
    .boot-title {
        font-size: 1.5rem;
    }
    
    .boot-logo-svg {
        width: 60px;
        height: 60px;
    }
    
    .boot-progress-container {
        width: 200px;
    }
}
