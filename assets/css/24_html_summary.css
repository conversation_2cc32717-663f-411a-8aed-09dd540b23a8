.rn-details {
    border-bottom: 1px solid #b5bfd9;  
    color: white;  
}

.details-hide {
    display: none;
}

.details-show {
    display: inline;
}

.rn-details summary {
    background-color: bisque;
    color: black;
    cursor: pointer;
    padding: 0.5em .5em;  
    margin-bottom: 10px;       
}

.rn-details summary:hover {
    background-color: #f2f5f9;
}

.rn-details div {
    display: flex;
	align-items: center;    
    margin-right: 10px;    
}

.rn-details div > label {
    color: white;
    padding-right: 10px;
}

.rn-details ul {
    list-style: none;
    display: inline-block;
    align-items: flex-start;
}

.rn-details ul > li > label {
    display: block;
    text-align: center;
    color: white;
}

.cls-format-rn-ul {
    list-style: none;
    display: inline-block;
    align-items: flex-start;
}

.cls-format-rn-ul li {
    display: flex;       
}

.cls-format-rn-ul li > div {    
    border: 1px solid;
}

.cls-format-rn-ul li > label {
    display: block;
    text-align: center;
    color: white;
}