.progressbar-container {
  background-color: white;
}

.progressbar {
  margin-bottom: 30px;
  overflow: hidden;  
  border: 1px dotted red;  
  position: sticky;
  z-index: 2;
}

.progressbar li {
  list-style-type: none;
  color: #99a2a8;  
  font-size: 9px;
  width: calc(100%/4);
  float: left;
  position: relative;
  font: 500 13px/1 'Roboto', sans-serif;    
}

.progressbar li:before {
  content: "\f007";
  font-family: "FontAwesome";  
  width: 50px;
  height: 50px;
  line-height: 50px;
  display: block;
  background: #eaf0f4;
  border-radius: 50%;
  margin: 0 auto 10px auto;   
  text-align: center;     
}

.progressbar li:after {
  content: '';
  width: 100%;
  height: 10px;
  background: #eaf0f4;
  position: absolute;
  left: -50%;
  top: 21px;
  z-index: -1;
} 

.progressbar li:last-child:after {
  width: 150%;
}

.progressbar li:nth-child(2):before {
  content: "\f023";
  font-family: "FontAwesome";  
}

.progressbar li:nth-child(3):before {
  content: "\f574";
  font-family: "FontAwesome";  
  
}


.progressbar li:nth-child(4):before {
  content: "\f164";
  font-family: "FontAwesome";  
}

.progressbar li.active {
  color: #5cb85c;
}

.progressbar li.active::before, .progressbar li.active::after {
  background: #5cb85c;
  color: white;  
}

.login-container-ul {
  overflow: hidden;    
  margin-left: 0px;
  padding-left: 0px;    
  padding-top: 5px;  
}

.login-container-ul li {
  list-style-type: none;
  color: #99a2a8;    
  font-size: 9px;  
  float: left;
  position: relative;
  /* font: 500 13px/1 'Roboto', sans-serif;   */
  /* border: 1px solid yellow;   */
}

.login-container-ul li:first-child {
  width: 100%;
}

.green {
  color: green;
}

.red {
  color: red;
}

.check-container {
  /* line-height: 65px; */
  /* display: block; */
  display: flex;

  width: 50px;
  height: 50px;  
  
  background: #eaf0f4;
  border-radius: 50%;
  margin: 0 auto 10px auto;   
  text-align: center;    
  align-items: center;  
  justify-content: center;
}

.login {    
    display: relative;    
    justify-content: center;
    color: white;       
    font-family: "Rajadhani";
    text-transform: uppercase;
    font-size: x-small;
    font-weight: 400;       
}

.wrap-input-creds {
    position: relative;
    width: 100%;
    z-index: 1;
    margin-bottom: 10px;        
  }

.input-creds {
    width: calc(100% - 22px); /* Adjust width minus padding */
    font-family: Poppins-Medium;
    font-size: 15px;
    line-height: 1.5;
    color: #666666;

    display: block;
    width: 100%;
    background: #e6e6e6;
    height: 50px;
    border-radius: 25px;
    box-sizing: border-box; /* Ensures padding is included in width calculation */
    padding: 0 30px 0 68px;    
}

.focus-input-creds {
    display: block;
    position: absolute;
    border-radius: 25px;
    bottom: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    box-shadow: 0px 0px 0px 0px;
    color: rgba(87,184,70, 0.8);    
}

.input-creds:focus + .focus-input-creds {
    -webkit-animation: anim-shadow 0.5s ease-in-out forwards;
    animation: anim-shadow 0.5s ease-in-out forwards;
  }
  
  @-webkit-keyframes anim-shadow {
    to {
      box-shadow: 0px 0px 70px 25px;
      opacity: 0;
    }
  }
  
  @keyframes anim-shadow {
    to {
      box-shadow: 0px 0px 70px 25px;
      opacity: 0;
    }
  }


.symbol-input-creds {
    font-size: 15px;

    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    position: absolute;
    border-radius: 25px;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding-left: 35px;
    pointer-events: none;
    color: #666666;
  
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}

.input-creds:focus + .focus-input-creds + .symbol-input-creds {
    color: #57b846;
    padding-left: 28px;
  }
/* 
  .logout_icon {
    color: orange;
    background-color: #57b846;
    z-index: 50;
  }

  .logout_icon::before {
    content: '\f2f5';
    font-family: "FontAwesome";
    font-weight: 400;
    border-radius: 50%;
    position: relative !important;
  }
   */