/* Create a two-column layout */
.column {
    float: left;
    width: 50%;
    padding: 5px;    
}


.row {
    margin-left: 5px;
    margin-right: 5px;    
}

/* Clearfix (clear floats) */
.row::after {
    content: "";
    clear: both;
    display: table;
  }

.custom-table {
    width: 100%;
}

.custom-table td {
    width: 50%;
}

.format-table {
    font-family: "Roboto";
    font-size: small;
    font-weight: 300;
    border-collapse: collapse;
    width: 100%;
}

.format-table td,
.format-table th {
    border: 1px solid #ddd;
    padding: 8px;
}

.format-table th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: center;
    background-color: #04AA6D;
    color: white;
    position: sticky;
    top: 0;
}

.format-table td {
    text-align: left;
}

.format-table .initiative {
    background-color: #8A4B08;
}

.format-table tr:nth-child(even) {
    background-color: #2E2E2E;
}

.styled-table {
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 0.9em;
    font-family: sans-serif;
    min-width: 400px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.styled-table {
    background-color: slategray;
    color: #ffffff;
    text-align: left;
}

.styled-table th,
.styled-table td {
    padding: 12px 15px;
}

.styled-table th {
    background-color:#007bff;
}
.styled-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tbody tr:last-of-type {
    border-bottom: 2px solid #009879;
}

.styled-table tbody tr.active-row {
    font-weight: bold;
    color: #009879;
}

.format-table tr:hover {
    background-color: #FE2EF7;
}

 .format-table caption {
     font-family: "Roboto";
     font-size: small;
     color: white;
     caption-side: top;
     text-align: center;
     border: 1px solid black;
     width: 100%;
 }
/* Override class provided by dash */
.progress {
    font-family: "Roboto";
    font-size: x-small;
    font-weight: 400;
    display: -ms-flexbox;
    display: flex;    
    height: 1rem;
    overflow: hidden;
    background-color: #e9ecef;
    
    border-radius: .25rem;
}

.progress-bar {
    position: relative;
    font-family: "Roboto";
    font-size: small;
    font-weight: 400;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: center;
    justify-content: center;
    color: black;           

    text-align: center;
    white-space: nowrap;
    background-color: #007bff;    
    transition: width .6s ease;   
    
}

.format-table a {
    text-decoration: none;
    color: white;
    background-color: transparent;
}

.format-table a::before {
    content: "\f0da";
    font-family: FontAwesome;
    color: #de1f1f;
    bottom: 7px;
    line-height: 0px;
    left: 50%;
    transform: translateX(50%);
    transition: all 0.35s ease;
    padding: 4px;
}

.text-font {
    color: white;
    text-align: center;
}

.format-table>tbody>tr.view td:first-child::before {
    position: relative;
    top: 50%;
    left: 0px;
    width: 9px;
    height: 16px;
    padding-right: 7px;
    margin-top: -8px;
    font: 20px fontawesome;
    color: #F2F2F2;
    content: "\f103";
    transition: all .3s ease
}

.format-table>tbody>tr.view.open td:first-child::before {
    content: "\f102";
    color: #FBEFF5;
    transition: all .3s ease;
}

.format-table>tbody>tr.view.open {
    background-color: #848484;
}

.format-table>tbody>tr.view td {
    cursor: pointer;
}

.format-table>tbody>tr.fold {
    display: none;
}

.format-table>tbody>tr.show {
    background-color: transparent;
}

.format-subtask-table {
    font-family: "Roboto";
    font-size: 12px;
    font-weight: 400;
    border-collapse: collapse;
    width: 100%;
}

.format-subtask-table td,
.format-subtask-table th {
    border: 1px solid #ddd;
    padding: 8px;
}

.format-subtask-table th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: center;
    background-color: #084B8A;
    color: white;
    position: sticky;
    top: 0;
}
