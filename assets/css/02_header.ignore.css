/*
Header

Contains all the CSS elements used for creating header menu. Taken from [codepen](https://codepen.io/ahsanrathore/pen/wMRwpZ)
This is [MIT Licensed](https://opensource.org/licenses/MIT)


Styleguide 2.0
*/
/*
.author {
    position: fixed;
    bottom: 15px;
    right: 15px;
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    color: #999;
}

.author a {
    color: #777;
    text-decoration: none;
}

.author a:hover {
    color: blue;
}

header.dark blockquote { 
    color:#fff; 
}
header.light blockquote { 
    color:#000; 
}

blockquote { 
    max-width: 1000px; 
    margin: 0 auto;
    font-size: 16px; 
    border-left: 0px;
    padding:  20px;   
}

blockquote h2 { 
    padding-right: 40px; 
    margin: 0px; 
}

header.dark blockquote a {
    color: orange; 
    text-decoration: underline;
}

header.light blockquote a {
    text-decoration: underline;
}

*/

/* header { min-height: 450px; } */
header.dark {     
    background-color: #444;
    /* background-color: #0404B4; */
}

header.light { 
    background-color: #fff; 
}

/* Navigation Styles */
nav { 
    position: relative;  
}
header.dark nav { 
    background-color:rgba(255,255,255,0.2); 
}
header.light nav { 
    background-color:rgba(0,0,0,0.5); 
}

ul.main-nav { 
    list-style-type: none; 
    padding: 0px;
    font-size: 0px;
    margin: 0 auto;
    white-space: nowrap;
    width: 80%
}

ul.main-nav:hover {
    overflow-x: auto;
}

ul.main-nav > li { 
    display: inline-block;
    padding: 0; 
}

ul.main-nav > li > a { 
    display: block; 
    padding: 20px 30px; 
    position: relative;
    color: #fff !important;
    font-size: 16px;
    font-weight: 400;
    box-sizing: border-box;     
    text-decoration: none;
}

ul.main-nav > li:hover { 
    background-color: #f9f9f9;
    
}
ul.main-nav > li:hover > a { 
    color: #333 !important;     
}

ul.main-nav > li ul.sub-menu-lists {
    margin: 0px;
    padding: 0px;
    list-style-type : none;
    display: block;
}

ul.main-nav > li ul.sub-menu-lists > li {
padding: 2px 0;
}

ul.main-nav > li ul.sub-menu-lists > li > a {
    display: block !important;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    color: var(--bs-body-color);      
    
    /* Source: https://codepen.io/yuhomyan/pen/WNwGywp */    
    
}

/* Source: https://stackoverflow.com/a/66936639/16073830 */

ul.main-nav > li ul.sub-menu-lists > li > a::after {
    display: flex;
    content: "";
    position: relative;
    inset: 0;
    border-radius: 50px;
    padding: 3px; /* control the border thickness */
    background: 
        linear-gradient(45deg, red, blue),
        linear-gradient(#14ffe9, #ffeb3b, #ff00e0);
    left: -10px;
    top: -35px;
    height: 40px;
    transform: scaleY(0);     
    /* 
    clip-path is not working as desired. It can be a replacement for -webkit-mask
    clip-path: polygon(0 0, 0 100%, 100% 100%, 100% 0); 
    */

    -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
/* 
ul.main-nav > li ul.sub-menu-lists > li > a::after {
    display: flex;
    position: relative;
    content: "";    
    border: 2px solid;
    border-image: linear-gradient(to right, #ff0000, #00ff00);
    border-radius: 30px;
    border-image-slice: 1;
    background-origin: border-box;
    background-clip: content-box, border-box;

    clip-path: circle(75%);
    left: -10px;
    top: -35px;
    height: 40px; 
    border-radius: 2px;
    transform: scaleY(0); 
    width: 50%;
    z-index: -1; 
    transition: transform 0.3s ease;        
}
 */

ul.main-nav > li ul.sub-menu-lists > li > a:hover {            
    color: blue;     
    text-align: center;    
}

ul.main-nav > li ul.sub-menu-lists > li > a:hover::after {
    transform: scaleY(1);     
    animation: rotate 1.5s linear infinite;       
}

@keyframes rotate {
    0%{
      filter: hue-rotate(0deg);
    }
    100%{
      filter: hue-rotate(360deg);
    }
}


.ic {
position: fixed; 
cursor: pointer;
display: inline-block;
right: 25px;
width: 32px;
height: 24px;
text-align: center;
top:0px;
outline: none;
}

.fa-arrow-right-from-bracket {
    color: blue;
}

/* 
.ic.close {
    border: 2px red solid;    
} */

.ic.close { 
opacity: 0;  
font-size: 0px; 
font-weight: 300; 
color: #fff;
top:8px;
height:40px;
width: 40px;
display: block;
outline: none;
}

/*

.avatar { 
    opacity: 1;  
    font-size: 12px; 
    font-weight: 300; 
    color: #fff;
    top:8px;
    height:40px;
    width: 40px;
    display: block;
    outline: none;    
}

*/
.ic.menu::before {
    font-family: "FontAwesome";
    position: relative;
}

/* Menu Icons for Devices*/
.ic.menu { top:25px; z-index : 20; }

.ic.menu .line { 
height: 4px; 
width: 100%; 
display: block; 
margin-bottom: 6px; 
}
.ic.menu .line-last-child { margin-bottom: 0px;  }

.sub-menu-head { margin: 10px 0; }
.banners-area { margin-top: 20px; padding-top: 15px; }


@media only screen and (max-width:768px) {
    .avatar {         
        display: none;  
        
    }
    
.sub-menu-head { color:orange; }
.ic.menu { display: block; }
header.dark .ic.menu .line { background-color: #fff; } 
header.light .ic.menu .line { background-color: #000; }
.ic.menu .line {
    -webkit-transition: all 0.4s ease 0s;
    -o-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
}
.ic.menu:focus .line { background-color: #fff !important; }

.ic.menu:focus .line:nth-child(1) { 
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg); 
}

.ic.menu:focus .line:nth-child(2){ 
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg); 
    margin-top: -10px;
}

.ic.menu:focus .line:nth-child(3){
    transform: translateY(15px);
    opacity: 0;
}

.ic.menu:focus{ outline: none; }
.ic.menu:focus ~ .ic.close { opacity: 1; z-index : 21;  outline: none;  }
/*

.ic.menu:focus ~ .ic.close { opacity: 1.0; z-index : 21;  }
.ic.close:focus { opacity: 0; }
*/
.ic.menu:hover, 
.ic.menu:focus{ opacity: 1; }


nav { background-color: transparent; }

/* Main Menu for Handheld Devices  */
ul.main-nav {
    z-index:20; 
    padding: 50px 0;
    position: fixed;
    right: 0px;
    top: 0px;
    width: 0px;
    background-color:rgba(0,0,0,1);
    height: 100%;
    overflow: auto;
    /*CSS animation applied : Slide from Right*/
    -webkit-transition-property: background, width;
    -moz-transition-property: background, width;
    -o-transition-property: background, width;
    transition-property: background, width;
        -webkit-transition-duration: 0.6s;
    -moz-transition-duration: 0.6s;
    -o-transition-duration: 0.6s;
    transition-duration: 0.6s;
}

.ic.menu:focus ~ .main-nav { width: 300px; background-color:rgba(0,0,0,1); }

ul.main-nav > * { 
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    -o-transition-property: opacity;
    transition-property: opacity;
        -webkit-transition-duration: 0.4s;
    -moz-transition-duration: 0.4s;
    -o-transition-duration: 0.4s;
    transition-duration: 0.4s;
    opacity: 0;
}
.ic.menu:focus ~ .main-nav > * {opacity: 1;}

ul.main-nav > li > a:after {display: none;}
ul.main-nav > li:first-child { border-radius: 0px; }
ul.main-nav > li {
    display: block;
    border-bottom: 1px solid #444;
}

ul.main-nav > li > a { font-weight: 600; }

ul.main-nav > li ul.sub-menu-lists > li a { 
    color: #eee; 
    font-size: 14px; 
}

.sub-menu-head { 
    font-size: 14px;       
    
}
ul.main-nav > li:hover { background-color: transparent;  }
ul.main-nav > li:hover > a {
    color: #fff; 
    text-decoration: none; 
    font-weight: 600;
}

.ic.menu:focus ~ ul.main-nav > li > div.sub-menu-block {
    border-left: 0px solid #ccc;
    border-right: 0px solid #ccc;
    border-bottom: 0px solid #ccc;
    position: relative;
    visibility: visible;
    opacity: 1.0;
}

.sub-menu-block { padding: 0 30px;}
.banners-area { padding-bottom: 0px;  }
.banners-area div { margin-bottom: 15px;  }
.banners-area { border-top: 1px solid #444; }
}

@media only screen and (min-width:769px) {
.ic.menu { display: none; }
.ic.close { opacity: 1; z-index : 5;  outline: none;  }
.avatar { 
    opacity: 1;  
    font-size: 12px; 
    font-weight: 300; 
    color: #fff;
    top:8px;
    height:40px;
    width: 40px;
    display: block;
    outline: none;    
}


/* Main Menu for Desktop Devices  */
ul.main-nav { display: block; position: relative; }
.sub-menu-block { padding: 15px; }

/* Sub Menu */
ul.main-nav > li > div.sub-menu-block { 
visibility: hidden;
background-color: #f9f9f9;
position: absolute;
margin-top: 0px;
width: 100%;
color: #333;
left: 0;
box-sizing: border-box;
z-index : 30;
font-size: 16px;
border-left: 1px solid #ccc;
border-right: 1px solid #ccc;
border-bottom: 1px solid #ccc;
opacity: 0;
    
/*CSS animation applied for sub menu : Slide from Top */
-webkit-transition: all 0.4s ease 0s;
-o-transition: all 0.4s ease 0s;
transition: all 0.4s ease 0s;
-webkit-transform: rotateX(90deg);
-moz-transform: rotateX(90deg);
-ms-transform: rotateX(90deg);
transform: rotateX(90deg);
-webkit-transform-origin: top center;
-ms-transform-origin: top center;
transform-origin: top center;

}

ul.main-nav > li:hover > div.sub-menu-block{ 
    background-color: #f9f9f9; 
    visibility: visible;
    opacity: 1;
    -webkit-transform: rotateX(0deg);
    -moz-transform: rotateX(0deg);
    -ms-transform: rotateX(0deg);
    transform: rotateX(0deg);
}

ul.main-nav > li > div.sub-menu-block > * {
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    -o-transition-property: opacity;
    transition-property: opacity;
        -webkit-transition-duration: 0.4s;
    -moz-transition-duration: 0.4s;
    -o-transition-duration: 0.4s;
    transition-duration: 0.4s;
    opacity: 0;
}

ul.main-nav > li:hover > div.sub-menu-block > * {
    opacity: 1;
}

.sub-menu-head { font-size: 20px;}

/* List Separator: Outer Border */
header.dark ul.main-nav > li > a { border-right: 1px solid #bbb; }
header.light ul.main-nav > li > a { border-right: 1px solid #666; }

/* List Separator: Inner Border */
ul.main-nav > li > a:after {
    content: '';
    width: 1px;
    height: 62px;
    position: absolute;
    right:0px;
    top: 0px;
    z-index : 20;
}
header.dark ul.main-nav > li > a:after { background-color: #777; }
header.light ul.main-nav > li > a:after { background-color: #999; }

/* Drop Down/Up Arrow for Mega Menu */
ul.main-nav > li > a.mega-menu > span { display: block; vertical-align: middle; }
ul.main-nav > li > a.mega-menu > span:after {
    width: 0; 
    height: 0; 
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #fff;
    content: '';
    background-color: transparent;
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

ul.main-nav > li:hover > a.mega-menu span:after{
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 0px solid transparent;
    border-bottom: 5px solid #666;
}
.banners-area { border-top: 1px solid #ccc; }
}