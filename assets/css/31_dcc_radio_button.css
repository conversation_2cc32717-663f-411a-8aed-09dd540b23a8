:root {
    --form-control-color: rebeccapurple;
}

.container-radio-button {   
    background: #e6e6e6; 
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: inline-flex;
    flex: 0 0 100%;
    flex-wrap: wrap;
    justify-content: left;
    vertical-align: middle;
    padding-top: 20px;        
    width: 50%;
    border-radius: 25px;
}


.container-radio-button > label {
    width: auto;        
    margin-left: 25px;
    margin-right: 15px;
    font-family: Roboto;
    font-size: 15px;
    font-weight: bolder;
    line-height: 1.1;              
}

.container-radio-button > .dcc-radio-button {            
    width: 50%;
    display: grid;
    grid-template-columns: auto auto;
    gap: 0.1em;            
}

.dcc-radio-button label {    
    /* margin-top: 5px; */
    font-family: Roboto;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.1;
}

.dcc-radio-button input[type="radio"] {
    box-sizing: border-box;
    /* Add if not using autoprefixer */
     
    -webkit-appearance: none;
    appearance: none;  
   
  /* For iOS < 15 to remove gradient background */
    background-color: white;
    /* Not removed via appearance */
    margin: 0;
    margin-right: 5px;
    font: inherit;
    color: currentColor;
    width: 1.15em;
    height: 1.15em;
    border: 0.15em solid currentColor;
    border-radius: 50%;
    transform: translateY(-0.075em);      
    vertical-align: middle;   
}

.dcc-radio-button input[type="radio"]::before {
    content: "";
    width: 0.65em;
    height: 0.65em;
    border-radius: 50%;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em var(--form-control-color);
    background-color: CanvasText;
}

.dcc-radio-button input[type="radio"]:checked::before {
    transform: scale(1);
}

.dcc-radio-button input[type="radio"]:checked {
    background-color: #5cb85c;    
}

.dcc-radio-button input[type="radio"]:focus {
    outline: max(2px, 0.15em) solid currentColor;
    outline-offset: max(2px, 0.15em);
}