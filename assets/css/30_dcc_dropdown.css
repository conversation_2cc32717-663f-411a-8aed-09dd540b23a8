.dcc-dropdown {
    position: relative;    
    /* width: 15em;   */
    width: 100%;
    min-width: 15ch;
    max-width: 30ch;
    height: auto;    
    border-radius: .25em;
}

.dcc-dropdown .VirtualizedSelectFocusedOption {
    background-color: #819FF7;    
    color:white;
}

.dash-dropdown > .Select-control .Select-value .Select-value-label {
    color: black !important;
}

.Select-input{
    height: 25px;
    font-family: "Rajadhani";
    font-size: small;        
}

.Select-value {
    height: 25px;
    font-family: "Rajadhani";
    font-size: small;    
}

.Select-control{
    height: 27px;
}

.Select-placeholder{
    line-height: 25px;    
}

.Select-menu-outer {
    font-family: "<PERSON>dhani";
    font-size: small;
    display : block !important;
}

.Select--multi .Select-value-label {
    font-family: "Rajadhani";
    font-size: small;            
}

/* .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label 
.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label, .dash-spreadsheet 
{
    color: white !important;
}

*/
