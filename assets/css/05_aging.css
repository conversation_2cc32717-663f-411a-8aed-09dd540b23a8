.display-area-3level {       
    color: white;
    display: grid;    
    grid-template-columns: 1fr;
    grid-template-rows: 40px 1fr 40px;
    grid-template-areas:
    "c10",
    "c11",
    "c12";
    align-content: start;   
    overflow-x: hidden;     
}

.header-menu-aging {
    grid-area: "c10";
    position: relative;    
}

.main-area-aging {
    grid-area: "c11";
    position: relative;
    border: 1px solid red;
}

.bottom-aging {
    grid-area: "c12";
    position: relative;    
}

.main-area-aging .layer {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding: 5px;
    margin: 5px;
    display: none;
    /* visibility: hidden; */
}

.main-area-aging .layer.show {
    display: inline;
    /* visibility: visible;            */
    animation: backInLeft;    
    animation-duration: 1s;    
}

.main-area-aging .layer.one {
    background: #dc6c5f;
    display: flexbox;    
}

.main-area-aging .layer.two {
    background: #dc6c5f;
    display: flexbox;    
}

.main-area-aging .layer.three {
    background: #dc6c5f;
    display: flexbox;    
}