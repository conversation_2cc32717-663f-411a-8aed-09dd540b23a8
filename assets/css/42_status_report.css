.status-report {
    border-bottom: 1px solid #b5bfd9;      
}

.release-format-button {
    font-family: <PERSON><PERSON><PERSON>;
    font-size: small;
    font-weight: 900;   
    color: #fff;
    text-transform: uppercase;
  
    width: 25px;
    height: 25px;
    border-radius: 2px;
    background-color: blue;
    outline: none;
    border: 2px solid #E2E7FA;
    
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    align-items: center;
  
    -webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}

.release-format-button:hover {
    cursor: pointer;
    background-color: #597FE2;
    color: #fff;
}

.release-format-button:disabled, .release-format-button[disabled=disabled] {
    background-color: gray;
    text-decoration: line-through;
}

.dropdown_custom {
    position: relative;    
    /* width: 15em;   */
    width: 100%;
    min-width: 10ch;
    max-width: 15ch;
    height: auto;    
    border-radius: .25em;    
}

.dropdown_custom > .Select-control .Select-value .Select-value-label {
    color: black !important;
}

