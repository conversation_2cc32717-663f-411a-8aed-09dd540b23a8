/* Modern Weekly Report Styles */

:root {
    --report-primary: #1890ff;
    --report-primary-hover: #40a9ff;
    --report-secondary: #52c41a;
    --report-secondary-hover: #73d13d;
    --report-danger: #ff4d4f;
    --report-danger-hover: #ff7875;
    --report-warning: #faad14;
    --report-warning-hover: #ffc53d;
    --report-dark: #1f1f1f;
    --report-dark-light: #2f2f2f;
    --report-gray: #8c8c8c;
    --report-light-gray: #f5f5f5;
    --report-border: #d9d9d9;
    --report-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --report-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.25);
    --report-radius: 8px;
    --report-radius-small: 4px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Layout */
.modern-weekly-report-layout {
    min-height: calc(100vh - 60px);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0;
    overflow-x: hidden;
}

/* Header Section */
.report-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--report-border);
    box-shadow: var(--report-shadow);
    padding: 24px 0;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon {
    font-size: 32px;
    color: var(--report-primary);
}

.header-title {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: var(--report-dark);
    letter-spacing: 0.5px;
}

.header-subtitle {
    margin: 4px 0 0 0;
    font-size: 16px;
    color: var(--report-gray);
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Main Content */
.report-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
}

/* Modern Tab Container */
.modern-tab-container {
    background: white;
    border-radius: var(--report-radius);
    box-shadow: var(--report-shadow);
    overflow: hidden;
    margin-bottom: 24px;
}

.tab-header {
    display: flex;
    background: var(--report-light-gray);
    border-bottom: 1px solid var(--report-border);
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--report-gray);
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    flex: 1;
    justify-content: center;
}

.tab-button:hover {
    color: var(--report-primary);
    background: rgba(24, 144, 255, 0.05);
}

.tab-button.active {
    color: var(--report-primary);
    border-bottom-color: var(--report-primary);
    background: white;
    font-weight: 600;
}

.tab-icon {
    font-size: 16px;
}

/* Tab Content */
.tab-content-container {
    position: relative;
    min-height: 400px;
}

.tab-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    padding: 24px;
    overflow-y: auto;
}

.tab-panel.active {
    opacity: 1;
    visibility: visible;
    position: relative;
}

.panel-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--report-border);
}

.panel-title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--report-dark);
}

.panel-description {
    margin: 0;
    font-size: 14px;
    color: var(--report-gray);
}

/* Configuration Section */
.config-section {
    background: var(--report-light-gray);
    border-radius: var(--report-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--report-border);
}

.section-header > div:first-child {
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-icon {
    font-size: 20px;
    color: var(--report-primary);
}

.section-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--report-dark);
}

.version-controls {
    display: flex;
    gap: 12px;
}

/* Modern Buttons */
.modern-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: var(--report-radius-small);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    min-height: 40px;
    justify-content: center;
}

.modern-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.modern-button.small {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 32px;
}

.modern-button.large {
    padding: 12px 20px;
    font-size: 16px;
    min-height: 48px;
}

.modern-button.primary {
    background: var(--report-primary);
    color: white;
}

.modern-button.primary:hover:not(:disabled) {
    background: var(--report-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--report-shadow-hover);
}

.modern-button.secondary {
    background: var(--report-gray);
    color: white;
}

.modern-button.secondary:hover:not(:disabled) {
    background: #595959;
    transform: translateY(-1px);
    box-shadow: var(--report-shadow-hover);
}

.modern-button.success {
    background: var(--report-secondary);
    color: white;
}

.modern-button.success:hover:not(:disabled) {
    background: var(--report-secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--report-shadow-hover);
}

.button-icon {
    font-size: 16px;
}

/* Configuration Grid */
.config-grid-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: var(--report-radius-small);
    box-shadow: var(--report-shadow);
}

.grid-header-cell {
    font-size: 14px;
    font-weight: 600;
    color: var(--report-dark);
    text-align: center;
    padding: 8px;
}

.config-grid-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Risk Grid */
.risk-grid-header {
    display: grid;
    grid-template-columns: 120px 2fr 100px 100px 2fr 2fr 80px;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: white;
    border-radius: var(--report-radius-small);
    box-shadow: var(--report-shadow);
}

.risk-header-cell {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 600;
    color: var(--report-dark);
    text-align: center;
    padding: 8px;
}

.risk-header-cell .header-icon {
    font-size: 14px;
    color: var(--report-primary);
}

.risk-grid-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Progress Section */
.progress-section {
    background: white;
    border-radius: var(--report-radius);
    box-shadow: var(--report-shadow);
    margin-bottom: 24px;
    overflow: hidden;
}

.progress-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    background: var(--report-light-gray);
    border-bottom: 1px solid var(--report-border);
}

.progress-icon {
    font-size: 24px;
    color: var(--report-primary);
}

.progress-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--report-dark);
}

.progress-content {
    padding: 24px;
}

.status-display {
    margin-bottom: 24px;
}

.status-text {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--report-dark);
    text-align: center;
}

.progress-chart {
    height: 100px;
    margin-bottom: 20px;
}

.action-buttons-section {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Log Section */
.log-section {
    background: white;
    border-radius: var(--report-radius);
    box-shadow: var(--report-shadow);
    overflow: hidden;
}

.log-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 24px;
    background: var(--report-light-gray);
    border-bottom: 1px solid var(--report-border);
}

.log-icon {
    font-size: 24px;
    color: var(--report-primary);
}

.log-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--report-dark);
}

.log-content {
    padding: 24px;
    max-height: 300px;
    overflow-y: auto;
    background: var(--report-dark);
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.log-message {
    margin: 0;
    padding: 4px 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .title-section {
        flex-direction: column;
        gap: 8px;
    }
    
    .header-title {
        font-size: 24px;
    }
    
    .report-content {
        padding: 16px;
    }
    
    .tab-header {
        flex-direction: column;
    }
    
    .tab-button {
        justify-content: flex-start;
        padding: 12px 16px;
    }
    
    .config-grid-header {
        grid-template-columns: 1fr;
        text-align: left;
    }
    
    .risk-grid-header {
        grid-template-columns: 1fr;
    }
    
    .action-buttons-section {
        flex-direction: column;
    }
    
    .modern-button.large {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 16px;
    }
    
    .report-content {
        padding: 12px;
    }
    
    .tab-panel {
        padding: 16px;
    }
    
    .config-section {
        padding: 16px;
    }
    
    .progress-content,
    .log-content {
        padding: 16px;
    }
}
