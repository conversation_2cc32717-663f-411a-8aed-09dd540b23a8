/* 
use fontawesome icons in ccs 
https://stackoverflow.com/questions/14736496/use-font-awesome-icons-in-css

CSS button hover: 
https://www.sliderrevolution.com/resources/css-button-hover-effects/

*/
.left-align-content {
    text-align: left;   
    padding-left: 5px;                  
}

.bullets {
    position: absolute;
    width: 100%;
    text-align: center;
}

.bullets li {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 3px;
    background: rgba( 255, 255, 255, 0.5 );
    box-shadow: 0px 0px 4px rgba( 0, 0, 0, 0.2 );
    cursor: pointer;
    vertical-align: bottom;
    -webkit-tap-highlight-color: rgba( 0, 0, 0, 0 );
}

.bullets li:hover {
    background: rgba( 255, 255, 255, 0.8 );
}

.bullets li.active {
    cursor: default;
    background: #fff;
}

.side-menu .line-divider {
    display: flex;
    color: snow;
    align-self:center;
    justify-content: center;
    padding-top: 10px;
    width: 100%;    
    border-bottom: 1px red groove;    
}

.format-text-header {
    position: relative;
    font-family: "Raj<PERSON>ni";
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    background-color: #5E610B;
    opacity: 0.8;
    color: white;
    vertical-align: middle;
    margin-top: 5px;
    padding-left: 2px;
    padding-right: 2px;
}