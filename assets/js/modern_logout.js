/**
 * Modern Logout Menu JavaScript
 * Handles dropdown menu interactions and logout functionality
 */

// Logout menu namespace
window.modernLogout = {
    // Configuration
    config: {
        countdownDuration: 10, // seconds
        autoRedirect: true
    },

    // State
    state: {
        dropdownOpen: false,
        countdownTimer: null,
        countdownSeconds: 10
    },

    // Initialize logout functionality
    init: function() {
        this.bindEvents();
        this.initCountdown();
        this.setupAccessibility();
    },

    // Bind event listeners
    bindEvents: function() {
        // Dropdown toggle
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('#user-menu-trigger');
            const dropdown = document.getElementById('user-dropdown-menu');
            
            if (trigger) {
                e.preventDefault();
                this.toggleDropdown();
            } else if (!e.target.closest('.user-dropdown-menu')) {
                this.closeDropdown();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDropdown();
            }
        });

        // Logout button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('#logout-button')) {
                e.preventDefault();
                this.confirmLogout();
            }
        });

        // Profile and settings links (placeholder functionality)
        document.addEventListener('click', (e) => {
            if (e.target.closest('#profile-link')) {
                e.preventDefault();
                this.closeDropdown();
                this.showProfileModal();
            }

            if (e.target.closest('#settings-link')) {
                e.preventDefault();
                this.closeDropdown();
                this.showSettingsModal();
            }
        });
    },

    // Toggle dropdown menu
    toggleDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        if (this.state.dropdownOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    },

    // Open dropdown menu
    openDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        this.state.dropdownOpen = true;
        trigger.classList.add('active');
        dropdown.classList.add('show');
        
        // Focus first menu item for accessibility
        const firstItem = dropdown.querySelector('.dropdown-item');
        if (firstItem) {
            setTimeout(() => firstItem.focus(), 100);
        }
    },

    // Close dropdown menu
    closeDropdown: function() {
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (!trigger || !dropdown) return;

        this.state.dropdownOpen = false;
        trigger.classList.remove('active');
        dropdown.classList.remove('show');
    },

    // Confirm logout with modern dialog
    confirmLogout: function() {
        // Create modern confirmation dialog
        const dialog = this.createConfirmDialog();
        document.body.appendChild(dialog);
        
        // Show dialog with animation
        setTimeout(() => {
            dialog.classList.add('show');
        }, 10);
    },

    // Create confirmation dialog
    createConfirmDialog: function() {
        const dialog = document.createElement('div');
        dialog.className = 'logout-confirm-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay"></div>
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="iconify" data-icon="mdi:logout" style="font-size: 2rem; color: #ff4d4f;"></i>
                </div>
                <h3 class="dialog-title">Sign Out</h3>
                <p class="dialog-message">Are you sure you want to sign out of your account?</p>
                <div class="dialog-actions">
                    <button class="dialog-button secondary" onclick="modernLogout.cancelLogout(this)">
                        Cancel
                    </button>
                    <button class="dialog-button primary" onclick="modernLogout.performLogout(this)">
                        Sign Out
                    </button>
                </div>
            </div>
        `;
        
        // Add styles if not already present
        if (!document.getElementById('logout-dialog-styles')) {
            const style = document.createElement('style');
            style.id = 'logout-dialog-styles';
            style.textContent = `
                .logout-confirm-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 10000;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                
                .logout-confirm-dialog.show {
                    opacity: 1;
                    visibility: visible;
                }
                
                .dialog-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(4px);
                }
                
                .dialog-content {
                    background: white;
                    border-radius: 12px;
                    padding: 2rem;
                    max-width: 400px;
                    width: 90%;
                    text-align: center;
                    position: relative;
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                }
                
                .logout-confirm-dialog.show .dialog-content {
                    transform: scale(1);
                }
                
                .dialog-icon {
                    margin-bottom: 1rem;
                }
                
                .dialog-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                    color: #1a1a1a;
                }
                
                .dialog-message {
                    color: #666;
                    margin-bottom: 2rem;
                    line-height: 1.5;
                }
                
                .dialog-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                }
                
                .dialog-button {
                    padding: 10px 20px;
                    border-radius: 6px;
                    border: none;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 100px;
                }
                
                .dialog-button.primary {
                    background: #ff4d4f;
                    color: white;
                }
                
                .dialog-button.primary:hover {
                    background: #ff7875;
                }
                
                .dialog-button.secondary {
                    background: #f5f5f5;
                    color: #666;
                }
                
                .dialog-button.secondary:hover {
                    background: #e6f7ff;
                    color: #1890ff;
                }
            `;
            document.head.appendChild(style);
        }
        
        return dialog;
    },

    // Cancel logout
    cancelLogout: function(button) {
        const dialog = button.closest('.logout-confirm-dialog');
        dialog.classList.remove('show');
        setTimeout(() => {
            dialog.remove();
        }, 300);
    },

    // Perform logout
    performLogout: function(button) {
        const dialog = button.closest('.logout-confirm-dialog');
        
        // Show loading state
        button.innerHTML = '<i class="iconify" data-icon="mdi:loading" style="animation: spin 1s linear infinite;"></i> Signing out...';
        button.disabled = true;
        
        // Redirect to logout endpoint
        setTimeout(() => {
            const basePath = window.dashBasePath || '/';
            window.location.href = basePath + 'logout';
        }, 1000);
    },

    // Initialize countdown on logout page
    initCountdown: function() {
        const countdownElement = document.getElementById('logout-countdown');
        if (!countdownElement) return;

        this.state.countdownSeconds = this.config.countdownDuration;
        this.updateCountdown();
        
        this.state.countdownTimer = setInterval(() => {
            this.state.countdownSeconds--;
            this.updateCountdown();
            
            if (this.state.countdownSeconds <= 0) {
                this.redirectToLogin();
            }
        }, 1000);
    },

    // Update countdown display
    updateCountdown: function() {
        const countdownElement = document.getElementById('logout-countdown');
        if (!countdownElement) return;

        if (this.state.countdownSeconds > 0) {
            countdownElement.textContent = `Redirecting to login in ${this.state.countdownSeconds} seconds...`;
        } else {
            countdownElement.textContent = 'Redirecting...';
        }
    },

    // Redirect to login page
    redirectToLogin: function() {
        if (this.state.countdownTimer) {
            clearInterval(this.state.countdownTimer);
        }
        
        const basePath = window.dashBasePath || '/';
        window.location.href = basePath + 'login';
    },

    // Setup accessibility features
    setupAccessibility: function() {
        // Add ARIA attributes
        const trigger = document.getElementById('user-menu-trigger');
        const dropdown = document.getElementById('user-dropdown-menu');
        
        if (trigger && dropdown) {
            trigger.setAttribute('aria-haspopup', 'true');
            trigger.setAttribute('aria-expanded', 'false');
            dropdown.setAttribute('role', 'menu');
            
            // Update aria-expanded when dropdown state changes
            const observer = new MutationObserver(() => {
                const isOpen = dropdown.classList.contains('show');
                trigger.setAttribute('aria-expanded', isOpen.toString());
            });
            
            observer.observe(dropdown, { attributes: true, attributeFilter: ['class'] });
        }
    },

    // Show notification
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `modern-notification ${type}`;
        notification.innerHTML = `
            <i class="iconify" data-icon="mdi:information" style="margin-right: 8px;"></i>
            ${message}
        `;
        
        // Add styles if not present
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .modern-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                    border-left: 4px solid #1890ff;
                    z-index: 10001;
                    animation: slideInRight 0.3s ease-out;
                }
                
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.modernLogout.init();
    });
} else {
    window.modernLogout.init();
}

// Mobile menu functionality
window.modernHeader = {
    init: function() {
        this.bindMobileMenuEvents();
        this.bindNotificationEvents();
        this.bindMobileSubmenuEvents();
    },

    bindMobileMenuEvents: function() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleMobileMenu();
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.main-navigation') && !e.target.closest('.mobile-menu-toggle')) {
                    this.closeMobileMenu();
                }
            });

            // Close mobile menu on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    this.closeMobileMenu();
                    this.closeAllMobileSubmenus();
                }
            });

            // Prevent menu from closing when clicking inside
            navMenu.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    },

    bindMobileSubmenuEvents: function() {
        // Handle mobile submenu toggles
        document.addEventListener('click', (e) => {
            const megaMenuLink = e.target.closest('a.mega-menu');
            if (megaMenuLink && window.innerWidth <= 768) {
                e.preventDefault();
                e.stopPropagation();

                const parentLi = megaMenuLink.closest('li');
                const isOpen = parentLi.classList.contains('mobile-submenu-open');

                // Close all other submenus
                this.closeAllMobileSubmenus();

                // Toggle current submenu
                if (!isOpen) {
                    parentLi.classList.add('mobile-submenu-open');
                }
            }
        });
    },

    closeAllMobileSubmenus: function() {
        const openSubmenus = document.querySelectorAll('.mobile-submenu-open');
        openSubmenus.forEach(submenu => {
            submenu.classList.remove('mobile-submenu-open');
        });
    },

    toggleMobileMenu: function() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileToggle && navMenu) {
            const isOpen = navMenu.classList.contains('mobile-open');

            if (isOpen) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        }
    },

    openMobileMenu: function() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.classList.add('active');
            navMenu.classList.add('mobile-open');
            document.body.style.overflow = 'hidden';

            // Close any open user dropdowns
            const userDropdown = document.getElementById('user-dropdown-menu');
            const userTrigger = document.getElementById('user-menu-trigger');
            if (userDropdown && userTrigger) {
                userDropdown.classList.remove('show');
                userTrigger.classList.remove('active');
            }
        }
    },

    closeMobileMenu: function() {
        const mobileToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.classList.remove('active');
            navMenu.classList.remove('mobile-open');
            document.body.style.overflow = '';
            this.closeAllMobileSubmenus();
        }
    },

    bindNotificationEvents: function() {
        const notificationBtn = document.getElementById('notifications-btn');

        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.showNotificationPanel();
            });
        }
    },

    showNotificationPanel: function() {
        this.showCenteredModal('Notifications', `
            <div class="notification-list">
                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="iconify" data-icon="mdi:information" style="color: #1890ff;"></i>
                    </div>
                    <div class="notification-content">
                        <h4>System Update</h4>
                        <p>New features have been added to the dashboard.</p>
                        <small>2 hours ago</small>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="iconify" data-icon="mdi:alert" style="color: #faad14;"></i>
                    </div>
                    <div class="notification-content">
                        <h4>Sprint Deadline</h4>
                        <p>Current sprint ends in 3 days.</p>
                        <small>1 day ago</small>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon">
                        <i class="iconify" data-icon="mdi:check-circle" style="color: #52c41a;"></i>
                    </div>
                    <div class="notification-content">
                        <h4>Task Completed</h4>
                        <p>Your assigned task has been completed.</p>
                        <small>3 days ago</small>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="modal-button secondary" onclick="this.closest('.centered-modal').remove()">
                    Close
                </button>
                <button class="modal-button primary">
                    Mark All Read
                </button>
            </div>
        `);
    },

    showProfileModal: function() {
        this.showCenteredModal('User Profile', `
            <div class="profile-content">
                <div class="profile-avatar">
                    <img src="https://via.placeholder.com/80x80/1890FF/white?text=JD" alt="Profile Avatar">
                    <button class="change-avatar-btn">
                        <i class="iconify" data-icon="mdi:camera"></i>
                    </button>
                </div>
                <div class="profile-form">
                    <div class="form-group">
                        <label>Display Name</label>
                        <input type="text" value="John Doe" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" value="<EMAIL>" readonly>
                    </div>
                    <div class="form-group">
                        <label>Department</label>
                        <input type="text" value="Engineering" readonly>
                    </div>
                    <div class="form-group">
                        <label>Role</label>
                        <input type="text" value="Senior Developer" readonly>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="modal-button secondary" onclick="this.closest('.centered-modal').remove()">
                    Close
                </button>
                <button class="modal-button primary">
                    Edit Profile
                </button>
            </div>
        `);
    },

    showSettingsModal: function() {
        this.showCenteredModal('Settings', `
            <div class="settings-content">
                <div class="settings-section">
                    <h4>Appearance</h4>
                    <div class="setting-item">
                        <label>Theme</label>
                        <select>
                            <option>Light</option>
                            <option selected>Dark</option>
                            <option>Auto</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>Language</label>
                        <select>
                            <option selected>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                        </select>
                    </div>
                </div>
                <div class="settings-section">
                    <h4>Notifications</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked> Email notifications
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked> Push notifications
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox"> SMS notifications
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button class="modal-button secondary" onclick="this.closest('.centered-modal').remove()">
                    Cancel
                </button>
                <button class="modal-button primary">
                    Save Changes
                </button>
            </div>
        `);
    },

    showCenteredModal: function(title, content) {
        // Remove any existing modals
        const existingModals = document.querySelectorAll('.centered-modal');
        existingModals.forEach(modal => modal.remove());

        const modal = document.createElement('div');
        modal.className = 'centered-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.closest('.centered-modal').remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="this.closest('.centered-modal').remove()">
                        <i class="iconify" data-icon="mdi:close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        // Add modal styles if not present
        if (!document.getElementById('centered-modal-styles')) {
            const style = document.createElement('style');
            style.id = 'centered-modal-styles';
            style.textContent = this.getCenteredModalStyles();
            document.head.appendChild(style);
        }

        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // Close mobile menu if open
        this.closeMobileMenu();
    },

    getCenteredModalStyles: function() {
        return `
            .centered-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .centered-modal.show {
                opacity: 1;
                visibility: visible;
            }

            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(4px);
            }

            .modal-content {
                background: white;
                border-radius: 16px;
                max-width: 500px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                position: relative;
                transform: scale(0.9);
                transition: transform 0.3s ease;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }

            .centered-modal.show .modal-content {
                transform: scale(1);
            }

            .modal-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1.5rem 2rem;
                border-bottom: 1px solid #f0f0f0;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 16px 16px 0 0;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 1.25rem;
                font-weight: 600;
                color: #1a1a1a;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #666;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.3s ease;
            }

            .modal-close:hover {
                background: rgba(0, 0, 0, 0.1);
                color: #333;
            }

            .modal-body {
                padding: 2rem;
            }

            .modal-actions {
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
                padding-top: 1.5rem;
                border-top: 1px solid #f0f0f0;
                margin-top: 1.5rem;
            }

            .modal-button {
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 100px;
            }

            .modal-button.primary {
                background: #1890ff;
                color: white;
            }

            .modal-button.primary:hover {
                background: #40a9ff;
                transform: translateY(-1px);
            }

            .modal-button.secondary {
                background: #f5f5f5;
                color: #666;
            }

            .modal-button.secondary:hover {
                background: #e6f7ff;
                color: #1890ff;
            }

            /* Notification List Styles */
            .notification-list {
                max-height: 400px;
                overflow-y: auto;
            }

            .notification-item {
                display: flex;
                gap: 1rem;
                padding: 1rem;
                border-bottom: 1px solid #f0f0f0;
                transition: background 0.3s ease;
            }

            .notification-item:hover {
                background: #f8f9fa;
            }

            .notification-item:last-child {
                border-bottom: none;
            }

            .notification-icon {
                flex-shrink: 0;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                border-radius: 50%;
            }

            .notification-content h4 {
                margin: 0 0 0.5rem 0;
                font-size: 1rem;
                font-weight: 600;
                color: #1a1a1a;
            }

            .notification-content p {
                margin: 0 0 0.5rem 0;
                color: #666;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .notification-content small {
                color: #999;
                font-size: 0.8rem;
            }

            /* Profile Content Styles */
            .profile-content {
                text-align: center;
            }

            .profile-avatar {
                position: relative;
                display: inline-block;
                margin-bottom: 2rem;
            }

            .profile-avatar img {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                border: 4px solid #f0f0f0;
            }

            .change-avatar-btn {
                position: absolute;
                bottom: 0;
                right: 0;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #1890ff;
                color: white;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }

            .change-avatar-btn:hover {
                background: #40a9ff;
                transform: scale(1.1);
            }

            .profile-form {
                text-align: left;
            }

            .form-group {
                margin-bottom: 1.5rem;
            }

            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
                color: #333;
            }

            .form-group input {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                font-size: 14px;
                background: #f9f9f9;
                color: #666;
            }

            /* Settings Content Styles */
            .settings-section {
                margin-bottom: 2rem;
            }

            .settings-section:last-child {
                margin-bottom: 0;
            }

            .settings-section h4 {
                margin: 0 0 1rem 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: #1a1a1a;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid #f0f0f0;
            }

            .setting-item {
                margin-bottom: 1rem;
            }

            .setting-item label {
                display: block;
                font-weight: 500;
                color: #333;
                margin-bottom: 0.5rem;
            }

            .setting-item select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                font-size: 14px;
                background: white;
            }

            .setting-item input[type="checkbox"] {
                margin-right: 8px;
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .centered-modal {
                    padding: 10px;
                }

                .modal-content {
                    max-width: none;
                    width: 100%;
                    max-height: 95vh;
                }

                .modal-header {
                    padding: 1rem 1.5rem;
                }

                .modal-body {
                    padding: 1.5rem;
                }

                .modal-actions {
                    flex-direction: column;
                }

                .modal-button {
                    width: 100%;
                }
            }
        `;
    },
};

// Initialize modern header when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.modernHeader.init();
    });
} else {
    window.modernHeader.init();
}

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernLogout = window.modernLogout;
window.dash_clientside.modernHeader = window.modernHeader;
