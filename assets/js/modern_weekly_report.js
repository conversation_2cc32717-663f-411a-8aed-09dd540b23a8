/**
 * Modern Weekly Report JavaScript Functionality
 * Handles tab switching, form interactions, and progress updates
 */

window.modernWeeklyReport = {
    // State management
    state: {
        activeTab: 'cookie-tab-panel',
        isGenerating: false,
        isMobile: window.innerWidth <= 768
    },

    // Initialize the weekly report functionality
    init: function() {
        this.bindEvents();
        this.handleResize();
        this.initializeTabs();
        
        console.log('Modern Weekly Report initialized');
    },

    // Bind event listeners
    bindEvents: function() {
        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Tab button clicks
        document.addEventListener('click', (e) => {
            const tabButton = e.target.closest('.tab-button');
            if (tabButton) {
                e.preventDefault();
                const tabId = tabButton.getAttribute('data-tab');
                this.switchTab(tabId);
            }
        });

        // Button click handlers
        document.addEventListener('click', (e) => {
            if (e.target.closest('#button_id')) {
                this.handleGenerateReport();
            }
            
            if (e.target.closest('#cancel_button_id')) {
                this.handleCancelJob();
            }
            
            if (e.target.closest('#id-download-report')) {
                this.handleDownloadReport();
            }
        });
    },

    // Handle window resize
    handleResize: function() {
        this.state.isMobile = window.innerWidth <= 768;
        this.updateLayout();
    },

    // Initialize tab functionality
    initializeTabs: function() {
        // Set first tab as active by default
        const firstTab = document.querySelector('.tab-button');
        const firstPanel = document.querySelector('.tab-panel');
        
        if (firstTab && firstPanel) {
            firstTab.classList.add('active');
            firstPanel.classList.add('active');
            this.state.activeTab = firstPanel.id;
        }
    },

    // Switch between tabs
    switchTab: function(targetTabId) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-button').forEach(tab => {
            tab.classList.remove('active');
        });
        
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Add active class to target tab and panel
        const targetButton = document.querySelector(`[data-tab="${targetTabId}"]`);
        const targetPanel = document.getElementById(targetTabId.replace('display-', '') + '-tab-panel');
        
        if (targetButton && targetPanel) {
            targetButton.classList.add('active');
            targetPanel.classList.add('active');
            this.state.activeTab = targetPanel.id;
            
            // Animate tab switch
            this.animateTabSwitch(targetPanel);
        }
    },

    // Animate tab switch
    animateTabSwitch: function(panel) {
        panel.style.opacity = '0';
        panel.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            panel.style.transition = 'all 0.3s ease-out';
            panel.style.opacity = '1';
            panel.style.transform = 'translateY(0)';
        }, 50);
    },

    // Handle generate report button
    handleGenerateReport: function() {
        if (this.state.isGenerating) return;
        
        this.state.isGenerating = true;
        this.updateButtonStates();
        this.showProgress();
        
        // Update status text
        const statusText = document.getElementById('paragraph_id');
        if (statusText) {
            statusText.textContent = 'Generating report...';
        }
        
        // Add log message
        this.addLogMessage('Report generation started', 'info');
    },

    // Handle cancel job button
    handleCancelJob: function() {
        if (!this.state.isGenerating) return;
        
        this.state.isGenerating = false;
        this.updateButtonStates();
        this.hideProgress();
        
        // Update status text
        const statusText = document.getElementById('paragraph_id');
        if (statusText) {
            statusText.textContent = 'Report generation cancelled';
        }
        
        // Update log
        this.addLogMessage('Report generation cancelled by user', 'warning');
    },

    // Handle download report button
    handleDownloadReport: function() {
        this.addLogMessage('Initiating report download...', 'info');
    },

    // Update button states based on generation status
    updateButtonStates: function() {
        const generateBtn = document.getElementById('button_id');
        const cancelBtn = document.getElementById('cancel_button_id');
        const downloadBtn = document.getElementById('id-download-report');
        
        if (generateBtn) {
            generateBtn.disabled = this.state.isGenerating;
            if (this.state.isGenerating) {
                generateBtn.classList.add('loading');
            } else {
                generateBtn.classList.remove('loading');
            }
        }
        
        if (cancelBtn) {
            cancelBtn.disabled = !this.state.isGenerating;
        }
        
        if (downloadBtn) {
            downloadBtn.disabled = this.state.isGenerating;
        }
    },

    // Show progress animation
    showProgress: function() {
        const progressChart = document.getElementById('progress_bar_graph');
        if (progressChart) {
            progressChart.style.opacity = '1';
        }
    },

    // Hide progress animation
    hideProgress: function() {
        const progressChart = document.getElementById('progress_bar_graph');
        if (progressChart) {
            progressChart.style.opacity = '0.5';
        }
    },

    // Add message to log
    addLogMessage: function(message, type = 'info') {
        const logContent = document.getElementById('log');
        if (!logContent) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('p');
        logEntry.className = 'log-message';
        
        const typeIcon = {
            'info': '→',
            'success': '✓',
            'warning': '⚠',
            'error': '✗'
        };
        
        logEntry.textContent = `[${timestamp}] ${typeIcon[type] || '→'} ${message}`;
        
        // Add color based on type
        const colors = {
            'info': '#00ff00',
            'success': '#00ff00',
            'warning': '#ffff00',
            'error': '#ff0000'
        };
        
        logEntry.style.color = colors[type] || '#00ff00';
        
        logContent.appendChild(logEntry);
        
        // Auto-scroll to bottom
        logContent.scrollTop = logContent.scrollHeight;
        
        // Limit log entries to prevent memory issues
        const logEntries = logContent.querySelectorAll('.log-message');
        if (logEntries.length > 50) {
            logEntries[0].remove();
        }
    },

    // Update layout based on screen size
    updateLayout: function() {
        const container = document.querySelector('.modern-weekly-report-layout');
        if (!container) return;
        
        if (this.state.isMobile) {
            container.classList.add('mobile-layout');
        } else {
            container.classList.remove('mobile-layout');
        }
    },

    // Get current state for debugging
    getState: function() {
        return { ...this.state };
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (document.querySelector('.modern-weekly-report-layout')) {
            window.modernWeeklyReport.init();
        }
    }, 100);
});

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernWeeklyReport = {
    
    /**
     * Handle tab switching for Dash callback
     */
    switchTab: function(cookie_clicks, jazz_clicks, risk_clicks) {
        const ctx = window.dash_clientside.callback_context;
        if (!ctx.triggered.length) return window.dash_clientside.no_update;
        
        const triggerId = ctx.triggered[0].prop_id.split('.')[0];
        
        if (window.modernWeeklyReport) {
            let tabId = 'display-cookie';
            if (triggerId.includes('jazz')) {
                tabId = 'display-jazz';
            } else if (triggerId.includes('risk')) {
                tabId = 'display-risk';
            }
            
            window.modernWeeklyReport.switchTab(tabId);
        }
        
        return window.dash_clientside.no_update;
    },

    /**
     * Handle progress updates
     */
    updateProgress: function(progress_data) {
        if (window.modernWeeklyReport && progress_data) {
            const message = progress_data.message || 'Processing...';
            const type = progress_data.type || 'info';
            window.modernWeeklyReport.addLogMessage(message, type);
        }
        
        return window.dash_clientside.no_update;
    }
};

// Global resize handler
window.addEventListener('resize', function() {
    if (window.modernWeeklyReport) {
        window.modernWeeklyReport.handleResize();
    }
});
