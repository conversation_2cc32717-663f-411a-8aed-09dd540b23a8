// Refer link for addEventListener https://community.plotly.com/t/execute-javascript-for-sidebar/69821?u=biyani701
//if (!window.dash_clientside) {
//    window.dash_clientside = {};
//}
//window.dash_clientside.clientside = {
//    make_draggable: function (children, inputs_info, id) {
//
//        setTimeout(function () {
//
//            let drake = dragula({});
//            let el = document.getElementById(id)
//            let output_div = document.getElementById('sort-order')
//            drake.containers.push(el);
//            drake.on("drop", function (_el, target, source, sibling) {
//
//                // a component has been dragged & dropped
//                let order_ids = Array.from(target.children).map(function (item) {
//                    return item.getAttribute('data-info')
//                })
//                window.console.log(order_ids)
//                output_div.innerHTML = order_ids.toString()
//            })
//        }, 1)
//        return window.dash_clientside.no_update
//    }
//}