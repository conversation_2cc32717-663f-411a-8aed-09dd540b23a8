/**
 * Boot Screen Management JavaScript
 * Handles boot screen animations and transitions
 */

// Boot screen namespace
window.bootScreen = {
    // Configuration
    config: {
        minDisplayTime: 2000,      // Minimum time to show boot screen (ms)
        fadeOutDuration: 500,      // Boot screen fade out duration (ms)
        formAnimationDelay: 100,   // Delay between form element animations (ms)
        progressSteps: [0, 30, 60, 85, 100], // Progress bar steps
        progressDuration: 2500     // Total progress animation duration (ms)
    },

    // State
    state: {
        isVisible: false,
        startTime: null,
        resourcesLoaded: false
    },

    // Initialize boot screen
    init: function() {
        this.bindEvents();
        this.checkInitialState();
    },

    // Bind event listeners
    bindEvents: function() {
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && this.state.isVisible) {
                this.updateProgress();
            }
        });

        // Listen for resource loading
        window.addEventListener('load', () => {
            this.state.resourcesLoaded = true;
            this.checkHideConditions();
        });

        // Listen for DOM content loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.enhanceBootScreen();
        });
    },

    // Check initial state and show boot screen if needed
    checkInitialState: function() {
        const pathname = window.location.pathname;
        if (pathname.includes('login') || pathname.includes('logout')) {
            this.show();
        }
    },

    // Show boot screen
    show: function() {
        const bootScreen = document.getElementById('boot-screen');
        const loginContainer = document.getElementById('login-form-container');
        
        if (!bootScreen) return;

        this.state.isVisible = true;
        this.state.startTime = Date.now();

        // Show boot screen
        bootScreen.classList.remove('boot-screen-hidden');
        
        // Hide login form
        if (loginContainer) {
            loginContainer.classList.add('login-form-hidden');
            loginContainer.classList.remove('login-form-visible');
        }

        // Start progress animation
        this.animateProgress();

        // Set minimum display time
        setTimeout(() => {
            this.checkHideConditions();
        }, this.config.minDisplayTime);
    },

    // Hide boot screen
    hide: function() {
        if (!this.state.isVisible) return;

        const bootScreen = document.getElementById('boot-screen');
        const loginContainer = document.getElementById('login-form-container');
        
        if (!bootScreen) return;

        this.state.isVisible = false;

        // Fade out boot screen
        bootScreen.classList.add('boot-screen-hidden');

        // Show login form after fade out
        setTimeout(() => {
            if (loginContainer) {
                loginContainer.classList.remove('login-form-hidden');
                loginContainer.classList.add('login-form-visible');
                this.animateFormElements(loginContainer);
            }
        }, this.config.fadeOutDuration);
    },

    // Check conditions to hide boot screen
    checkHideConditions: function() {
        const minTimeElapsed = Date.now() - this.state.startTime >= this.config.minDisplayTime;
        const resourcesReady = this.checkResourcesLoaded();

        if (minTimeElapsed && (resourcesReady || this.state.resourcesLoaded)) {
            this.hide();
        } else if (minTimeElapsed) {
            // Wait a bit more for resources
            setTimeout(() => {
                this.hide();
            }, 1000);
        }
    },

    // Check if critical resources are loaded
    checkResourcesLoaded: function() {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        const scripts = document.querySelectorAll('script[src]');
        let loadedCount = 0;
        let totalCount = stylesheets.length + scripts.length;

        // Check stylesheets
        stylesheets.forEach(link => {
            if (link.sheet || link.readyState === 'complete') {
                loadedCount++;
            }
        });

        // Check scripts
        scripts.forEach(script => {
            if (script.readyState === 'complete' || script.readyState === 'loaded') {
                loadedCount++;
            }
        });

        return totalCount > 0 ? (loadedCount / totalCount) >= 0.8 : true;
    },

    // Animate progress bar
    animateProgress: function() {
        const progressBar = document.getElementById('boot-progress-bar');
        if (!progressBar) return;

        const steps = this.config.progressSteps;
        const stepDuration = this.config.progressDuration / steps.length;

        steps.forEach((width, index) => {
            setTimeout(() => {
                progressBar.style.width = width + '%';
            }, index * stepDuration);
        });
    },

    // Update progress based on actual loading
    updateProgress: function() {
        const progressBar = document.getElementById('boot-progress-bar');
        if (!progressBar) return;

        const loadingPercentage = this.getLoadingPercentage();
        progressBar.style.width = Math.min(loadingPercentage, 95) + '%';
    },

    // Get actual loading percentage
    getLoadingPercentage: function() {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        const scripts = document.querySelectorAll('script[src]');
        let loadedCount = 0;
        let totalCount = stylesheets.length + scripts.length;

        stylesheets.forEach(link => {
            if (link.sheet || link.readyState === 'complete') {
                loadedCount++;
            }
        });

        scripts.forEach(script => {
            if (script.readyState === 'complete' || script.readyState === 'loaded') {
                loadedCount++;
            }
        });

        return totalCount > 0 ? Math.round((loadedCount / totalCount) * 100) : 100;
    },

    // Animate form elements entrance
    animateFormElements: function(container) {
        const elements = container.querySelectorAll('.modern-input-group, .modern-login-button');
        
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.4s ease-out';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * this.config.formAnimationDelay);
        });
    },

    // Enhance boot screen with additional effects
    enhanceBootScreen: function() {
        const bootScreen = document.getElementById('boot-screen');
        if (!bootScreen) return;

        // Add particle effect (optional)
        this.addParticleEffect(bootScreen);

        // Add keyboard shortcuts
        this.addKeyboardShortcuts();
    },

    // Add subtle particle effect
    addParticleEffect: function(container) {
        // Create floating particles for visual enhancement
        for (let i = 0; i < 5; i++) {
            const particle = document.createElement('div');
            particle.className = 'boot-particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(255,255,255,0.3);
                border-radius: 50%;
                animation: bootFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 2}s;
            `;
            container.appendChild(particle);
        }

        // Add CSS for particle animation
        if (!document.getElementById('boot-particle-styles')) {
            const style = document.createElement('style');
            style.id = 'boot-particle-styles';
            style.textContent = `
                @keyframes bootFloat {
                    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
                    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);
        }
    },

    // Add keyboard shortcuts
    addKeyboardShortcuts: function() {
        document.addEventListener('keydown', (e) => {
            // Press 'S' to skip boot screen (for development)
            if (e.key.toLowerCase() === 's' && this.state.isVisible) {
                this.hide();
            }
        });
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.bootScreen.init();
    });
} else {
    window.bootScreen.init();
}

// Export for use in other scripts
window.bootScreenManager = window.bootScreen;
