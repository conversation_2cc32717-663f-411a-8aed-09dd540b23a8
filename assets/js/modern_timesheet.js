/**
 * Modern Timesheet JavaScript Functionality
 * Handles sidebar toggle, tab switching, and mobile interactions
 */

window.modernTimesheet = {
    // State management
    state: {
        sidebarOpen: true,
        activeTab: 'id-isc-ts-layer-2',
        isMobile: window.innerWidth <= 768
    },

    // Initialize the timesheet functionality
    init: function() {
        this.bindEvents();
        this.handleResize();
        this.initializeTabs();
        
        // Set initial state
        this.updateLayout();
        
        console.log('Modern Timesheet initialized');
    },

    // Bind event listeners
    bindEvents: function() {
        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Click outside sidebar to close on mobile
        document.addEventListener('click', (e) => {
            if (this.state.isMobile && this.state.sidebarOpen) {
                const sidebar = document.querySelector('.modern-sidebar');
                const toggleButton = document.querySelector('.modern-toggle-button');
                
                if (sidebar && !sidebar.contains(e.target) && !toggleButton.contains(e.target)) {
                    this.closeSidebar();
                }
            }
        });

        // Tab button clicks
        document.addEventListener('click', (e) => {
            const tabButton = e.target.closest('.tab-button');
            if (tabButton) {
                e.preventDefault();
                const tabId = tabButton.id;
                const layerId = tabId.replace('bullet', 'layer');
                this.switchTab(layerId);
            }
        });

        // Escape key to close sidebar on mobile
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.state.isMobile && this.state.sidebarOpen) {
                this.closeSidebar();
            }
        });
    },

    // Handle window resize
    handleResize: function() {
        const wasMobile = this.state.isMobile;
        this.state.isMobile = window.innerWidth <= 768;
        
        // If switching from mobile to desktop, ensure sidebar is visible
        if (wasMobile && !this.state.isMobile) {
            this.state.sidebarOpen = true;
        }
        
        this.updateLayout();
    },

    // Initialize tab functionality
    initializeTabs: function() {
        // Set first tab as active by default
        const firstTab = document.querySelector('.tab-button');
        const firstPanel = document.querySelector('.tab-panel');
        
        if (firstTab && firstPanel) {
            firstTab.classList.add('active');
            firstPanel.classList.add('active');
            this.state.activeTab = firstPanel.id;
        }
    },

    // Toggle sidebar visibility
    toggleSidebar: function() {
        this.state.sidebarOpen = !this.state.sidebarOpen;
        this.updateLayout();
        
        // Trigger Dash callback if needed
        this.triggerDashCallback();
    },

    // Open sidebar
    openSidebar: function() {
        this.state.sidebarOpen = true;
        this.updateLayout();
    },

    // Close sidebar
    closeSidebar: function() {
        this.state.sidebarOpen = false;
        this.updateLayout();
    },

    // Update layout based on current state
    updateLayout: function() {
        const layout = document.querySelector('.modern-timesheet-layout');
        const sidebar = document.querySelector('.modern-sidebar');
        const mainContent = document.querySelector('.modern-main-content');
        const toggleButton = document.querySelector('.modern-toggle-button');
        
        if (!layout || !sidebar || !mainContent || !toggleButton) return;

        if (this.state.isMobile) {
            // Mobile layout
            layout.classList.remove('sidebar-collapsed');
            mainContent.classList.remove('expanded');
            
            if (this.state.sidebarOpen) {
                sidebar.classList.add('mobile-open');
                sidebar.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            } else {
                sidebar.classList.remove('mobile-open');
                sidebar.classList.add('hidden');
                document.body.style.overflow = '';
            }
        } else {
            // Desktop layout
            sidebar.classList.remove('mobile-open');
            document.body.style.overflow = '';
            
            if (this.state.sidebarOpen) {
                layout.classList.remove('sidebar-collapsed');
                sidebar.classList.remove('hidden');
                mainContent.classList.remove('expanded');
                toggleButton.classList.remove('active');
            } else {
                layout.classList.add('sidebar-collapsed');
                sidebar.classList.add('hidden');
                mainContent.classList.add('expanded');
                toggleButton.classList.add('active');
            }
        }
    },

    // Switch between tabs
    switchTab: function(targetTabId) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-button').forEach(tab => {
            tab.classList.remove('active');
        });
        
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Add active class to target tab and panel
        const targetButton = document.getElementById(targetTabId.replace('layer', 'bullet'));
        const targetPanel = document.getElementById(targetTabId);
        
        if (targetButton && targetPanel) {
            targetButton.classList.add('active');
            targetPanel.classList.add('active');
            this.state.activeTab = targetTabId;
            
            // Update page title based on active tab
            this.updatePageTitle(targetTabId);
        }
    },

    // Update page title based on active tab
    updatePageTitle: function(tabId) {
        const titleElement = document.getElementById('id-wd-ts-main-header');
        if (!titleElement) return;
        
        const titles = {
            'id-isc-ts-layer-2': 'WD Timesheet',
            'id-isc-ts-layer-3': 'WD Effort Spent Chart',
            'id-isc-ts-layer-5': 'WD Subtask Effort Spent'
        };
        
        const newTitle = titles[tabId] || 'WD Timesheet';
        titleElement.textContent = newTitle;
    },

    // Trigger Dash callback for toggle functionality
    triggerDashCallback: function() {
        // This will be handled by the existing Dash callback
        // We just need to ensure the button click is registered
        const toggleButton = document.querySelector('[id*="toggle-panel"]');
        if (toggleButton) {
            // Simulate click to trigger Dash callback
            toggleButton.click();
        }
    },

    // Handle file upload styling
    handleFileUpload: function(files) {
        const uploadArea = document.querySelector('.modern-upload');
        if (!uploadArea) return;
        
        if (files && files.length > 0) {
            uploadArea.style.borderColor = 'var(--timesheet-secondary)';
            uploadArea.style.background = 'rgba(82, 196, 26, 0.1)';
            
            // Show process button
            const processButton = document.getElementById('id-run-gs-jira');
            if (processButton) {
                processButton.classList.remove('hidden');
            }
        } else {
            uploadArea.style.borderColor = 'var(--timesheet-border)';
            uploadArea.style.background = 'var(--timesheet-light-gray)';
        }
    },

    // Show loading state for data grid
    showGridLoading: function() {
        const gridContainer = document.querySelector('.data-grid-container');
        if (gridContainer) {
            gridContainer.classList.add('loading');
        }
    },

    // Hide loading state for data grid
    hideGridLoading: function() {
        const gridContainer = document.querySelector('.data-grid-container');
        if (gridContainer) {
            gridContainer.classList.remove('loading');
        }
    },

    // Utility function to check if element is visible
    isElementVisible: function(element) {
        return element && element.offsetParent !== null;
    },

    // Get current state for debugging
    getState: function() {
        return { ...this.state };
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure all Dash components are rendered
    setTimeout(() => {
        window.modernTimesheet.init();
    }, 100);
});

// Export for Dash clientside callbacks
window.dash_clientside = window.dash_clientside || {};
window.dash_clientside.modernTimesheet = {
    
    /**
     * Toggle sidebar functionality for Dash callback
     */
    toggleSidebar: function(n_clicks) {
        if (!n_clicks) return window.dash_clientside.no_update;
        
        if (window.modernTimesheet) {
            window.modernTimesheet.toggleSidebar();
        }
        
        return window.dash_clientside.no_update;
    },

    /**
     * Handle tab switching for Dash callback
     */
    switchTab: function(n_clicks_2, n_clicks_3, n_clicks_5) {
        const ctx = window.dash_clientside.callback_context;
        if (!ctx.triggered.length) return window.dash_clientside.no_update;
        
        const triggerId = ctx.triggered[0].prop_id.split('.')[0];
        
        if (window.modernTimesheet) {
            const layerId = triggerId.replace('bullet', 'layer');
            window.modernTimesheet.switchTab(layerId);
        }
        
        return window.dash_clientside.no_update;
    },

    /**
     * Handle file upload feedback
     */
    handleFileUpload: function(contents, filename) {
        if (window.modernTimesheet) {
            window.modernTimesheet.handleFileUpload(contents ? [filename] : null);
        }
        
        return window.dash_clientside.no_update;
    }
};

// Global resize handler
window.addEventListener('resize', function() {
    if (window.modernTimesheet) {
        window.modernTimesheet.handleResize();
    }
});
