import datetime
from typing import Annotated

from sqlalchemy.orm import mapped_column, Mapped

from .base import Base, TableName
from sqlalchemy import Integer
from sqlalchemy.schema import Index

from sqlalchemy.dialects.postgresql import TEXT

int_pk = Annotated[int, mapped_column(primary_key=True, autoincrement=False)]
required_text = Annotated[str, mapped_column(TEXT, nullable=False)]
required_int = Annotated[int, mapped_column(Integer, nullable=False)]


class Versions(Base, TableName):
    id: Mapped[int_pk]
    description: Mapped[required_text]
    name: Mapped[required_text]
    archived: Mapped[bool]
    released: Mapped[bool]
    startDate: Mapped[datetime.date]
    userStartDate: Mapped[datetime.date]
    releaseDate: Mapped[datetime.date]
    userReleaseDate: Mapped[datetime.date]
    overdue: Mapped[bool]
    projectId: Mapped[required_int]
    issuesUnresolvedCount: Mapped[required_int]
    issuesCount: Mapped[required_int]
    issuesFixedCount: Mapped[required_int]
    issuesAffectedCount: Mapped[required_int]
    issueCountWithCustomFieldsShowingVersion: Mapped[required_int]
    __table__args = (
        Index(
            'ix_versions_name_trgm',
            "name",
            postgresql_using='gin',
            postgresql_ops={'name': 'gin_trgm_ops'},
        )
    )
