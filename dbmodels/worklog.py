import numpy as np
from numpy import signedinteger
from numpy._typing import _32Bit, _64Bit
from sqlalchemy import String, ForeignKey, Integer, DateTime
from sqlalchemy.dialects.postgresql.json import JSON
from sqlalchemy.ext.hybrid import hybrid_property

from sqlalchemy.orm import  mapped_column, Mapped
from sqlalchemy.sql.schema import Computed
from sqlalchemy.sql.sqltypes import Numeric
from .base import Base, TableName


class WorkLog(Base, TableName):
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=False)

    author: Mapped[str] = mapped_column(
        String, ForeignKey("user.accountId"), nullable=False, index=True
    )

    updateauthor: Mapped[str] = mapped_column(
        String, ForeignKey("user.accountId"), nullable=False
    )

    created: Mapped[DateTime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )

    updated: Mapped[DateTime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )

    started: Mapped[DateTime] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    timeSpent: Mapped[str] = mapped_column(String, nullable=False)

    timeSpentSeconds: Mapped[int] = mapped_column(Integer, nullable=False)

    timeSpentHours: Mapped[float] = mapped_column(
        Numeric,
        Computed("timeSpentSeconds / 3600", persisted=True)
    )

    comment: Mapped[dict] = mapped_column(JSON)

    issue_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("issue.id", deferrable=True, initially="DEFERRED"),
        nullable=False,
        index=True,
    )

    issue_key: Mapped[str] = mapped_column(
        String,
        ForeignKey("issue.key", deferrable=True, initially="DEFERRED"),
        nullable=False,
        index=True,
    )

    @hybrid_property
    def business_days(self) -> signedinteger[_32Bit | _64Bit] | None:
        if not self.started or not self.updated:
            return None
        try:
            start = np.datetime64(self.started)
            end = np.datetime64(self.updated)
            return np.busday_count(start, end)
        except (ValueError, TypeError):
            return None

    # author_name = column_property(select(User.displayName).where(User.accountId == author).scalar_subquery())
    # team_name = column_property(
    #     select(Teams.team_name).where(Teams.accountId == author) \
    #         .filter(or_(Teams.endDate > created, Teams.endDate == None)) \
    #         .filter(or_(Teams.startDate < created, Teams.startDate.is_(None))).scalar_subquery())

    # issue = relationship("Issue", back_populates='worklogs', primaryjoin="Issue.key == WorkLog.issue_key")
    # team_created = relationship(
    #     "Teams",
    #     primaryjoin=and_(author == foreign(Teams.accountId), created.between(Teams.startDate, Teams.endDate)),
    #     viewonly=True
    # )
    # team_started = relationship(
    #     "Teams",
    #     primaryjoin=and_(author == foreign(Teams.accountId), started.between(Teams.startDate, Teams.endDate)),
    #     viewonly=True
    # )
