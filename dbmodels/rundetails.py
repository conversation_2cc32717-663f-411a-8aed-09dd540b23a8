from datetime import datetime
from typing import Optional

from sqlalchemy import MetaData, Table, String, Integer, Column
from sqlalchemy.dialects.postgresql import TEXT
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base, TableName


class RunDetails(Base):
    __tablename__ = 'Run_Details'
    Topic: Mapped[str] = mapped_column(TEXT, primary_key=True)
    LAST_RUN: Mapped[datetime] = mapped_column(nullable=False)


# For system tables like pg_stat_activity, we still use the Table constructor
# as they're not ORM models but rather reflections of system tables
metadata_obj = MetaData(schema=None)
PGStatActivity = Table(
    "pg_stat_activity",
    metadata_obj,
    Column('datname', String, primary_key=True),
    <PERSON>umn('usename', String, primary_key=True),
    <PERSON>umn('pid', Integer, primary_key=True),
    <PERSON>umn('state', String),
)