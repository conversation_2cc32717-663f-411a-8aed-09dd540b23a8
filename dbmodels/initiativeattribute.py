from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import ForeignKey, Index, func
from sqlalchemy.dialects.postgresql import HSTORE, TEXT
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TableName
from .issue import Issue


class InitiativeAttribute(Base, TableName):
    initiative_id: Mapped[int] = mapped_column(ForeignKey(Issue.id), primary_key=True, nullable=False)
    initiative_key: Mapped[str] = mapped_column(nullable=False, unique=True, index=True)
    attr: Mapped[Dict[str, Any]] = mapped_column(
        MutableDict.as_mutable(HSTORE),
        nullable=False,
        default=dict,
        server_default=None
    )
    project: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    release: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    feature: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        server_default=func.now(),
        onupdate=func.now()
    )

    # Relationships
    initiative: Mapped["Issue"] = relationship(
        "Issue", backref="initiative_attributes"
    )

    __table_args__ = (
        Index("ix_attr", attr, postgresql_using='gist'),
    )
