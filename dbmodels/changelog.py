from datetime import datetime
from typing import Optional

from sqlalchemy import Foreign<PERSON>ey, String
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>
from sqlalchemy.ext.indexable import index_property
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TableName
from .user import User
from .issue import Issue


# Source: https://docs.sqlalchemy.org/en/20/orm/extensions/indexable.html
class PgJsonProperty(index_property):
    def __init__(self, attr_name, index, cast_type):
        super(PgJsonProperty, self).__init__(attr_name, index)
        self.cast_type = cast_type

    def expr(self, model):
        expr = super(PgJsonProperty, self).expr(model)
        return expr.astext.cast(self.cast_type)


class ChangeLog(Base, TableName):
    id: Mapped[int] = mapped_column(nullable=False, primary_key=True)
    author: Mapped[str] = mapped_column(ForeignKey(User.accountId), nullable=False)
    created: Mapped[datetime] = mapped_column(nullable=False)
    field: Mapped[str] = mapped_column(nullable=False, primary_key=True)
    fieldtype: Mapped[str] = mapped_column(nullable=False)
    fieldId: Mapped[Optional[str]] = mapped_column(nullable=True)
    from_: Mapped[str] = mapped_column(String, nullable=False, primary_key=True)
    fromString: Mapped[Optional[str]] = mapped_column(nullable=True)
    to: Mapped[str] = mapped_column(nullable=False, primary_key=True)
    toString: Mapped[Optional[str]] = mapped_column(nullable=True)
    issue_key: Mapped[str] = mapped_column(ForeignKey(Issue.key), nullable=False, index=True)
    issue_id: Mapped[int] = mapped_column(ForeignKey(Issue.id), nullable=False, index=True)

    # Relationships
    author_user: Mapped["User"] = relationship(
        "User", foreign_keys=[author], backref="authored_changelogs"
    )
    issue: Mapped["Issue"] = relationship(
        "Issue", foreign_keys=[issue_id], backref="changelogs"
    )

    __table_args__ = (
        {'schema': None, 'extend_existing': True},
    )


class ChangelogJSON(Base):
    use_snake_case = True
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=False)
    author: Mapped[str] = mapped_column(ForeignKey(User.accountId), nullable=False)
    created: Mapped[datetime] = mapped_column(nullable=False)
    items: Mapped[dict] = mapped_column(JSONB)
    issue_key: Mapped[str] = mapped_column(ForeignKey(Issue.key), nullable=False, index=True)
    issue_id: Mapped[int] = mapped_column(ForeignKey(Issue.id), nullable=False, index=True)

    # Relationships
    author_user: Mapped["User"] = relationship(
        "User", foreign_keys=[author], backref="authored_json_changelogs"
    )
    issue: Mapped["Issue"] = relationship(
        "Issue", foreign_keys=[issue_id], backref="json_changelogs"
    )

    # Index properties for JSONB fields
    field = index_property(attr_name='items', index='field', default=None)
    fieldtype = index_property('items', 'fieldtype', default=None)
    fieldId = index_property('items', 'fieldId', default=None)
    from_value = PgJsonProperty('items', 'from', String)
    fromString = index_property('items', 'fromString', default=None)
    to = PgJsonProperty('items', 'to', String)
    toString = index_property('items', 'toString', default=None)
