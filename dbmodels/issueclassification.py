from .base import Base, TableName
from .issue import Issue
from sqlalchemy import Column, ForeignKey, Index
from sqlalchemy.dialects.postgresql import TEXT, BIGINT, ENUM
from sqlalchemy_utils import LtreeType
from enum import Enum, unique
from sqlalchemy.orm import relationship

@unique
class IssueClassificationEnum(Enum):
    initiative = 1
    epic = 2
    standard = 3
    subtask = 4


class IssueClassification(Base, TableName):
    """
    Defines data model to store 4 level hierarchy for a given issue key/id
    """
    use_snake_case = True
    id = Column(BIGINT, ForeignKey(Issue.id), primary_key=True, unique=True)
    key = Column(TEXT, ForeignKey(Issue.key), index=True, nullable=False, unique=True)
    issueclass = Column(ENUM(IssueClassificationEnum), nullable=False)
    path_id = Column(LtreeType, nullable=True)
    path_key = Column(LtreeType, nullable=True)
    initiative_key = Column(TEXT, nullable=True)
    epic_key = Column(TEXT, nullable=True)
    standard_key = Column(TEXT, nullable=True)
    subtask_key = Column(TEXT, nullable=True)
    initiative_id = Column(BIGINT, nullable=True)
    epic_id = Column(BIGINT, nullable=True)
    standard_id = Column(BIGINT, nullable=True)
    subtask_id = Column(BIGINT, nullable=True)

    # child_classification_id = relationship("Issue", back_populates="classification_id",
    #                                        foreign_keys="[IssueClassification.id]", uselist=False, innerjoin=True)
    myself = relationship(
        "Issue", backref='issueclassification_self',
        primaryjoin="IssueClassification.key == Issue.key",
        foreign_keys="IssueClassification.key"
    )

    standard = relationship(
        "Issue", backref='issueclassification_stan',
        primaryjoin="IssueClassification.standard_id == Issue.id",
        foreign_keys="IssueClassification.standard_id"
    )

    epic = relationship(
        "Issue", backref='issueclassification',
        primaryjoin="IssueClassification.epic_key == Issue.key",
        foreign_keys=Issue.key
    )

    initiative_attr = relationship(
        "InitiativeAttribute", backref='issueclassification',
        primaryjoin="IssueClassification.initiative_key == InitiativeAttribute.initiative_key",
        foreign_keys="InitiativeAttribute.initiative_key"
    )