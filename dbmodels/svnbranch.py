from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import TEXT, ARRAY, TIMESTAMP, INTEGER, SMALLINT, BOOLEAN
from sqlalchemy_utils import EmailType
from .base import Base, TableNameCamelCase


class SVNBase:
    __abstract__ = True
    name = Column(TEXT, primary_key=True, index=True, nullable=False)
    kind = Column(TEXT, nullable=False)
    revision = Column(INTEGER, nullable=False)
    author = Column(TEXT, nullable=False)
    author_email = Column(EmailType, nullable=False, index=True)
    svn_server = Column(TEXT, nullable=False)
    created_on = Column(TIMESTAMP(timezone=True), nullable=False)


class SVNBranch(Base, TableNameCamelCase, SVNBase):
    use_snake_case = True
    branch_directory = Column(TEXT, nullable=False)


class SVNPackage(Base, TableNameCamelCase, SVNBase):
    use_snake_case = True
    package_directory = Column(TEXT, nullable=False)


class SVNCheckIn:
    __abstract__ = True
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(EmailType, nullable=False, index=True)
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    comment = Column(TEXT)
    action = Column(TEXT)
    prop_mods = Column(TEXT, nullable=False)
    text_mods = Column(TEXT, nullable=False)
    kind = Column(TEXT, nullable=False)
    copyfrom_path = Column(TEXT)
    copyfrom_rev = Column(TEXT)
    cc_jira = Column(ARRAY(TEXT), nullable=True)
    client_jira = Column(ARRAY(TEXT), nullable=True)
    app_indexes = Column(BOOLEAN, nullable=False)
    report_indexes = Column(BOOLEAN, nullable=False)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class SVNBranchCheckIn(Base, TableNameCamelCase, SVNCheckIn):
    use_snake_case = True
    branch_name = Column(TEXT, nullable=False, primary_key=True)


class SVNPackageCheckIn(Base, TableNameCamelCase, SVNCheckIn):
    use_snake_case = True
    package_name = Column(TEXT, nullable=False, primary_key=True)


class CCJiraBaseCheckin:
    __abstract__ = True
    revision = Column(INTEGER, nullable=False, primary_key=True)
    filename = Column(TEXT, nullable=False, primary_key=True)
    cc_jira = Column(TEXT, nullable=False, primary_key=True)
    author = Column(TEXT, nullable=False)
    author_email = Column(EmailType, nullable=False, index=True)
    checked_in_on = Column(TIMESTAMP(timezone=True), nullable=False, index=True)
    app_index_change = Column(SMALLINT, nullable=True)
    report_index_change = Column(SMALLINT, nullable=True)
    primary_schema_change = Column(SMALLINT, nullable=True)
    primary_sql_change = Column(SMALLINT, nullable=True)
    conversion_script = Column(SMALLINT, nullable=True)
    control_parameters = Column(SMALLINT, nullable=True)
    svn_server = Column(TEXT, nullable=False, primary_key=True)


class CCJiraBranchCheckIn(Base, TableNameCamelCase, CCJiraBaseCheckin):
    use_snake_case = True
    branch_name = Column(TEXT, nullable=False, primary_key=True)


class CCJiraPackageCheckIn(Base, TableNameCamelCase, CCJiraBaseCheckin):
    use_snake_case = True
    package_name = Column(TEXT, nullable=False, primary_key=True)