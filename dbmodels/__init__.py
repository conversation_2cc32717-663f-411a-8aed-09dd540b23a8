import datetime
from typing import Annotated

from sqlalchemy import func
from sqlalchemy.orm import mapped_column

from .base import Base, TableName, TableNameCamelCase
from .user import User, Role, Permission, RolePermission
from .user_addons import UserRole
from .allboards import AllBoards
from .teams import Teams, RequestTracker, NLPTrainingData
from .worklog import WorkLog
from .changelog import ChangeLog
from .issue import Issue
from .initiativeattribute import InitiativeAttribute
from .versions import Versions
from .issueclassification import IssueClassification
from .rundetails import RunDetails, PGStatActivity
from .platform_version import PlatformVersion
from .svnbranch import SVNBranch, SVNPackage, CCJiraBranchCheckIn, CCJiraPackageCheckIn
from .gs_jira import GSJiraIssues


