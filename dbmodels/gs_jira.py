from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import TEXT, DATE
from .base import Base, TableNameCamelCase


class GSJiraIssues(Base, TableNameCamelCase):
    client_jira_no = Column(TEXT, primary_key=True, index=True)
    issuetype = Column(TEXT, nullable=False)
    summary = Column(TEXT, nullable=False)
    status = Column(TEXT, nullable=False)
    priority = Column(TEXT, nullable=True)
    fix_versions = Column(TEXT, nullable=True)
    labels = Column(TEXT, nullable=True)
    coretrack_id = Column(TEXT, nullable=True)
    client_epic_no = Column(TEXT, nullable=True)
    client_epic_name = Column(TEXT, nullable=True)
    ccb_status = Column(TEXT, nullable=True)
    estimate_submitted = Column(TEXT, nullable=True)
    effort_estimates = Column(TEXT, nullable=True)
    created = Column(DATE, nullable=False)
    updated = Column(DATE, nullable=False)