<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Logout Menu Demo - JIRA Dashboard</title>
    <link rel="stylesheet" href="assets/css/modern_logout.css">
    <script src="https://code.iconify.design/3/3.1.1/iconify.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .demo-header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .demo-content {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #1a1a1a;
        }
        
        .demo-button {
            background: #1890FF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .demo-button:hover {
            background: #40A9FF;
        }
        
        .demo-button.secondary {
            background: #f5f5f5;
            color: #666;
        }
        
        .demo-button.secondary:hover {
            background: #e6f7ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <!-- Demo Header with User Menu -->
    <div class="demo-header">
        <h1 class="demo-title">JIRA Dashboard</h1>
        
        <!-- Modern User Menu -->
        <div class="user-menu-container">
            <button class="user-menu-trigger" id="user-menu-trigger">
                <img src="https://via.placeholder.com/32x32/1890FF/white?text=JD" alt="User Avatar" class="user-avatar" id="user-avatar-img">
                <span class="user-name">John Doe</span>
                <i class="iconify dropdown-icon" data-icon="mdi:chevron-down"></i>
            </button>
            
            <div class="user-dropdown-menu" id="user-dropdown-menu">
                <div class="user-dropdown-header">
                    <img src="https://via.placeholder.com/48x48/1890FF/white?text=JD" alt="User Avatar" class="user-avatar">
                    <div class="user-info">
                        <div class="user-display-name">John Doe</div>
                        <div class="user-email"><EMAIL></div>
                    </div>
                </div>
                <hr class="dropdown-divider">
                <div class="dropdown-menu-items">
                    <a href="#" class="dropdown-item" id="profile-link">
                        <i class="iconify item-icon" data-icon="mdi:account-circle"></i>
                        <span>Profile</span>
                    </a>
                    <a href="#" class="dropdown-item" id="settings-link">
                        <i class="iconify item-icon" data-icon="mdi:cog"></i>
                        <span>Settings</span>
                    </a>
                    <hr class="dropdown-divider">
                    <button class="dropdown-item logout-item" id="logout-button">
                        <i class="iconify item-icon" data-icon="mdi:logout"></i>
                        <span>Sign Out</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Content -->
    <div class="demo-content">
        <div class="demo-section">
            <h2>Modern Logout Menu Demo</h2>
            <p>This demonstrates the new modern logout menu that replaces the old Angular-based logout system.</p>
            
            <h3>Features:</h3>
            <ul>
                <li>✅ Modern dropdown design with smooth animations</li>
                <li>✅ User avatar and information display</li>
                <li>✅ Profile and Settings links (placeholder)</li>
                <li>✅ Confirmation dialog for logout</li>
                <li>✅ Keyboard navigation support</li>
                <li>✅ Mobile responsive design</li>
                <li>✅ Accessibility features (ARIA attributes)</li>
            </ul>
            
            <h3>Try it out:</h3>
            <p>Click on the user avatar in the top-right corner to open the dropdown menu, then try the logout functionality.</p>
            
            <button class="demo-button" onclick="showLogoutPage()">Preview Logout Page</button>
            <button class="demo-button secondary" onclick="resetDemo()">Reset Demo</button>
        </div>
        
        <div class="demo-section">
            <h2>Integration Notes</h2>
            <p>The new logout system integrates seamlessly with your existing Dash application:</p>
            
            <ul>
                <li><strong>No Angular dependency:</strong> Pure JavaScript and Dash implementation</li>
                <li><strong>Consistent styling:</strong> Matches your modern login page design</li>
                <li><strong>Better UX:</strong> Confirmation dialogs and smooth transitions</li>
                <li><strong>Accessible:</strong> Proper ARIA attributes and keyboard navigation</li>
                <li><strong>Mobile-friendly:</strong> Responsive design for all screen sizes</li>
            </ul>
        </div>
    </div>

    <!-- Logout Page Preview (hidden by default) -->
    <div id="logout-page-preview" style="display: none;">
        <div class="modern-logout-page">
            <div class="logout-background"></div>
            <div class="logout-content">
                <div class="logout-icon-container">
                    <i class="iconify logout-icon" data-icon="mdi:check-circle" style="color: #52c41a;"></i>
                </div>
                <h1 class="logout-title">You've Successfully Signed Out</h1>
                <p class="logout-subtitle">Thanks for using JIRA Dashboard. Your session has been securely ended.</p>
                <div class="logout-actions">
                    <a href="#" class="logout-primary-button" onclick="hideLogoutPage()">Sign In Again</a>
                    <a href="#" class="logout-secondary-button" onclick="hideLogoutPage()">Go to Home</a>
                </div>
                <div class="logout-footer">
                    <p class="logout-security-note">For security reasons, please close your browser if you're on a shared computer.</p>
                    <div class="logout-countdown" id="logout-countdown">Redirecting to login in 10 seconds...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/modern_logout.js"></script>
    <script>
        // Demo-specific functions
        function showLogoutPage() {
            document.getElementById('logout-page-preview').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function hideLogoutPage() {
            document.getElementById('logout-page-preview').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        function resetDemo() {
            // Close any open dropdowns
            const dropdown = document.getElementById('user-dropdown-menu');
            const trigger = document.getElementById('user-menu-trigger');
            
            if (dropdown && trigger) {
                dropdown.classList.remove('show');
                trigger.classList.remove('active');
            }
            
            // Hide logout page if visible
            hideLogoutPage();
            
            // Remove any dialogs
            const dialogs = document.querySelectorAll('.logout-confirm-dialog');
            dialogs.forEach(dialog => dialog.remove());
            
            // Remove any notifications
            const notifications = document.querySelectorAll('.modern-notification');
            notifications.forEach(notification => notification.remove());
        }
        
        // Override the performLogout function for demo
        const originalPerformLogout = window.modernLogout.performLogout;
        window.modernLogout.performLogout = function(button) {
            const dialog = button.closest('.logout-confirm-dialog');
            
            // Show loading state
            button.innerHTML = '<i class="iconify" data-icon="mdi:loading" style="animation: spin 1s linear infinite;"></i> Signing out...';
            button.disabled = true;
            
            // Show logout page instead of redirecting
            setTimeout(() => {
                dialog.remove();
                showLogoutPage();
            }, 1000);
        };
        
        // Set demo base path
        window.dashBasePath = '/';
    </script>
</body>
</html>
