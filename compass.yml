name: jiradashboard
id: 'ari:cloud:compass:7dee1656-45c5-4efe-9130-da5c70912ae6:component/c24b9fcc-ea67-46a3-859f-772928cfe7b8/bd693302-13c9-407a-bd49-58fb82761079'
description: Imported from a Bitbucket repository linked to the DASH Jira project.
configVersion: 1
typeId: SERVICE
ownerId: 'ari:cloud:identity::team/148589c8-ff4d-4785-802a-a325b01bdea4'
fields:
  tier: 4
links:
  - name: jiradashboard
    type: REPOSITORY
    url: 'https://bitbucket.org/visby8em/jiradashboard'
  - name: DASH
    type: PROJECT
    url: 'https://biyani.atlassian.net/browse/DASH'
relationships: {}
labels:
  - jira-import
  - synced-with-jsm
customFields: null

# Learn more about formatting compass.yml:
# https://go.atlassian.com/compass-yml-format