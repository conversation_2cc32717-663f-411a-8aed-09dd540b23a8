import dash

from dash import dash_table, html, dcc, clientside_callback
import plotly.graph_objs as go
from dash.dependencies import Input, Output

external_stylesheets = [

]

external_scripts = [
    {
        'src': 'https://kit.fontawesome.com/4e20f97c7e.js',
        'crossorigin': 'anonymous'
    },
]
# Initialize the Dash app
app = dash.Dash(
    __name__,
    external_scripts=external_scripts
)

# External CSS for Font Awesome
# app.css.append_css({
#     'external_url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css'
# })

# Define the app layout
status = [True, False, True, False, True, True, False]

# Example services list
services = [
    "InVision", "Craft Sync", "Sketch processing", "Blog",
    "Billing", "LiveShare", "Private Cloud/Network"
]

# Generate child tags based on status and services lists

# Generate child tags based on status and services lists
child_tag = []
for i in range(0, len(services), 4):
    row_children = []
    for j in range(4):
        if i + j < len(services):
            service = services[i + j]
            is_operational = status[i + j]
            icon = "fas fa-check-circle" if is_operational else "fas fa-times-circle"
            icon_color = '#2ECC40' if is_operational else '#FF4136'
            width_style = '30%' if j % 2 == 1 else '50%'  # 50% for columns 1 and 3, 30% for columns 2 and 4
            row_children.append(
                html.Div(
                    children=[
                        html.Span(service, className="col", style={'width': '80%' if j % 2 == 0 else '20%'}),
                        html.I(className=f"{icon} col",
                               style={'color': icon_color, 'width': '20%' if j % 2 == 0 else '80%'})
                    ],
                    className="col",
                    style={
                        'flex': '1',
                        'padding': '10px',
                        'border': '1px solid #ccc',
                        'display': 'flex',
                        'justify-content': 'space-between',
                    }
                )
            )
        else:
            row_children.append(
                html.Div(
                    children=[],
                    className="col",
                    style={'flex': '1', 'padding': '10px', 'border': '1px solid #ccc'}
                )
            )
    child_tag.append(html.Div(children=row_children, className="row", style={
        'display': 'flex',
        'justify-content': 'space-between',
        'margin': '10px 0',
        'padding': '0 10%'  # 10% padding on both sides
    }))

app.layout = [
    html.H2('Partially Degraded Service', style={'backgroundColor': '#F4B400', 'padding': '10px', 'color': 'white'}),
    html.Div(
        'Refreshed less than 1 minute ago', style={'text-align': 'right', 'font-size': '12px', 'color': '#555'},
        id="refresh-time"
    ),
    html.Div(children=child_tag, className="service-status"),

    dash_table.DataTable(
        id='service-table',
        columns=[
            {"name": "Service", "id": "Service1"},
            {"name": "Status", "id": "Status1"},
            {"name": "Service", "id": "Service2"},
            {"name": "Status", "id": "Status2"}
        ],
        data=[
            {"Service1": "InVision", "Status1": "operational", "Service2": "InVision Sync", "Status2": "operational"},
            {"Service1": "Craft Sync", "Status1": "operational", "Service2": "PSD processing",
             "Status2": "operational"},
            {"Service1": "Sketch processing", "Status1": "operational", "Service2": "WWW", "Status2": "operational"},
            {"Service1": "Blog", "Status1": "operational", "Service2": "Support Portal", "Status2": "operational"},
            {"Service1": "Billing", "Status1": "operational", "Service2": "InVision Marketplace",
             "Status2": "operational"},
            {"Service1": "LiveShare", "Status1": "operational", "Service2": "Freehand", "Status2": "operational"},
            {"Service1": "Private Cloud/Network", "Status1": "partial outage", "Service2": "", "Status2": ""}
        ],
        style_cell={'textAlign': 'left', 'padding': '10px'},
        style_header={'display': 'none'},
        style_as_list_view=True,
        style_data_conditional=[
            {
                'if': {'column_id': 'Status1', 'filter_query': '{Status1} contains "operational"'},
                'backgroundColor': '#DFF2BF',
                'color': 'black',
            },
            {
                'if': {'column_id': 'Status2', 'filter_query': '{Status2} contains "operational"'},
                'backgroundColor': '#DFF2BF',
                'color': 'black',
            },
            {
                'if': {'column_id': 'Status1', 'filter_query': '{Status1} contains "partial outage"'},
                'backgroundColor': '#FCE8B2',
                'color': 'black',
            }
        ],
    ),
    html.Div(style={'padding': '20px', 'font-size': '12px'}, children=[
        html.Span([
            html.I(className="fas fa-check-circle", style={'color': '#2ECC40', 'margin-right': '5px'}),
            "Operational"
        ], style={'margin-right': '20px'}),
        html.Span([
            html.I(className="fas fa-exclamation-triangle", style={'color': '#FF851B', 'margin-right': '5px'}),
            "Degraded Performance"
        ], style={'margin-right': '20px'}),
        html.Span([
            html.I(className="fas fa-minus-circle", style={'color': '#FFDC00', 'margin-right': '5px'}),
            "Partial Outage"
        ], style={'margin-right': '20px'}),
        html.Span([
            html.I(className="fas fa-times-circle", style={'color': '#FF4136', 'margin-right': '5px'}),
            "Major Outage"
        ], style={'margin-right': '20px'}),
        html.Span([
            html.I(className="fas fa-tools", style={'color': '#AAAAAA', 'margin-right': '5px'}),
            "Maintenance"
        ])
    ]),
    html.Div(
        children=[
            html.I(className="fa fa-3x fa-spinner fa-pulse", id="id-pulse"),
            html.I(className="fa fa-3x fa-spinner fa-fw fa-sign-in round-class hide", id="id-logon")
        ]
    ),
    dcc.Interval(id='interval-component', interval=1000, n_intervals=0)  # Update every second
]

# Include JavaScript to dynamically update the refresh time
clientside_callback(
    """
    function(n_intervals) {
    if (!window.initialTime) {
            window.initialTime = Math.floor(Date.now() / 1000);
        }
        let currentTime = Math.floor(Date.now() / 1000);
        let elapsed = currentTime - window.initialTime;
        let text = '';
        let class_spinner = "";
        let class_login = ""
        if (elapsed > 3) {
            class_spinner = "fa fa-3x fa-spinner fa-pulse hide";
            class_login = "fa fa-3x fa-spinner fa-fw fa-sign-in round-class"
        }            
        

        if (elapsed < 60) {
            text = `Refreshed ${elapsed} seconds ago`;
        } else {
            let minutes = Math.floor(elapsed / 60);
            text = `Refreshed ${minutes} minutes ago`;
        }
        

        return text, class_spinner, class_login;
    }
    """,
    Output('refresh-time', 'children'),
    Output("id-pulse", "className"),
    Output("id-logon", "className"),
    Input('interval-component', 'n_intervals')
)

# Run the Dash app
if __name__ == '__main__':
    app.run_server(debug=True)
