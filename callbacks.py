# -*- coding: utf-8 -*-
"""
callback functions attached to dash_app layout
"""
import asyncio
import base64
import csv
import io

import os
import re
import struct

from abc import ABC
from collections import namedtuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import date, timedelta, datetime
import time
from html.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Union
from urllib.parse import urlparse, parse_qs

import pandas as pd
import numpy as np
import flask
# import pkcs11
import pyodbc
import sqlalchemy
from Cryptodome.Cipher import AES
from PIL import Image
from aiohttp import client_exceptions

from dash import (
    Output,
    Input, State, callback, html, dcc,
    callback_context, MATCH
)

import dash_mantine_components as dmc
from dash.exceptions import PreventUpdate
from dash_iconify import DashIconify
import plotly.express as px
from dependency_injector.wiring import inject, Provide
from flask import session, current_app
from flask_login import login_user
from flask_principal import identity_changed, Identity

from pandas._libs.tslibs.offsets import MonthEnd
# from pkcs11 import Attribute, ObjectClass, Mechanism, KeyType

from sqlalchemy import text, Engine

# import custom_container
from container import DatabaseContainer
# from app import UserAuth

from data import MyLogger
import data.restapi as api
from data.helper import canonical_form, get_error_message, UserAuth, clicked
import data.get_from_db as db


# Functions | Start
def check_ms_db_connection(ms_db: Engine) -> Union[bool, None]:
    try:
        ms_db.connect()
        connection = True
    except sqlalchemy.exc.OperationalError:
        connection = False
    except pyodbc.OperationalError:
        connection = False
    return connection


def parse_contents(contents, filename, date):
    my_logger = MyLogger().get_logger()

    content_type, content_string = contents.split(',')

    decoded = base64.b64decode(content_string)
    try:
        if 'txt' in filename:
            df = pd.read_csv(
                io.StringIO(decoded.decode('unicode_escape')), sep=" "
            )
        elif 'csv' in filename:
            # Assume that the user uploaded a CSV file
            df = pd.read_csv(io.StringIO(decoded.decode('utf-8')))
        elif 'xlsx' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='openpyxl')
        elif 'xls' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='xlrd')
        elif 'ods' in filename:
            df = pd.read_excel(io.BytesIO(decoded), engine='odf')
        else:
            raise ValueError
    except ValueError as e:
        print(e)
        return html.Div(['File with unsupported file extension uploaded .'])

    json_str = ''
    try:
        memory_usage = df.memory_usage().sum() / 1024 ** 2  # Convert to MB
        max_records = (16_384 - 1_024) // 512  # Leave 1 GB of RAM for other processes
        my_logger.debug(f'Memory usage: {memory_usage:0.2f} MB')

        json_str = df.to_json(orient='split')
    except MemoryError:
        my_logger.info('Unable to convert DataFrame to JSON due to memory constraints.')

    # df.to_json(orient='split')
    return html.Div([
        html.H5(filename, className='white'),
        # html.H6(datetime.fromtimestamp(date), className='white'),

        # dash_table.DataTable(
        #     df.to_dict('records'),
        #     [{'name': i, 'id': i} for i in df.columns]
        # ),
    ]), json_str
def extract_jira_issue_ids(comment):
    pattern = r'(:-PLAT-\d+|:-P20-\d+)'
    if isinstance(comment, str):
        issue_keys = list(set(re.findall(pattern, comment)))
        # remove :- from the list
        return [s.replace(':-', '') for s in issue_keys]
    else:
        return []


def get_issue_keys(comment) -> list:
    pattern = r'(?i)(Cookie-\d+|CARDS-\d+)'
    # Use the regex to find the ID
    # In some instance comment = None
    if isinstance(comment, str):
        match = re.findall(pattern, comment)
        return match
    return []


def get_time_source(comment):
    jira_pattern = r'(:-PLAT-\d+|:-P20-\d+)'
    rt_pattern = r'RT ID:'
    source = 'WD'
    if isinstance(comment, str):
        if re.search(jira_pattern, comment):
            source = 'JIRA'
        elif re.search(rt_pattern, comment):
            source = 'RT'
    return source


def apply_classification(
        project_name: str, gs_ticket_summary: str, task_name: str,
        custom_feature: str, input_feature: str
) -> str:
    if project_name not in ['Plat', 'Plat-Jazz', 'Plat_NEST', 'Plat Stress Test', 'CP_Plat_CI', 'Plat-Lotus',
                            'Plat-CODA']:
        return "Non PSA"
    elif project_name == "Plat Stress Test":
        return "Stress Testing"
    elif custom_feature == "Prod_Managed_PSA":
        return "GS Owned Issue"
    elif custom_feature == "CODA" or project_name == 'Plat-CODA':
        return "CODA"
    elif custom_feature == "Release Management":
        return "Release Mgmt"
    elif custom_feature == "Project Lotus" or project_name == 'Plat-Lotus':
        return "LOTUS"
    elif task_name == "Development Defect Resolution":
        return "Bug Fix"
    elif task_name == "QA Feature Testing":
        return "QA Feature Testing"
    elif task_name in ['Management', 'Reports', 'Reports Mgmt']:
        return "Proj Mgmt"
    elif task_name in ['Release Management', 'QA Release Notes Preparation and Review', 'Release Planning']:
        return "Release Mgmt"
    elif isinstance(gs_ticket_summary, str):
        if re.search(r'\bStress\s*testing\b', gs_ticket_summary, re.IGNORECASE):
            return "Stress Testing"
        elif re.search(r"\bregression\s*testing", gs_ticket_summary, re.IGNORECASE):
            return "Regression Testing"
        elif re.search(r"\bRequirement\s*Management\b", gs_ticket_summary):
            return "Req Mgmt"
        elif re.search(r"\bProject\s*Management\b|\bPM/TPM\b", gs_ticket_summary, re.IGNORECASE):
            return "Proj Mgmt"
        elif re.search(r"\bPentation\s*Testing\b|\bSec\s*Testing\b", gs_ticket_summary, re.IGNORECASE):
            return "Security Testing"
        elif re.search(r"\bbackporting\b", gs_ticket_summary, re.IGNORECASE):
            return "Back Porting"
        elif re.search(r"CODA", gs_ticket_summary, re.IGNORECASE):
            return "CODA"
        elif re.search(r"LOTUS", gs_ticket_summary, re.IGNORECASE):
            return "LOTUS"
        elif re.search(r"Ledger Observability", gs_ticket_summary, re.IGNORECASE):
            return "Observability"

    return "Development"


def apply_table(x):
    # rows = []
    # for y in [i.split('|') for i in x]:
    #     rows.append(html.Tr([html.Td(i) for i in y]))
    #
    # row_comp = [html.Tr([html.Td(z) for z in y]) for y in [i.split('|') for i in x]]

    return html.Table(
        children=[html.Thead([html.Tr([html.Th(col) for col in ['Date', 'Author', 'Display Name', 'Team']])]),
                  html.Tbody([html.Tr([html.Td(z) for z in y]) for y in [i.split('|') for i in x]]), ],
        className="styled-table"
    )


def apply_markdown(x):
    return dcc.Markdown(children=[x])


# Function to split, strip leading/trailing spaces, and remove duplicates
def split_strip_unique(keys: str):
    ids = keys.split(",")
    ids = [id_.strip().upper() for id_ in ids]  # Remove leading/trailing spaces
    unique_ids = list(set(ids))  # Remove duplicates
    return unique_ids


async def get_user_project_details(user_id: str, token: str):
    jira_client = api.JiraRestAioHttpRepository(user_id, token)

    tasks = [
        jira_client.get_myself(),
        jira_client.get_project_permissions()
    ]

    responses = await asyncio.gather(*tasks)
    return responses


async def get_user_avatars(
        user_id: str, token: str, url: str
):
    jira_client = api.JiraRestAioHttpRepository(user_id, token)

    tasks = [
        jira_client.download_image(url),
    ]

    responses = await asyncio.gather(*tasks)
    return responses


def image_to_base64(image_data: bytes):
    image = Image.open(io.BytesIO(image_data))
    buffered = io.BytesIO()
    print(image.format)
    image.save(buffered, format=image.format)
    return base64.b64encode(buffered.getvalue()).decode('utf-8')


# Source: https://www.educative.io/edpresso/what-is-the-html-parser-in-python
class Parser(HTMLParser, ABC):

    def __init__(self, start_tags, end_tags, all_data, comments):
        super().__init__()
        self.start_tags = start_tags
        self.end_tags = end_tags
        self.all_data = all_data
        self.comments = comments

    # method to append the start tag to the list start_tags.
    def handle_starttag(self, tag, attrs):
        self.start_tags.append(tag)

    # method to append the end tag to the list end_tags.
    def handle_endtag(self, tag):
        self.end_tags.append(tag)

    # method to append the data between the tags to the list all_data.
    def handle_data(self, data):
        self.all_data.append(data)

    # method to append the comment to the list comments.
    def handle_comment(self, data):
        self.comments.append(data)


# Source: https://community.plotly.com/t/how-to-clear-the-value-in-dropdown-via-callback/28777/2


def format_sheet(df_sheet: pd.DataFrame, worksheet, wrap_format):
    wrap_col_width = [
        'Summary', 'description', 'Components',
        'SQL Object Names', 'App Indexes', 'Alter Tables', 'Reporting Indexes'
    ]
    for i, col in enumerate(df_sheet.columns):
        column_len = df_sheet[col].astype(str).str.len().max()
        column_len = max(column_len, len(col)) + 2

        if col in wrap_col_width:
            worksheet.set_column(i, i, 50, wrap_format)
        else:
            worksheet.set_column(i, i, column_len)


def apply_billing_period(end_date) -> str:
    first_day_of_month = end_date.replace(day=1)
    first_friday = first_day_of_month + pd.DateOffset(days=(4 - first_day_of_month.weekday() + 7) % 7)
    # first_friday = first_day_of_month + pd.DateOffset(days=(4 - first_day_of_month.weekday() + 7) % 7))

    # Find the previous Saturday of the first Friday
    previous_saturday_of_first_friday = first_friday - pd.DateOffset(days=6)
    # Find the last Friday of the month
    last_day_of_month = (first_day_of_month + MonthEnd(0))
    last_friday = last_day_of_month - pd.DateOffset(days=(last_day_of_month.weekday() - 4 + 7) % 7)

    # Format the dates
    start_date_str = previous_saturday_of_first_friday.strftime('%y_%b_%d')
    end_date_str = last_friday.strftime('%y_%b_%d')

    # Create the name
    date_range_name = f"{start_date_str}_TO_{end_date_str}"

    return date_range_name


def extract_start_date(column_name):
    match = re.match(r'(\d+)_([A-Za-z]+)_(\d+)', column_name)
    if match:
        day, month, year = match.groups()
        return datetime.strptime(f"{day} {month} {year}", "%d %b %y")
    return None


def debug_dataframe(
        df_debug: pd.DataFrame,
        filename: str
):
    if filename != "":
        file_extension = os.path.splitext(filename)[1]
    else:
        file_extension = ""
    path = "e:/vishal/log" if os.name == 'nt' else '/tmp'

    if file_extension == '.csv':
        df_debug.to_csv(os.path.join(path, filename), index=False, quotechar='"', quoting=csv.QUOTE_MINIMAL)
    elif file_extension == '.xlsx':
        xslx_writer = pd.ExcelWriter(os.path.join(path, filename), engine="xlsxwriter")
        df_debug.to_excel(xslx_writer, index=False, sheet_name="sheet1")
        worksheet = xslx_writer.sheets['sheet1']
        workbook = xslx_writer.book
        wrap_format = workbook.add_format({'text_wrap': True})
        for i, col in enumerate(df_debug.columns):
            if isinstance(df_debug[col], pd.Series):
                column_len = df_debug[col].astype(str).str.len().max()
                column_len = max(column_len, len(col)) + 2
                if col == 'summary':
                    worksheet.set_column(i, i, 50)
                elif col in ['clientjira', 'components']:
                    worksheet.set_column(i, i, 25, wrap_format)
                else:
                    worksheet.set_column(i, i, column_len)

        row_, col_ = df_debug.shape
        worksheet.autofilter(0, 0, row_, col_ - 1)
        xslx_writer.close()


# Function to fetch data for a single project
def fetch_project_data(prj, engine, begin_week_id, end_week_id, project_client):
    my_logger = MyLogger().get_logger()
    my_logger.info(f"Processing {prj} ...")
    with engine.connect() as connection:
        result_set = db.get_ts_data_from_ms(connection, prj, begin_week_id, end_week_id, project_client)
    if result_set:
        df = pd.DataFrame(result_set)
        if project_client[str(prj)] == 8033:
            my_logger.info(f"Processed {prj} !!!")
            return df, None
        else:
            df.insert(24, 'JiraIssue', np.nan)
            df['BugDefectNo'] = ''
            df['JiraFeaturekey'] = ''
            df['ClientJiraNo'] = ''
            my_logger.info(f"Processed {prj} !!!")
            return None, df
    my_logger.info(f"Processed {prj} !!!")
    return None, None


def fetch_data_parallel(
        project_id_list: list,
        engine, begin_week_id, end_week_id, project_client
):
    my_logger = MyLogger().get_logger()
    dataframe_list = []
    dataframe_non_plat = []
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=len(project_id_list)) as executor:
        # future_to_project = {
        #     executor.submit(
        #         fetch_project_data, prj, engine, begin_week_id, end_week_id, project_client
        #     ): prj for prj in project_id_list
        # }

        futures = [
            executor.submit(fetch_project_data, prj, engine, begin_week_id, end_week_id, project_client)
            for prj in project_id_list
        ]
        # for future in future_to_project:
        for future in as_completed(futures):
            # prj = future_to_project[future]
            try:
                df_plat, df_non_plat = future.result()
                if df_plat is not None:
                    dataframe_list.append(df_plat)
                if df_non_plat is not None:
                    dataframe_non_plat.append(df_non_plat)
            except Exception as exc:
                my_logger.error(f"Project generated an exception: {exc}")

        # for prj in project_id_list:
        #     my_logger.info(f"Processing {prj} ...")
        #     result_set = db.get_ts_data_from_ms(conn, prj, begin_week_id, end_week_id, project_client)
        #     if project_client[str(prj)] == 8033:
        #         if result_set:
        #             df = pd.DataFrame(result_set)
        #             dataframe_list.append(df)
        #     else:
        #         if result_set:
        #             df = pd.DataFrame(result_set)
        #             df.insert(24, 'JiraIssue', np.nan)
        #             df['BugDefectNo'] = ''
        #             df['JiraFeaturekey'] = ''
        #             df['ClientJiraNo'] = ''
        #             dataframe_non_plat.append(df)
        my_logger.info(f"Time taken: {time.time() - start_time}")
        return dataframe_list, dataframe_non_plat


# Implementation of rfc5649. This doesn't account for the case of ciphertext
# with 2 blocks (since we are using it to export RSA private keys). While
def unwrap_key(kek, ciphertext):
    # use 6 steps of AES ECB
    decrypt = AES.new(kek, AES.MODE_ECB).decrypt
    steps = 6
    # split the ciphertext in 8-byte blocks.
    blocks = []
    block_size = 8
    block_count = len(ciphertext) // block_size - 1
    for i in range(block_count):
        block = (i + 1) * block_size
        blocks.append(ciphertext[block:block + block_size])
    # struct.pack format for serializing/deserializing 64-bit unsigned integers
    ulong_be = '>Q'
    # Integrity/size block. After decryption should contain the alternative
    # initial value (AIV) which is a constant (0xA65959A600000000) plus the
    # size of the original plaintext
    aiv = struct.unpack(ulong_be, ciphertext[:8])[0]
    for j in range(0, steps):
        for i in range(block_count, 0, -1):
            cipherblock = struct.pack(ulong_be,
                                      aiv ^ ((5 - j) * block_count + i)) + blocks[i - 1]
            block = decrypt(cipherblock)
            aiv = struct.unpack(ulong_be, block[:8])[0]
            blocks[i - 1] = block[8:]
    assert aiv & 0xffffffff00000000 == 0xa65959a600000000
    # Integrity check passed, the original length is specified in the 32 LSB of
    # the aiv
    length = aiv & 0xffffffff
    plaintext_with_pad = b''.join(blocks)
    return plaintext_with_pad[:length]


# def export_private_key(session, key):
#     KEK = os.urandom(16)
#     kek_handle = session.create_object({
#         Attribute.CLASS: ObjectClass.SECRET_KEY,
#         Attribute.KEY_TYPE: KeyType.AES,
#         Attribute.VALUE: KEK,
#         Attribute.WRAP: True,
#         Attribute.UNWRAP: True,
#         Attribute.TOKEN: False,
#     })
#     wrapped_key = kek_handle.wrap_key(key,
#                                       mechanism=Mechanism.AES_KEY_WRAP_PAD)
#     clear_key = unwrap_key(KEK, wrapped_key)
#     return clear_key


# Convert the encrypted token from hexadecimal to bytes
def hex_to_bytes(hex_str):
    return bytes.fromhex(hex_str)


# functions | End
@callback(
    Output(dict(type='side-panel', index=MATCH), "className"),
    Output(dict(type='main-panel', index=MATCH), "className"),
    Output(dict(type='toggle-panel', index=MATCH), "className"),
    Input(dict(type='toggle-panel', index=MATCH), "n_clicks"),
)
def toggle_side_panel(n_click):
    user_clicked = clicked(callback_context)
    if user_clicked is None:
        raise PreventUpdate

    # Check if this is the modern timesheet layout
    if user_clicked and 'isctimesheet' in str(user_clicked):
        # Modern timesheet toggle
        if n_click % 2 == 0:
            return "modern-sidebar", "modern-main-content", "modern-toggle-button"
        else:
            return "modern-sidebar hidden", "modern-main-content expanded", "modern-toggle-button active"
    else:
        # Legacy layout for other pages
        if n_click % 2 == 0:
            return "side-menu", "display-area", "format-button"
        else:
            return "side-menu hidden", "display-area expand-grid", "format-button active"


@callback(
    Output('url_login', 'pathname'),
    Output(component_id="notifications-container", component_property="children"),
    Input('id-button-login-main', 'n_clicks'),

    State('id-login-main', 'value'),
    State('id-login-passwd-main', 'value'),

    running=[
        (Output("id-button-login-main", "disabled"), True, False),
    ],
)
@inject
async def login_button_click(
        n_clicks: int,
        username, token,
        # session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
):
    """MyKeePassContainer
    This function is used for enabling Single factor or 2FA authentication.
    First validate if user is able to access his/her JIRA account with provided credential.
    Then, redirect user to validate using Google Authenticator.
    if both steps pass, allow user to login.

    Args:
        session_factory:
        n_clicks: Number of times button is clicked
        username: O365 email id used for login to JIRA
        token: security token generated by user in JIRA

    Returns: redirect url, message, projects
    :param session_factory:
    :param token:
    :param username:
    :param n_clicks:
    :param pwd_mgr:

    """
    if n_clicks > 0:
        my_logger = MyLogger().get_logger()
        my_logger.debug(f'root url: {flask.request.root_url}')

        if username == "" or token == "":
            raise PreventUpdate

        # SoftHSM2 code to encrypt/decrypt data.
        # Check in future if this needs to be used.
        # user_pin = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'PIN')
        # token_label = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'Token Label')
        # keypair_label = pwd_mgr.password_manager().get_custom_string('softHSM Creds', 'KEY_LABEL')
        # lib = pkcs11.lib(os.environ['PKCS11_MODULE'])
        # pkcs_token = lib.get_token(token_label=token_label)
        # my_logger.info(f"keypair_label = {keypair_label}")
        # with pkcs_token.open(user_pin=user_pin) as hsm_session:
        #     key_private = list(hsm_session.get_objects({
        #         Attribute.LABEL: keypair_label,
        #         Attribute.PRIVATE: True
        #     }))
        #     key_public = list(hsm_session.get_objects({
        #         Attribute.LABEL: keypair_label,
        #         Attribute.PRIVATE: False
        #     }))
        #     my_logger.debug(f"encrypted text")
        #     encr = key_public[0].encrypt(token)
        #     my_logger.debug(encr)
        #     my_logger.debug(f"decrypted = {key_private[0].decrypt(encr)}")

        responses = await get_user_project_details(username, token)
        my_logger.debug(f"{responses}")
        # TODO Remove hardcoded list by reading from environment variable
        db_schema_list = ["plat", "cpp", "ccp"]
        # resp = api.get_myself(username, token)
        # my_logger.debug(f'response code: {resp.status_code}')
        user_status_code = responses[0]['status']
        user_details = responses[0]['response']
        projects = [prj['key'].lower() for prj in responses[1]['response']['projects']]
        my_logger.debug(f"list of projectS: {projects}")
        try:
            parsed_url = urlparse(user_details['avatarUrls']["16x16"])
            # Extract query parameters
            query_params = parse_qs(parsed_url.query)
            d_value = query_params.get('d', [None])[0]

            responses = await get_user_avatars(username, token, d_value)

            session['encoded_image_status'] = responses[0]['status_code']
            my_logger.debug(f"encoded_image_status: {session['encoded_image_status']}")
            if session['encoded_image_status'] == 200:
                session['encoded_image'] = image_to_base64(responses[0]['data'])
            else:
                session['encoded_image'] = None
        except client_exceptions.ClientConnectorError as e:
            my_logger.error("Error connecting to gravatar")
            session['encoded_image'] = None

        if user_status_code == 200:
            # output = resp.json()
            my_logger.debug(f'{username} verified successfully')
            session['email_id'] = username
            session['api_token'] = token

            session['avatarUrls16x16'] = user_details['avatarUrls']["16x16"]
            session['displayName'] = user_details['displayName']
            # dash.callback_context.response.set_cookie('email_address', username, max_age=600)
            # set_cookie can be passed an option expires=expiration_time
            # expiration_time = datetime.now() + timedelta(hours=1)            #
            # max_age is set in seconds
            # value_email = username
            # projects = api.get_project_list(username, token)

            session['projects'] = sorted(
                list(set(projects) & set(db_schema_list)), key=lambda x: canonical_form(x),
                reverse=False
            )
            # session['projects'] = sorted(projects, key=lambda x: canonical_form(x), reverse=False)

            user = UserAuth(username, session['displayName'])

            multi_factor = os.getenv("TWO_FA_ENABLED", "No")
            my_logger.info(f'Is 2FA enabled: {multi_factor.lower()}')

            if multi_factor.lower() == "yes":
                # cache_key = helper.hash_data(f'{username}')
                email_address, token = db.get_user_totp(username)

                if token == 'NOT_FOUND':
                    session['2fa_enabled'] = False
                    login_user(user, remember=False)
                    identity_changed.send(
                        current_app._get_current_object(),
                        identity=Identity(user.id, auth_type='jira_token')
                    )
                    message = f"Login Successful without 2FA. API Response code: {user_status_code}."

                    notification = dmc.Notification(
                        title="Login Status",
                        id="simple-notify",
                        action="show",
                        message=message,
                        icon=DashIconify(icon="ic:round-celebration"),
                    )
                    return f"{os.getenv('DASH_BASE_PATH', '/')}success", notification
                else:
                    message = f"Initiating 2FA authentication."
                    notification = dmc.Notification(
                        title="Login Status",
                        id="simple-notify",
                        action="show",
                        message=message,
                        icon=DashIconify(icon="ic:round-celebration"),
                    )
                    if token is None:
                        my_logger.info("redirecting to new registration flow")
                        return '/new_reg', notification
                    else:
                        # Go to page to accept Google Authenticator token
                        # login_user(user)
                        session['2fa_enabled'] = True
                        session['new_registration'] = 'existing'
                        return f"{os.getenv('DASH_BASE_PATH', '/')}login/2fa", notification
            else:
                session['2fa_enabled'] = False
                login_user(user)
                identity_changed.send(
                    current_app._get_current_object(),
                    identity=Identity(user.id, auth_type='jira_token')
                )

                message = f"Login Successful. API Response code: {user_status_code}."
                notification = dmc.Notification(
                    title="Login Status",
                    id="simple-notify",
                    action="show",
                    message=message,
                    icon=DashIconify(icon="ic:round-celebration"),
                )
                if 'requested_path' in session:
                    return f"{session['requested_path']}", notification
                else:
                    return f"{os.getenv('DASH_BASE_PATH', '/')}success", notification
        else:
            my_logger.warning(f"{username} failed verification error: {user_status_code}")
            my_logger.debug(f"{username} error {get_error_message(user_status_code)}")

            error_message = f"Your userid or token is invalid. API Response code: {user_status_code}."
            error_message += "Check your credentials and try again"
            notification = dmc.Notification(
                title="Application Login Failed",
                id="simple-notify",
                action="show",
                message=error_message,
                icon=DashIconify(icon="ic:round-celebration"),
            )

            return f"{os.getenv('DASH_BASE_PATH', '/')}login", notification
    else:
        raise PreventUpdate


# WD Timesheet | Start
@callback(
    Output("id-get-ts-details", "disabled"),
    Input("id-isc-date-picker-range", "start_date"),
    Input("id-isc-date-picker-range", "end_date"),
    Input("id-ts-project-dropdown", "value"),
    prevent_initial_call=True,
)
def check_details_ts(
        start_date: str, end_date: str, project_list: list
):
    my_logger = MyLogger().get_logger()
    if start_date is not None and end_date is not None and len(project_list) > 0:
        return False
    return True


@callback(
    Output("id-isc-date-picker-range", "end_date"),
    Input("id-check-same-day", "value"),
    Input("id-isc-date-picker-range", "start_date"),
    prevent_initial_call=True,
)
def update_end_date(
        check, start_date: str
):
    if check == [1] and start_date is not None:
        return start_date
    else:
        raise PreventUpdate


@callback(
    Output("my-isc-timesheet-grid", "rowData"),
    Output("id-cc-timesheet-graph", "figure"),
    Output("id-cc-issuetype-graph", "figure"),
    Output("id-gs-jira-msg", "children", allow_duplicate=True),
    Output("id-wd-df-list-store", "data"),
    Input("id-get-ts-details", "n_clicks"),
    State("id-isc-date-picker-range", 'start_date'),
    State("id-isc-date-picker-range", 'end_date'),
    State("id-ts-project-dropdown", "value"),
    State("id-ts-project-dropdown", "options"),
    State("id-ts-issuetype", "value"),
    State("id-project-client", "data"),
    prevent_initial_call=True,
    running=[
        (Output("id-get-ts-details", "disabled"), True, False),
        (Output("csv-button", "disabled"), True, False),
    ]
)
@inject
def isc_timesheet(
        n_clicks: int,
        start_date: str, end_date: str, project_id_list: list, project_options,
        issue_type: str, project_client: dict,
        # session_factory: custom_container.AppContainer = Provide[custom_container.AppContainer.session_factory],
        session_factory: DatabaseContainer = Provide[DatabaseContainer],
):
    my_logger = MyLogger().get_logger()
    message = ""

    def map_teams_from_dict(email_id: str, week_end_date: date):
        team_info_list = teams_dict.get(email_id)
        if team_info_list is not None:
            if len(team_info_list) == 1:
                return team_info_list[0].team_name
            else:
                for i, team_info_row in enumerate(team_info_list):
                    if team_info_row.startDate <= week_end_date <= team_info_row.endDate:
                        return team_info_row.team_name
                    elif i < len(team_info_list) - 1 and week_end_date == team_info_row.endDate + timedelta(days=1):
                        return team_info_list[i + 1].team_name
        else:
            return ''

    project_name = [item['label'] for item in project_options if item['value'] in project_id_list]
    # project_dict = {item['value']: item['label'] for item in project_options if item['value'] in project_id_list}

    if n_clicks > 0:
        session_factory.config.override({'ro_or_rw': 'ro'})
        session_factory.config_schema.override({'schema_name': 'mssql'})
        my_logger.info(f'Start date = {start_date}')
        my_logger.info(f'End date = {end_date}, type = {type(end_date)}')
        start_date_datetime: datetime = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_datetime: datetime = datetime.strptime(end_date, '%Y-%m-%d')

        my_logger.info(f'{end_date_datetime}, type = {type(end_date_datetime)}')

        # Calculate the difference in days to the next Friday (where Monday is 0 and Sunday is 6)
        # days_until_friday = (4 - end_date_datetime.weekday() + 7) % 7
        # Calculate the date of the next Friday
        # next_friday = end_date_datetime + timedelta(days=days_until_friday)
        # Format the next Friday as a string
        # next_friday_str: str = next_friday.strftime('%Y-%m-%d')
        # my_logger.info(f'Next Friday date: {next_friday_str}')
        # business_days = np.busday_count(start_date, next_friday_str) + 1
        # my_logger.info(f"bussiness days = {business_days}")

        # Calculate the difference in days to the previous Saturday (where Monday is 0 and Sunday is 6)
        days_until_sat = 6
        previous_sat = start_date_datetime - timedelta(days=days_until_sat)
        previous_sat = previous_sat.strftime('%Y-%m-%d')
        business_days = np.busday_count(previous_sat, end_date)

        dataframe_list = []
        dataframe_non_plat = []
        connection_ms_db = None

        with session_factory.db_conn_provider().create_engine() as engine:
            connection_ms_db = check_ms_db_connection(engine)

            if connection_ms_db:
                start_time = time.perf_counter_ns()
                # with session_factory.db_ms_engine().create_engine() as connection:
                # with session_factory.db_conn_provider().create_engine() as connection:
                with engine.connect() as conn:
                    sql_query = text(
                        """
                        SELECT MIN(WeekId), MAX(WeekId)
                        FROM week WITH (NOLOCK)
                        WHERE WeekEndDate BETWEEN CONVERT(DATE, :start_date, 120) AND CONVERT(DATE, :end_date, 120)
                        """
                    )
                    # Create a dictionary with parameter values
                    params = {"start_date": start_date, "end_date": end_date}

                    begin_week_id, end_week_id = conn.execute(sql_query.bindparams(**params)).fetchone()
                    print(f"week ids = {begin_week_id}, {end_week_id}")

                    leaves_result = db.get_ts_leave_details_from_ms(conn, begin_week_id, end_week_id)
                    df_leaves: pd.DataFrame = pd.DataFrame(leaves_result)
                    df_leaves['WeekEndDate'] = df_leaves['WeekEndDate'].dt.date

                dataframe_list, dataframe_non_plat = fetch_data_parallel(
                    project_id_list, engine, begin_week_id, end_week_id, project_client
                )

                if dataframe_list:
                    df = pd.concat(dataframe_list)
                    df.reset_index(drop=True, inplace=True)

                team_list = df['EeLogin'].unique()
                if dataframe_non_plat:
                    df_non_plat = pd.concat(dataframe_non_plat)
                    df_non_plat = df_non_plat[df_non_plat['EeLogin'].isin(team_list)]
                    df_non_plat.reset_index(drop=True, inplace=True)
                    df = pd.concat([df, df_non_plat])

                # Define the regex pattern to extract Jira Issue IDs
                df['jira_issue_ids'] = df['comments'].apply(extract_jira_issue_ids)
                # Convert the list of Jira Issue IDs to a value
                df['jira_issue_ids'] = df['jira_issue_ids'].apply(lambda x: ','.join(x))

                df['wd_time_source'] = df['comments'].apply(get_time_source)
                df['ClientKey'] = df['comments'].apply(get_issue_keys)
            else:
                # Use Mock data
                mock_data = [
                    {"Year": 2023, "PSAID": 0, "EeLogin": "Krunal.Kumbhare", "EeStatus": "Active", "EeManagerId": 800,
                     "TimeEntryId": 25368935, "TimecardId": 329090, "EeId": 15335, "Grade": None,
                     "ProjectCode": 1106223,
                     "TaskCode": 404, "Totalhrs": 2.0, "WeekEndDate": "2023-04-14", "weekDateFrom": "2023-04-07",
                     "weekDateTo": "2023-04-28", "Status": "Finalized", "ApprovedBy": 800.0,
                     "TaskName": "Development Designing", "TaskCategory": 4,
                     "InputFeature": "Status Change Request Received during EOM Window Handling",
                     "feature": "Status Change Request Received during EOM Window Handling",
                     "comments": "Status Change Request Received during EOM Window Handling - COOKIE-232232- Jira Issue :-PLAT-106390",
                     "JiraIssue": "PLAT-106390", "ProjectName": "Plat_NEST", "ClientName": "Plat",
                     "officename": "CoreCard_India", "monthid": 4, "BugDefectNo": None,
                     "JiraFeaturekey": "PLAT-101221",
                     "ClientJiraNo": "COOKIE-232232", "jira_issue_ids": "PLAT-106390"},
                    {"Year": 2023, "PSAID": 0, "EeLogin": "Priyanka.Gurjar", "EeStatus": "Active", "EeManagerId": 574,
                     "TimeEntryId": 25580080, "TimecardId": 330497, "EeId": 15890, "Grade": None,
                     "ProjectCode": 1106223,
                     "TaskCode": 3203, "Totalhrs": 3.0, "WeekEndDate": "2023-04-21", "weekDateFrom": "2023-04-07",
                     "weekDateTo": "2023-04-28", "Status": "Finalized", "ApprovedBy": 574.0, "TaskName": "Sub-task",
                     "TaskCategory": 32,
                     "InputFeature": "Enhancement in pruning package to get weekly potential count.",
                     "feature": "Enhancement in pruning package to get weekly potential count.",
                     "comments": "- Jira Issue :-PLAT-108521", "JiraIssue": "PLAT-108521", "ProjectName": "Plat_NEST",
                     "ClientName": "Plat", "officename": "CoreCard_India", "monthid": 4, "BugDefectNo": None,
                     "JiraFeaturekey": "PLAT-102719", "ClientJiraNo": "COOKIE-239782",
                     "jira_issue_ids": "PLAT-108521"}]
                df = pd.DataFrame(mock_data)
                df['WeekEndDate'] = pd.to_datetime(df['WeekEndDate']).dt.date

                df_leaves = pd.DataFrame(
                    [
                        ['2024-4-5', 'Aafreen.Khan', '<EMAIL>', 8, 'Leave + Holiday']
                    ], columns=[
                        'WeekEndDate', 'EeLogin', 'email_id', 'Totalhrs', 'ProjectName'

                    ]
                )

        df['BillingPeriod'] = df['WeekEndDate'].apply(apply_billing_period)
        df['ClientJiraNo'] = df['ClientJiraNo'].str.upper()


        session_factory.config_schema.override({'schema_name': 'plat'})
        start_time = time.time()
        with session_factory.db_conn_provider().session() as pg_session:
            my_logger.debug(f"pg_session url = {pg_session.bind.url}")
            client_jira = df['ClientJiraNo'].unique().tolist()
            df_client = db.get_gs_jira(client_jira, pg_session)
            # df_teams, teams_dict = db.get_team_details(pg_session)
            teams_list: list = db.get_teams_by_email(pg_session)
            df_team_list = pd.DataFrame(teams_list)
            issue_key_list = df['jira_issue_ids'].unique().tolist()

            if all(element == '' for element in issue_key_list):
                my_logger.debug(f'issue_key_list contains empty value')
                df_key = pd.DataFrame(columns=[
                    'cc_issue_key', 'cc_issue_summary', 'cc_issuetype', 'cc_issue_status',
                    'fixVersions', 'versions', 'priority', 'severity',
                    'cc_epic_key', 'cc_epic_summary', 'standard_key',
                    'standard_issuetype', 'aggregatetimespent', 'custom_feature'
                ]
                )
            else:
                df_key = db.get_issue_details(issue_key_list, pg_session)

        print(f"Time taken in postgres: {time.time() - start_time}")

        # df_client.to_csv("e:/vishal/log/df_client.csv") if os.name == 'nt' else df_client.to_csv('/tmp/df_client.csv')
        # df_key.to_csv("e:/vishal/log/df_key.csv") if os.name == 'nt' else df_key.to_csv('/tmp/df_key.csv')
        if df_client.shape[0] > 0:
            df_client['client_jira_no'] = df_client['client_jira_no'].str.upper()
            df = df.merge(df_client, left_on="ClientJiraNo", right_on="client_jira_no", how='left')
        else:
            df['gs_summary'] = ''
            df['gs_fixversion'] = ''
            df['effort_estimates'] = 0

        # debug_dataframe(df, "df_client_merged.xlsx")

        df = df.merge(df_key, left_on="jira_issue_ids", right_on="cc_issue_key", how="left")
        # Initialize a list to store columns with list values
        columns_with_lists = []
        # debug_dataframe(df, filename="df_key_merged.xlsx")

        for column in df.columns:
            if any(isinstance(item, list) for item in df[column]):
                columns_with_lists.append(column)

        for col in columns_with_lists:
            df[col] = df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)

        df_ts = df[['WeekEndDate', 'Totalhrs']].groupby('WeekEndDate').sum().reset_index()
        # df_ts['text'] = df['WeekEndDate'].dt.strftime("%m/%d/%y") + '<br>' + 'Totalhrs: ' + df['Totalhrs'].astype(str)
        df_ts['WeekEndDate'] = pd.to_datetime(df_ts['WeekEndDate'])
        df_ts['WeekEndDate_ts'] = df_ts['WeekEndDate'].apply(lambda x: datetime.timestamp(x))

        # fig = px.scatter(
        #     df_ts, x='WeekEndDate_ts', y='Totalhrs', text='Totalhrs',
        #     title=", ".join(project_name),
        #     trendline='ols', trendline_scope="overall"
        # )

        # This is working
        # fig = px.scatter(
        #     df_ts, x='WeekEndDate', y='Totalhrs', text='Totalhrs',
        #     title=", ".join(project_name),
        #     trendline='lowess', trendline_options=dict(frac=0.1)
        # )

        fig = px.scatter(
            df_ts, x='WeekEndDate', y='Totalhrs', text='Totalhrs',
            title=", ".join(project_name),
            trendline='ewm', trendline_options=dict(halflife=2)
        )

        fig.update_traces(textposition="bottom right")
        fig.update_xaxes(title='Week Ending')
        fig.update_yaxes(title='Hours')
        # get trendline results
        results = px.get_trendline_results(fig)

        # df_issue = df.query('TaskName == @issue_type')
        df_issue = df[df['TaskName'] == issue_type]

        fig_issue_type = px.histogram(
            df_issue, x='WeekEndDate', y='Totalhrs', text_auto=True,
            title=", ".join(project_name), barmode='group'
        )
        fig_issue_type.update_layout(bargap=0.2)

        # Start Added new code
        fig_issue_type.update_xaxes(
            rangeslider_visible=False,
            rangeselector=dict(
                buttons=list([
                    dict(count=1, label="1m", step="month", stepmode="backward"),
                    dict(count=6, label="6m", step="month", stepmode="backward"),
                    dict(count=1, label="YTD", step="year", stepmode="todate"),
                    dict(count=1, label="1y", step="year", stepmode="backward"),
                    dict(step="all")
                ])
            ),
            title="Week Ending"
        )

        fig_issue_type.update_yaxes(title="Hours")
        fig_issue_type.update_xaxes(showgrid=True, tickformat="%b\n%Y", ticklabelmode="period")

        # End new code addition
        df['WeekEndDate'] = pd.to_datetime(df['WeekEndDate'])
        df['WeekEndDate'] = df['WeekEndDate'].dt.date
        df['classification'] = df.apply(lambda x: apply_classification(
            x['ProjectName'], x['gs_summary'], x['TaskName'], x['custom_feature'], x['InputFeature']
        ), axis=1)
        df['email_id'] = df['EeLogin'] + '@corecard.com'
        df['email_id'] = df['email_id'].str.lower()
        # df_teams['emailAddress'] = df_teams['emailAddress'].str.lower()

        # @cache.memoize(timeout=helper.TIME_OUT)
        # def map_team(email_id: str, week_end_date: date):
        #     _start_time = time.perf_counter_ns()
        #     row = df_teams[
        #         (df_teams['emailAddress'] == email_id) &
        #         (df_teams['startDate'] < week_end_date) &
        #         (df_teams['endDate'] >= week_end_date)
        #         ]
        #     my_logger.debug(f'Time taken in row: {(time.perf_counter_ns() - _start_time)} ns')
        #     if row.shape[0] > 0:
        #         return ", ".join(row['team_name'].values.tolist())
        #     else:
        #         return ''

        # Convert teams which is list of tuple into dict.
        teams_dict = {}
        TeamInfo = namedtuple('TeamInfo', ['team_name', 'startDate', 'endDate'])
        for email, team_name, start_date, end_date in teams_list:
            if email in teams_dict:
                teams_dict[email].append(TeamInfo(team_name, start_date, end_date))
            else:
                teams_dict[email] = [TeamInfo(team_name, start_date, end_date)]

        # time_taken = time.perf_counter_ns()
        # df['team_name'] = df.apply(lambda x: map_team(x['email_id'], x['WeekEndDate']), axis=1)
        # my_logger.debug(f'Time taken in adding team_name = {(time.perf_counter_ns() - time_taken) * pow(10, -6)} ms')

        time_taken = time.perf_counter_ns()
        df['team_name'] = df.apply(lambda x: map_teams_from_dict(x['email_id'], x['WeekEndDate']), axis=1)

        df_leaves['email_id'] = df_leaves['email_id'].str.lower()
        # Filter out resources who are not in plat
        plat_team: list = df['email_id'].unique()
        df_leaves = df_leaves[df_leaves['email_id'].isin(plat_team)].copy()

        df_leaves['team_name'] = df_leaves.apply(
            lambda x: map_teams_from_dict(x['email_id'], x['WeekEndDate']), axis=1
        )

        df_leaves.query("team_name != ''", inplace=True)
        df_leaves.drop(columns=['WeekEndDate', 'email_id'], inplace=True)

        df_inter = df[df['team_name'] != ''][['team_name', 'EeLogin', 'ProjectName', 'Totalhrs']]
        df_inter = pd.concat([df_inter, df_leaves])
        df_inter['ProjectName'] = df_inter['ProjectName'].str.strip()
        # The (?i) in the regular expression pattern makes the pattern case-insensitive,
        # allowing it to match "Plat" regardless of its capitalization.
        # \b specifies word boundaries
        replace_values: dict = {
            'Plat-NBL': 'NBL', 'Plat Production Support': 'Prod Support', 'Plat Managed Services': 'Managed Services',
            'Plat-Jazz': "Prof Services", 'Plat_NEST': "Prof Services",
            'CP_Plat_CI': "Prof Services", 'Plat Stress Test': "Prof Services", 'Plat': "Prof Services",
        }
        repl = {rf'\b{k}\b': v for k, v in replace_values.items()}
        df_inter['ProjectName'] = df_inter['ProjectName'].replace(repl, regex=True)
        df_team_data = df_inter[['team_name', 'EeLogin']].drop_duplicates(keep='first')
        df_pivot = df_inter.pivot_table(index='team_name', columns='ProjectName', values='Totalhrs', aggfunc='sum')
        df_pivot.reset_index(inplace=True)
        df_pivot.fillna(0, inplace=True)
        df_headcount = df_inter[df_inter['team_name'] != ''].pivot_table(
            index='team_name', values='EeLogin',
            aggfunc='nunique'
        )
        df_headcount.reset_index(inplace=True)
        df_headcount['Total_Hours'] = business_days * 8 * df_headcount['EeLogin']
        df_merge = pd.merge(df_headcount, df_pivot, on='team_name', how='left')
        df_merge['Hours_Consumed'] = df_merge.iloc[:, 3:].sum(axis=1)

        selected_column_names = df_merge.columns[3:-1]
        my_logger.info(f'selected cols = {selected_column_names}')
        df_merge['Hours_Available'] = df_merge['Total_Hours'] - df_merge['Hours_Consumed']
        for col in selected_column_names:
            df_merge[f'{col} %'] = round(df_merge[col] * 100 / df_merge['Total_Hours'], 2)
        df_merge.rename(columns={'EeLogin': 'Head Count', 'team_name': 'Team Name'}, inplace=True)
        df_team_data.rename(columns={'EeLogin': 'Resource Name', 'team_name': 'Team Name'}, inplace=True)
        df_team_data.sort_values(by=['Team Name', 'Resource Name'], ascending=[True, True], inplace=True)
        pivot_df = df.pivot_table(
            values="Totalhrs",
            index=["WeekEndDate", "team_name"],
            columns="classification",
            aggfunc="sum", fill_value=0,
            # margins=True, margins_name='Grand Total',
        ).copy()
        pivot_df_week = df.pivot_table(
            values="Totalhrs",
            index=["WeekEndDate"],
            columns="classification",
            aggfunc="sum", fill_value=0,
            # margins=True, margins_name='Grand Total',
        ).copy()
        pivot_df.reset_index(inplace=True)
        pivot_df_week.reset_index(inplace=True)

        df['cc_epic_components'] = df['cc_epic_components'].fillna(" ")
        # df['effort_estimates'] = df['effort_estimates'].fillna(0)
        df['gs_summary'] = df['gs_summary'].fillna(" ")

        # debug_dataframe(df, "check_before_groupby.xlsx")
        df_client_jira = df.groupby(
            [
                'ClientJiraNo', 'gs_summary', 'effort_estimates',
                'BillingPeriod', 'cc_epic_components',
                'JiraFeaturekey',
            ]
        ).agg({'Totalhrs': 'sum'}).reset_index()
        rows_with_empty_values = df_client_jira.isna().any(axis=1)
        df_with_empty_values = df_client_jira[rows_with_empty_values]
        # debug_dataframe(df_with_empty_values, "df_with_empty_values.xlsx")

        # debug_dataframe(df_client_jira, "df_client_jira.xlsx")

        df_client_jira = df_client_jira.pivot(
            index=[
                'ClientJiraNo', 'gs_summary', 'effort_estimates',
                'cc_epic_components', 'JiraFeaturekey',
            ],
            columns='BillingPeriod',
            values='Totalhrs'
        ).reset_index()
        # debug_dataframe(df_client_jira, "df_client_jira_pivot.xlsx")
        date_columns = [
            col for col in df_client_jira.columns
            if col not in [
                'ClientJiraNo', 'gs_summary', 'effort_estimates',
                'cc_epic_components', 'JiraFeaturekey',
            ]
        ]
        sorted_columns = sorted(date_columns, key=extract_start_date)
        df_client_jira = df_client_jira[
            ['ClientJiraNo', 'gs_summary', 'effort_estimates', 'cc_epic_components', 'JiraFeaturekey', ]
            + sorted_columns
            ]
        df_client_jira['Total'] = df_client_jira[sorted_columns].sum(axis=1)
        # df_client_jira.dropna(how='any', inplace=True, subset='ClientJiraNo')
        # df_client_jira['gs_fixversion'] = df_client_jira['gs_fixversion'].apply(
        #     lambda x: ', '.join(x) if isinstance(x, list) else x)
        df_client_jira['cc_epic_components'] = df_client_jira['cc_epic_components'].apply(
            lambda x: ', '.join(x) if isinstance(x, list) else x
        )
        # df_client_jira_simple = df_client_jira[['ClientJiraNo'] + sorted_columns]

        df_client_jira.rename(columns={
            'ClientJiraNo': 'GS Key',
            'gs_summary': 'Summary',
            'effort_estimates': "Estimates",
            'cc_epic_components': 'Components',
            'JiraFeaturekey': "CC Epic Key",
        }, inplace=True)

        df_custom_key = df[['ClientKey', 'Totalhrs']].copy(deep=True)
        # debug_dataframe(df_custom_key, "df_custom_key.xlsx")
        df_custom_key = df_custom_key[df_custom_key['ClientKey'].map(lambda x: len(x) > 0)]
        # debug_dataframe(df_custom_key, "df_custom_key_0.xlsx")
        df_custom_key['ClientKey'] = df_custom_key['ClientKey'].apply(split_strip_unique)
        df_custom_key['count'] = df_custom_key['ClientKey'].map(lambda x: len(x))
        df_custom_key['TotalHoursCalc'] = (df_custom_key['Totalhrs'] / df_custom_key['count']).round(2)
        # debug_dataframe(df_custom_key, "df_custom_key_1.xlsx")
        df_custom_key = df_custom_key.explode('ClientKey').reset_index(drop=True)
        # debug_dataframe(df_custom_key, "df_custom_key_2.xlsx")

        # Count the occurrences of each original row to divide the hours equally
        # df_custom_key["count"] = df_custom_key.groupby(level=0)["ClientKey"].transform("count")
        # df_custom_key["Totalhrs"] = (df_custom_key["Totalhrs"] / df_custom_key["count"]).round(2)
        # debug_dataframe(df_custom_key, "df_custom_key_3.xlsx")
        # Drop the count column as it's no longer needed
        df_custom_key.drop(columns=["count", 'Totalhrs'], inplace=True)
        df_custom_key = df_custom_key.groupby(["ClientKey"]).sum().reset_index()

        # df_client_jira_simple = df[['ClientJiraNo', 'BillingPeriod', 'Totalhrs']].copy(deep=True)
        # df_client_jira_simple = df_client_jira_simple.groupby(['ClientJiraNo', 'BillingPeriod']).agg({'Totalhrs': 'sum'}).reset_index()
        # df_client_jira_simple = df_client_jira_simple.pivot(
        #     index=['ClientJiraNo'],
        #     columns=['BillingPeriod'],
        #     values=['Totalhrs']
        # ).reset_index()

        list_of_df = [
            df_merge.to_json(orient='records'),
            df_team_data.to_json(orient='records'),
            pivot_df_week.to_json(orient='records', date_format="iso"),
            pivot_df.to_json(orient='records', date_format="iso"),
            # df_client_jira_simple.to_json(orient='records'),
            df_client_jira.to_json(orient='records'),
            df_custom_key.to_json(orient='records')
        ]
        df.drop(columns=['TimeEntryId'], inplace=True)
        df['priority'] = df['priority'].astype(str)
        return df.to_dict("records"), fig, fig_issue_type, message, list_of_df


@callback(
    Output("my-data-grid", "exportDataAsCsv"),
    Input("csv-button", "n_clicks"),
)
def export_data_as_csv(n_clicks):
    if n_clicks:
        return True
    return False


@callback(
    Output(dict(type='file-download-prop', index=MATCH), "data"),
    Input(dict(type='file-download-btn', index=MATCH), "n_clicks"),
    Input(dict(type='file-download-btn', index=MATCH), "id"),
    State("id-wd-df-list-store", "data")
)
def download_files(
        n_clicks: int, call_name: dict, collection_of_dfs: Union[list[pd.DataFrame], dict[str, pd.DataFrame]]
):
    """
    Download files based on the provided names and DataFrames or a list of DataFrames.

    Args:
        n_clicks (int): Number of clicks.
        call_name: id of the pattern matching callback. Same information is obtained using below code
            (This information can also be obtained using the 'clicked' function.)
        collection_of_dfs (Union[Dict[str, pd.DataFrame], List[pd.DataFrame]]):
            If it's a dictionary, it should contain names (keys) and DataFrames (values).
            If it's a list, it should be a list of DataFrames.
    Returns:
        None: This function doesn't return anything. It performs file downloads.
    Raises:
        ValueError: If collection_of_dfs is not a dictionary or a list.
    """

    if n_clicks is not None and n_clicks > 0 and collection_of_dfs is not None:
        my_logger = MyLogger().get_logger()
        if call_name['index'] == "wd-ts":
            filename = "plat_utilization.xlsx"
        else:
            filename = "default.xlsx"

        print(collection_of_dfs)

        output = io.BytesIO()
        excel_writer = pd.ExcelWriter(output, engine="xlsxwriter")
        workbook = excel_writer.book

        def determine_orient(_df_json):
            # Attempt to load the JSON data with various orientations
            for orient in ["split", "records", "index", "columns", "values"]:
                print(f"Checking orient: {orient}")
                try:
                    df = pd.read_json(_df_json, orient=orient)
                    if not df.empty:
                        return orient  # If successful, return the orientation
                except Exception as e:
                    my_logger.debug(f'Exception in determining orient: {e}')
            return None  # If all orientations failed, return None

        def process_df_to_excel(_df_json, _sheet_name):
            orient = determine_orient(_df_json)
            my_logger.info(f"Orient = {orient}")
            df = pd.read_json(_df_json, orient=orient)
            if "WeekEndDate" in df.columns:
                df['WeekEndDate'] = pd.to_datetime(df['WeekEndDate'])
                df['WeekEndDate'] = df['WeekEndDate'].dt.date
                if "team_name" in df.columns:
                    df['Total'] = df.iloc[:, 2:].sum(axis=1)
                else:
                    if os.name != "nt":
                        df.to_csv("/tmp/check_df.csv")
                    df['Total'] = df.iloc[:, 1:].sum(axis=1)

            df.to_excel(excel_writer, index=False, startrow=1, header=False, sheet_name=_sheet_name)
            worksheet = excel_writer.sheets[_sheet_name]
            (max_row, max_col) = df.shape

            # column_settings = [{'header': column, 'total_function': 'sum'} for column in df.columns]
            column_settings = []
            for column in df.columns:
                if column in ['team_name', 'Team Name', 'Resource Name', 'Key', 'Summary']:
                    column_settings.append(
                        {'header': column, }
                    )
                elif column in ['WeekEndDate']:
                    column_settings.append(
                        {'header': column, 'total_string': 'Totals'}
                    )
                else:
                    column_settings.append({'header': column, 'total_function': 'sum'})

            worksheet.add_table(
                0, 0, max_row + 1, max_col - 1,
                {
                    'columns': column_settings, 'style': 'Table Style Medium 2',
                    'total_row': True,
                },
            )
            wrap_format = workbook.add_format({'text_wrap': True})
            format_sheet(df, worksheet, wrap_format)

        if isinstance(collection_of_dfs, list):
            for i, df_json in enumerate(collection_of_dfs):
                process_df_to_excel(df_json, f'Sheet{i + 1}')
        elif isinstance(collection_of_dfs, dict):
            for sheet_name, df_json in collection_of_dfs.items():
                process_df_to_excel(df_json, sheet_name)
        else:
            raise ValueError("Input collection_of_dfs should be either list or dict")
        excel_writer.close()
        return dcc.send_bytes(output.getvalue(), filename)
    else:
        raise PreventUpdate


@callback(
    Output("my-isc-timesheet-grid", "exportDataAsCsv"),
    Input("csv-button", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(_):
    return True


# WD Timesheet | End


