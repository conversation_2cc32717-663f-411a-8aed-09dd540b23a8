#  Template python-build

#  This template allows you to validate your python code.
#  The workflow allows running tests and code linting on the default branch.

image: python:3.11.9

clone:
  depth: 1       # include the last commit

definitions:
  services:
    postgres:
      image: postgres:14
      memory: 2048
      environment:
        POSTGRES_DB: jira
        POSTGRES_USER: jira_rw
        POSTGRES_PASSWORD: jira_rw
        DATABASE_URL: postgresql+psycopg2://jira_rw:jira_rw@localhost:5432/jira
      volumes:
        - ./init-user-db.sh:/docker-entrypoint-initdb.d/init-user-db.sh
    redis:
      image: redis:alpine

pipelines:
  default:
      - step:
          name: Set-up database
          caches:
            - pip
          services:
            - postgres                
            - redis
          script:
            - apt-get update && apt-get install -y build-essential libpq-dev
            - pip install --upgrade pip
            - pip install -r requirements.txt
            - pip install pytest
            - pip install zapcli
          runs-on:
            - self.hosted
            - linux
      - step:
          name: Test
          caches:
            - pip
          services:
            - postgres
          script:
            - apt-get update && apt-get install -y odbcinst1debian2 libodbc1 odbcinst unixodbc gnupg
            - apt install -y odbc-postgresql tdsodbc postgresql-client software-properties-common wget unzip default-jdk
            - mkdir -p /var/log/dashboard
            - echo $KEEPASS_PASSPHRASE
            - echo $KEEPASS_PASSPHRASE | gpg --batch --yes --passphrase-fd 0 --pinentry-mode loopback  -o Database.kdbx -d Database.kdbx.gpg
            - echo $KEEPASS_PASSPHRASE | gpg --batch --yes --passphrase-fd 0 --pinentry-mode loopback -o Database.key -d Database.key.gpg
            - if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
            - pip install pytest
            - pytest -v tests/* --junitxml=test-reports/report.xml
          runs-on:
            - self.hosted
            - linux
      - step:
          name: Generate SBOM using cyclonedx
          caches:
            - pip
          script:
            # install dependecies
            - pip install cyclonedx-bom
            - cyclonedx-py environment -o sbom.json
            - pipe: atlassian/bitbucket-upload-file:0.1.2
              variables:
                BITBUCKET_USERNAME: $BITBUCKET_USERNAME
                BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
                FILENAME: "sbom.json"
          runs-on:
            - self.hosted
            - linux

#      - step:
#          name: Skynk Scan
#          script:
#            - pipe: snyk/snyk-scan:1.0.1
#              variables:
#                SNYK_TOKEN: $SNYK_TOKEN
#                LANGUAGE: "python"
#      - step:
#          name: Lint code
#          script:
#            # Enforce style consistency across Python projects https://flake8.pycqa.org
#            - pip install flake8 flake8-html
#            - flake8 . --extend-exclude=dist,build --exclude=.venv --show-source --statistics --format=html --htmldir=flake8_html_report
#      - step:
#          name: Run SAST Analysis
#          script:
#            - pip install bandit
#            - bandit -r .

#      - step:
#          name: Upload bitbucket pipeline generated files
#          script:
#            - pipe: atlassian/bitbucket-upload-file:0.1.2
#              variables:
#                BITBUCKET_USERNAME: $BITBUCKET_USERNAME
#                BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
#                FILENAME: "app/build/outputs/apk/debug/application-debug.apk"