<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boot Screen Demo - JIRA Dashboard</title>
    <link rel="stylesheet" href="assets/css/boot_screen.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .demo-content {
            padding: 2rem;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .demo-button {
            background: #1890FF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s ease;
        }
        
        .demo-button:hover {
            background: #40A9FF;
        }
        
        .modern-login-container {
            max-width: 400px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .modern-login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #1a1a1a;
        }
        
        .modern-login-subtitle {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .modern-input-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .modern-input-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .modern-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .modern-input:focus {
            outline: none;
            border-color: #1890FF;
        }
        
        .modern-login-button {
            width: 100%;
            background: #1890FF;
            color: white;
            border: none;
            padding: 14px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .modern-login-button:hover {
            background: #40A9FF;
        }
    </style>
</head>
<body>
    <!-- Boot Screen -->
    <div id="boot-screen" class="boot-screen boot-screen-hidden">
        <div class="boot-screen-overlay"></div>
        <div class="boot-screen-content">
            <div class="boot-logo-container">
                <svg width="120" height="120" viewBox="0 0 120 120" class="boot-logo-svg">
                    <defs>
                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop stop-color="#1890FF" stop-opacity="1"/>
                            <stop offset="100%" stop-color="#40A9FF" stop-opacity="1"/>
                        </linearGradient>
                        <filter id="glow">
                            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                            <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <rect width="120" height="120" rx="20" fill="url(#logoGradient)" filter="url(#glow)" class="boot-logo-bg"/>
                    <path d="M30 60C30 43.4315 43.4315 30 60 30V30C76.5685 30 90 43.4315 90 60V60C90 76.5685 76.5685 90 60 90V90C43.4315 90 30 76.5685 30 60V60Z" stroke="white" stroke-width="4" fill="none" class="boot-logo-circle"/>
                    <path d="M60 45V75" stroke="white" stroke-width="4" stroke-linecap="round" class="boot-logo-vertical"/>
                    <path d="M45 60H75" stroke="white" stroke-width="4" stroke-linecap="round" class="boot-logo-horizontal"/>
                </svg>
            </div>
            <h1 class="boot-title">JIRA Dashboard</h1>
            <div class="boot-loading-container">
                <div class="boot-loading-text">Loading...</div>
                <div class="boot-loading-dots">
                    <span class="boot-dot"></span>
                    <span class="boot-dot"></span>
                    <span class="boot-dot"></span>
                </div>
            </div>
            <div class="boot-progress-container">
                <div class="boot-progress-bar" id="boot-progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Demo Content -->
    <div class="demo-content">
        <h1>Boot Screen Demo</h1>
        <p>This demonstrates the boot screen that will appear while your Dash application loads.</p>
        
        <button class="demo-button" onclick="showBootScreen()">Show Boot Screen</button>
        <button class="demo-button" onclick="hideBootScreen()">Hide Boot Screen</button>
        <button class="demo-button" onclick="simulateLogin()">Simulate Login Flow</button>
        
        <!-- Sample Login Form -->
        <div id="login-form-container" class="modern-login-container login-form-visible">
            <div class="modern-login-header">
                <h1 class="modern-login-title">Welcome Back</h1>
                <p class="modern-login-subtitle">Sign in to your account</p>
            </div>
            <div class="modern-input-group">
                <label class="modern-input-label">Email Address</label>
                <input type="email" class="modern-input" placeholder="Enter your email address">
            </div>
            <div class="modern-input-group">
                <label class="modern-input-label">Password</label>
                <input type="password" class="modern-input" placeholder="Enter your password">
            </div>
            <button class="modern-login-button">Sign In</button>
        </div>
    </div>

    <script src="assets/js/boot_screen.js"></script>
    <script>
        function showBootScreen() {
            const bootScreen = document.getElementById('boot-screen');
            const loginContainer = document.getElementById('login-form-container');
            
            bootScreen.classList.remove('boot-screen-hidden');
            loginContainer.classList.add('login-form-hidden');
            loginContainer.classList.remove('login-form-visible');
        }
        
        function hideBootScreen() {
            const bootScreen = document.getElementById('boot-screen');
            const loginContainer = document.getElementById('login-form-container');
            
            bootScreen.classList.add('boot-screen-hidden');
            
            setTimeout(() => {
                loginContainer.classList.remove('login-form-hidden');
                loginContainer.classList.add('login-form-visible');
            }, 500);
        }
        
        function simulateLogin() {
            showBootScreen();
            
            // Simulate loading time
            setTimeout(() => {
                hideBootScreen();
            }, 3000);
        }
        
        // Auto-show boot screen on page load for demo
        window.addEventListener('load', () => {
            setTimeout(() => {
                simulateLogin();
            }, 500);
        });
    </script>
</body>
</html>
