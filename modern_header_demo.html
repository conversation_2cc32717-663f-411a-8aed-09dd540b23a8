<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Header Demo - JIRA Dashboard</title>
    <link rel="stylesheet" href="assets/css/modern_header.css">
    <link rel="stylesheet" href="assets/css/modern_logout.css">
    <script src="https://code.iconify.design/3/3.1.1/iconify.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .demo-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #1a1a1a;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .feature-card h3 {
            margin-top: 0;
            color: #1890ff;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li:before {
            content: "✅";
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Modern Header -->
    <header class="modern-header dark">
        <div class="header-container">
            <!-- Left Section: Logo and Brand -->
            <div class="header-left">
                <a class="brand-logo" href="/">
                    <i class="iconify brand-icon" data-icon="mdi:view-dashboard"></i>
                    <span class="brand-text">JIRA Dashboard</span>
                </a>
            </div>
            
            <!-- Center Section: Navigation Menu -->
            <div class="header-center">
                <nav class="main-navigation">
                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                    
                    <!-- Desktop Navigation -->
                    <ul class="nav-menu" id="nav-menu">
                        <li>
                            <a href="/"><span>Home</span></a>
                        </li>
                        <li>
                            <a href="#" class="mega-menu"><span>PLAT</span></a>
                            <div class="sub-menu-block">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Dashboard</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">By sprint</a></li>
                                            <li><a href="#">By release</a></li>
                                            <li><a href="#">SVN Details</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Reports</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">Sprint Report</a></li>
                                            <li><a href="#">Release Report</a></li>
                                            <li><a href="#">Team Performance</a></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Tools</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">Batch Jobs</a></li>
                                            <li><a href="#">Data Export</a></li>
                                            <li><a href="#">Settings</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a href="#" class="mega-menu"><span>PLP</span></a>
                            <div class="sub-menu-block">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Dashboard</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">Overview</a></li>
                                            <li><a href="#">Analytics</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a href="#" class="mega-menu"><span>PMO</span></a>
                            <div class="sub-menu-block">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Management</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">Project Overview</a></li>
                                            <li><a href="#">Resource Planning</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a href="#" class="mega-menu"><span>R&D</span></a>
                            <div class="sub-menu-block">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h2 class="sub-menu-head">Research</h2>
                                        <ul class="sub-menu-lists">
                                            <li><a href="#">Experiments</a></li>
                                            <li><a href="#">Prototypes</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </nav>
            </div>
            
            <!-- Right Section: User Menu and Actions -->
            <div class="header-right">
                <!-- Notifications -->
                <button class="header-action-btn" id="notifications-btn">
                    <i class="iconify action-icon" data-icon="mdi:bell-outline"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <!-- User Menu -->
                <div class="user-menu-container">
                    <button class="user-menu-trigger" id="user-menu-trigger">
                        <img src="https://via.placeholder.com/32x32/1890FF/white?text=JD" alt="User Avatar" class="user-avatar" id="user-avatar-img">
                        <span class="user-name">John Doe</span>
                        <i class="iconify dropdown-icon" data-icon="mdi:chevron-down"></i>
                    </button>
                    
                    <div class="user-dropdown-menu" id="user-dropdown-menu">
                        <div class="user-dropdown-header">
                            <img src="https://via.placeholder.com/48x48/1890FF/white?text=JD" alt="User Avatar" class="user-avatar">
                            <div class="user-info">
                                <div class="user-display-name">John Doe</div>
                                <div class="user-email"><EMAIL></div>
                            </div>
                        </div>
                        <hr class="dropdown-divider">
                        <div class="dropdown-menu-items">
                            <a href="#" class="dropdown-item" id="profile-link">
                                <i class="iconify item-icon" data-icon="mdi:account-circle"></i>
                                <span>Profile</span>
                            </a>
                            <a href="#" class="dropdown-item" id="settings-link">
                                <i class="iconify item-icon" data-icon="mdi:cog"></i>
                                <span>Settings</span>
                            </a>
                            <hr class="dropdown-divider">
                            <button class="dropdown-item logout-item" id="logout-button">
                                <i class="iconify item-icon" data-icon="mdi:logout"></i>
                                <span>Sign Out</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Demo Content -->
    <div class="demo-content">
        <div class="demo-section">
            <h2>🎉 Modern Header Implementation</h2>
            <p>The header has been completely redesigned with a modern, responsive layout that properly accommodates the user menu and maintains all existing functionality.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎨 Modern Design</h3>
                    <ul class="feature-list">
                        <li>Beautiful gradient background</li>
                        <li>Clean typography and spacing</li>
                        <li>Smooth hover animations</li>
                        <li>Professional brand logo</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📱 Responsive Layout</h3>
                    <ul class="feature-list">
                        <li>Mobile-first design approach</li>
                        <li>Hamburger menu for mobile</li>
                        <li>Touch-friendly interactions</li>
                        <li>Adaptive content layout</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔧 Enhanced Functionality</h3>
                    <ul class="feature-list">
                        <li>Project-based navigation preserved</li>
                        <li>Authorization-based menu access</li>
                        <li>Notification system ready</li>
                        <li>User menu with profile options</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ Performance Optimized</h3>
                    <ul class="feature-list">
                        <li>CSS-only animations</li>
                        <li>Minimal JavaScript footprint</li>
                        <li>Efficient event handling</li>
                        <li>Smooth transitions</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🧪 Test the Features</h2>
            <p>Try out the different interactive elements:</p>
            <ul>
                <li><strong>Desktop:</strong> Hover over menu items to see dropdown submenus</li>
                <li><strong>Mobile:</strong> Click hamburger menu, then tap menu items to expand submenus</li>
                <li><strong>User Menu:</strong> Click the user avatar to open the dropdown menu</li>
                <li><strong>Profile & Settings:</strong> Click Profile/Settings to see centered modal dialogs</li>
                <li><strong>Notifications:</strong> Click the notification bell to see the notification panel</li>
                <li><strong>Logout:</strong> Try the logout functionality with confirmation dialog</li>
                <li><strong>Responsive:</strong> Resize the window to test mobile behavior</li>
            </ul>

            <div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin-top: 1rem; border-left: 4px solid #ffc107;">
                <strong>Mobile Testing:</strong>
                <ul style="margin: 0.5rem 0 0 0;">
                    <li>Menu slides in from left (no more scrolling issues)</li>
                    <li>Tap menu items to expand/collapse submenus</li>
                    <li>User dropdown positioned properly on small screens</li>
                    <li>Modals are centered and responsive</li>
                    <li>Easy to close menu by tapping outside or hamburger icon</li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔄 Migration Benefits & Fixes</h2>
            <p>The new header system provides several advantages and fixes all reported issues:</p>

            <h3>✅ Issues Fixed:</h3>
            <ul>
                <li><strong>Mobile Menu Rendering:</strong> Menu now slides properly from left, no scrolling issues</li>
                <li><strong>iPhone SE Viewport:</strong> User dropdown positioned correctly on ultra-small screens</li>
                <li><strong>iPad Menu Behavior:</strong> Fixed hover scrolling, proper touch interactions</li>
                <li><strong>Menu Closing:</strong> Easy to close with tap outside, hamburger icon, or navigation</li>
                <li><strong>Modal Positioning:</strong> Profile/Settings/Notifications show in screen center</li>
                <li><strong>Touch Interactions:</strong> Optimized for mobile touch with proper event handling</li>
            </ul>

            <h3>🚀 Additional Improvements:</h3>
            <ul>
                <li>✅ <strong>Better Alignment:</strong> User menu properly positioned on the right</li>
                <li>✅ <strong>Modern Design:</strong> Contemporary look matching your login page</li>
                <li>✅ <strong>Preserved Functionality:</strong> All navigation and authorization features intact</li>
                <li>✅ <strong>Enhanced Mobile UX:</strong> Submenu expand/collapse, smooth animations</li>
                <li>✅ <strong>Centered Modals:</strong> Professional dialog system for all screen sizes</li>
                <li>✅ <strong>Performance:</strong> Optimized CSS and JavaScript with proper event handling</li>
            </ul>
        </div>
    </div>

    <script src="assets/js/modern_logout.js"></script>
    <script>
        // Set demo base path
        window.dashBasePath = '/';
        
        // Override logout for demo
        const originalPerformLogout = window.modernLogout.performLogout;
        window.modernLogout.performLogout = function(button) {
            const dialog = button.closest('.logout-confirm-dialog');
            button.innerHTML = '<i class="iconify" data-icon="mdi:loading" style="animation: spin 1s linear infinite;"></i> Signing out...';
            button.disabled = true;
            
            setTimeout(() => {
                dialog.remove();
                window.modernLogout.showNotification('Demo logout completed!', 'info');
            }, 1000);
        };
    </script>
</body>
</html>
