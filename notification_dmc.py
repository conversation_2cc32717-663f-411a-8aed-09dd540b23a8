import dash_mantine_components as dmc
from dash import Output, Input, html, callback, Dash, _dash_renderer
from dash_iconify import <PERSON><PERSON><PERSON><PERSON>

# add this before creating app object
_dash_renderer._set_react_version('18.2.0')

stylesheets = [
    "https://unpkg.com/@mantine/dates@7/styles.css",
    "https://unpkg.com/@mantine/code-highlight@7/styles.css",
    "https://unpkg.com/@mantine/charts@7/styles.css",
    "https://unpkg.com/@mantine/carousel@7/styles.css",
    "https://unpkg.com/@mantine/notifications@7/styles.css",
    "https://unpkg.com/@mantine/nprogress@7/styles.css",
]

app = Dash(
    __name__, external_stylesheets=stylesheets
)


app.layout = dmc.MantineProvider(
    [
        dmc.NotificationProvider(),
html.Div(id="notifications-container"),
    dmc.Title('Hello Dash Mantine Components!', size="h3"),
    dmc.<PERSON><PERSON>('Click Me', color="teal",),
        dmc.<PERSON><PERSON>("Show Notification", id="notify"),
dmc.<PERSON><PERSON>(
    "GitHub",
    leftSection=DashIconify(icon="radix-icons:github-logo", width=20),
    rightSection=dmc.Badge("3", circle=True, color="gray"),
)
    ]
)

@callback(
    Output("notifications-container", "children"),
    Input("notify", "n_clicks"),
    prevent_initial_call=True,
)
def show(n_clicks):
    return dmc.Notification(
        title="Hey there!",
        id="simple-notify",
        action="show",
        message="Notifications in Dash, Awesome!",
        icon=DashIconify(icon="ic:round-celebration"),
    )

if __name__ == "__main__":
    app.run_server(debug=True)