<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Logout Page</title>
    <base href="{{ dash_base_path }}">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.0.0-alpha.6/css/bootstrap.min.css'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css'>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="//ajax.googleapis.com/ajax/libs/angularjs/1.4.9/angular.min.js"></script>
    <script src="{{ url_for('static', filename='main.js') }}"></script>
   <style>
        [ng-cloak] {
            display: none !important;
        }
    </style>
    <script type="text/javascript">
        window.dashBasePath = "{{ dash_base_path }}";
    </script>
</head>
<body ng-app="logoutApp" ng-controller="logoutController" ng-cloak>
{#
Source: https://codepen.io/Marem/pen/BWqgOr
Learnt how to use angular script with jinja2
#}
{% block title %}{% endblock %}

{% block navbar %}

{% endblock %}

{% block content %}
<div class="background-photo">
		<div class="jumbotron">
			<div class="container">
                {% if 'user_initiated' in session['logout_reason'] %}
                <h1>You have logged off!</h1>
                {% elif 'inactivity' in session['logout_reason'] %}
                <h1>logged off due to inactivity</h1>
                {% else %}
                <h1>You have not logged in</h1>
                {% endif %}
			</div>

		</div>
		<div class="middle-block">
      <div ng-if="!!loadingShowed">
        login in {{ '{{ seconds }}' }}  seconds.
      </div >
      <div ng-if="!loadingShowed">
          {% if 'user_initiated' in session['logout_reason'] %}
          <h4>Please, click below to log back in.</h4>
          {% elif 'inactivity' in session['logout_reason'] %}
          <h4>Please, click below to log back in.</h4>
          {% else %}
          <h4>Please, click below to login.</h4>
          {% endif %}

      </div>
			<div class="round-class">
                <a href="javascript:void(0)" ng-click="iconClick()">
				<i ng-class="{'fa fa-3x':true, 'fa-spinner fa-pulse fa-fw':!!loadingShowed, 'fa-sign-in':!loadingShowed}" ></i>
                    </a>
				<span class="sr-only">Loading...</span>
			</div>
		</div>
		<div class="second">
			<div class="container">
				<div class="col-xs-12 col-sm-6">
          <div class="row">
          <div class="round-class ball">
          <i class="fa fa-thumbs-o-up fa-lg" aria-hidden="true"></i></div>
            <div class="right-text">
						Thanks for using the site. Hope you liked it.
					</div>
          </div>
          <div class="row">
             <div class="round-class ball">
          <i class="fa fa-mobile fa-2x" aria-hidden="true"></i></div>
            <div class="right-text">
						Pls share your valuable feedback
            </div>
				</div>
			</div>
		</div>
	</div>
	</div>
{% endblock %}

</body>
</html>