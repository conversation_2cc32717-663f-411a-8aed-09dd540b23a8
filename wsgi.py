import sys

import custom_container
from index import server as application
# from dash_app import socketio
from index import dash_app

# import data.get_from_db as db

container = custom_container.AppContainer()
container.wire(modules=["callbacks", "layouts", "index", sys.modules[__name__]])
# container.wire(modules=["callbacks", "layouts", "index", sys.modules[__name__]])
application.container = container


if __name__ == '__main__':
    # socketio.run(dash_app=application, debug=True)
    application.run(debug=True, port=1951, host="localhost")
