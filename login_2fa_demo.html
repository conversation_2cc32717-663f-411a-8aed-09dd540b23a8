<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern 2FA Login Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/modern_login.css">
</head>
<body class="login-page">
    <div class="modern-login-page modern-2fa-page">
        <!-- Notifications Container -->
        <div id="notifications-container"></div>
        
        <!-- Modern 2FA Container -->
        <div class="modern-login-container modern-2fa-container">
            <!-- 2FA Header with Security Icon -->
            <div class="modern-login-header">
                <div class="modern-2fa-icon">
                    <i class="fa fa-shield-alt"></i>
                </div>
                <h1 class="modern-login-title">Two-Factor Authentication</h1>
                <p class="modern-login-subtitle">Enter the 6-digit code from your Google Authenticator app</p>
            </div>
            
            <!-- 2FA Form -->
            <form class="modern-login-form" onsubmit="handle2FA(event)">
                <!-- OTP Input Group -->
                <div class="modern-input-group">
                    <label class="modern-input-label" for="otp">Authentication Code</label>
                    <div class="modern-input-field modern-otp-field">
                        <input
                            id="otp"
                            type="text"
                            placeholder="000000"
                            class="modern-input modern-otp-input"
                            maxlength="6"
                            pattern="[0-9]+"
                            required
                            autocomplete="one-time-code"
                            autofocus
                        />
                        <i class="fa fa-key modern-input-icon"></i>
                    </div>
                    <div id="validation-message" class="modern-validation-message"></div>
                </div>
                
                <!-- Instructions -->
                <div class="modern-2fa-instructions">
                    <div class="modern-instruction-item">
                        <i class="fa fa-mobile-alt"></i>
                        <span>Open your Google Authenticator app</span>
                    </div>
                    <div class="modern-instruction-item">
                        <i class="fa fa-search"></i>
                        <span>Find your account entry</span>
                    </div>
                    <div class="modern-instruction-item">
                        <i class="fa fa-keyboard"></i>
                        <span>Enter the 6-digit code above</span>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="modern-2fa-buttons">
                    <button
                        type="submit"
                        class="modern-login-button modern-2fa-primary-button"
                        id="verifyButton"
                    >
                        Verify Code
                    </button>
                    <button
                        type="button"
                        class="modern-2fa-secondary-button"
                        onclick="resetPIN()"
                    >
                        Reset PIN
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // OTP Input formatting and validation
        function setupOTPInput() {
            const otpInput = document.getElementById('otp');
            
            // Format input to only allow numbers
            otpInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
                if (value.length > 6) {
                    value = value.substring(0, 6);
                }
                e.target.value = value;
                
                // Update visual state
                updateInputState(e.target, value);
                
                // Auto-submit when 6 digits are entered
                if (value.length === 6) {
                    setTimeout(() => {
                        document.getElementById('verifyButton').click();
                    }, 500);
                }
            });
            
            // Handle paste events
            otpInput.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                const digits = paste.replace(/\D/g, '').substring(0, 6);
                e.target.value = digits;
                updateInputState(e.target, digits);
                
                if (digits.length === 6) {
                    setTimeout(() => {
                        document.getElementById('verifyButton').click();
                    }, 500);
                }
            });
            
            // Handle keydown for better UX
            otpInput.addEventListener('keydown', function(e) {
                // Allow: backspace, delete, tab, escape, enter
                if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true)) {
                    return;
                }
                // Ensure that it is a number and stop the keypress
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });
        }
        
        // Update input visual state
        function updateInputState(input, value) {
            input.classList.remove('success', 'error');
            clearValidationMessage();
            
            if (value.length === 6) {
                input.classList.add('success');
            }
        }
        
        // Handle 2FA form submission
        function handle2FA(event) {
            event.preventDefault();
            
            const otp = document.getElementById('otp').value;
            const button = document.getElementById('verifyButton');
            const otpInput = document.getElementById('otp');
            
            // Clear previous validation
            clearValidationMessage();
            otpInput.classList.remove('success', 'error');
            
            // Validate OTP
            if (!otp || otp.trim() === '') {
                showValidationError('Please enter the 6-digit authentication code');
                otpInput.classList.add('error');
                return;
            }
            
            if (!/^\d{6}$/.test(otp.trim())) {
                showValidationError('Authentication code must be exactly 6 digits');
                otpInput.classList.add('error');
                return;
            }
            
            // Show loading state
            button.classList.add('loading');
            button.disabled = true;
            button.textContent = 'Verifying...';
            
            // Simulate 2FA verification
            setTimeout(() => {
                button.classList.remove('loading');
                button.disabled = false;
                button.textContent = 'Verify Code';
                
                // Simulate different outcomes based on code
                if (otp === '123456') {
                    otpInput.classList.add('success');
                    showNotification('Authentication successful! (Demo)', 'success');
                } else if (otp === '000000') {
                    otpInput.classList.add('error');
                    showValidationError('Invalid authentication code. Please try again.');
                } else {
                    otpInput.classList.add('success');
                    showNotification('Code verified! Redirecting... (Demo)', 'success');
                }
            }, 2000);
        }
        
        // Reset PIN functionality
        function resetPIN() {
            showNotification('PIN reset request sent. Please check your email. (Demo)', 'info');
        }
        
        // Show validation error
        function showValidationError(message) {
            const validationDiv = document.getElementById('validation-message');
            validationDiv.innerHTML = `
                <div class="modern-error-message">
                    <i class="fa fa-exclamation-circle"></i>
                    ${message}
                </div>
            `;
        }
        
        // Clear validation message
        function clearValidationMessage() {
            const validationDiv = document.getElementById('validation-message');
            validationDiv.innerHTML = '';
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notifications-container');
            
            const notification = document.createElement('div');
            notification.className = `modern-${type}-message`;
            notification.innerHTML = `
                <i class="fa fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${message}
            `;
            
            container.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 5000);
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            setupOTPInput();
            
            // Add entrance animations
            const formElements = document.querySelectorAll('.modern-2fa-icon, .modern-input-group, .modern-2fa-instructions, .modern-2fa-buttons');
            formElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.4s ease-out';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 200 + (index * 150));
            });
            
            // Demo instructions
            setTimeout(() => {
                showNotification('Demo: Try entering "123456" for success or "000000" for error', 'info');
            }, 2000);
        });
    </script>
</body>
</html>
