import os

import pandas as pd

try:
    from .restapi import get_rmp_portal
except ImportError:
    from restapi import get_rmp_portal

from pptx import Presentation

from pptx.util import Inches, Pt
from pptx.chart.data import CategoryChartData
from pptx.enum.chart import XL_CHART_TYPE
from pptx.enum.chart import XL_TICK_MARK
from pptx.dml.color import RGBColor, ColorFormat

from pptx.enum.text import PP_PARAGRAPH_ALIGNMENT
from pptx.enum.text import MSO_VERTICAL_ANCHOR, MSO_AUTO_SIZE
from pptx.enum.dml import MSO_LINE_DASH_STYLE, MSO_THEME_COLOR_INDEX
from pptx.enum.chart import XL_CHART_TYPE, XL_LEGEND_POSITION, XL_DATA_LABEL_POSITION
from pptx.enum.shapes import MSO_SHAPE_TYPE


table_style_dict = {
    'NoStyleNoGrid': '{2D5ABB26-0587-4C30-8999-92F81FD0307C}',
    'ThemedStyle1Accent1': '{3C2FFA5D-87B4-456A-9821-1D502468CF0F}',
    'ThemedStyle1Accent2': '{284E427A-3D55-4303-BF80-6455036E1DE7}',
    'ThemedStyle1Accent3': '{69C7853C-536D-4A76-A0AE-DD22124D55A5}',
    'ThemedStyle1Accent4': '{775DCB02-9BB8-47FD-8907-85C794F793BA}',
    'ThemedStyle1Accent5': '{35758FB7-9AC5-4552-8A53-C91805E547FA}',
    'ThemedStyle1Accent6': '{08FB837D-C827-4EFA-A057-4D05807E0F7C}',
    'NoStyleTableGrid': '{5940675A-B579-460E-94D1-54222C63F5DA}',
    'ThemedStyle2Accent1': '{D113A9D2-9D6B-4929-AA2D-F23B5EE8CBE7}',
    'ThemedStyle2Accent2': '{18603FDC-E32A-4AB5-989C-0864C3EAD2B8}',
    'ThemedStyle2Accent3': '{306799F8-075E-4A3A-A7F6-7FBC6576F1A4}',
    'ThemedStyle2Accent4': '{E269D01E-BC32-4049-B463-5C60D7B0CCD2}',
    'ThemedStyle2Accent5': '{327F97BB-C833-4FB7-BDE5-3F7075034690}',
    'ThemedStyle2Accent6': '{638B1855-1B75-4FBE-930C-398BA8C253C6}',
    'LightStyle1': '{9D7B26C5-4107-4FEC-AEDC-1716B250A1EF}',
    'LightStyle1Accent1': '{3B4B98B0-60AC-42C2-AFA5-B58CD77FA1E5}',
    'LightStyle1Accent2': '{0E3FDE45-AF77-4B5C-9715-49D594BDF05E}',
    'LightStyle1Accent3': '{C083E6E3-FA7D-4D7B-A595-EF9225AFEA82}',
    'LightStyle1Accent4': '{D27102A9-8310-4765-A935-A1911B00CA55}',
    'LightStyle1Accent5': '{5FD0F851-EC5A-4D38-B0AD-8093EC10F338}',
    'LightStyle1Accent6': '{68D230F3-CF80-4859-8CE7-A43EE81993B5}',
    'LightStyle2': '{7E9639D4-E3E2-4D34-9284-5A2195B3D0D7}',
    'LightStyle2Accent1': '{69012ECD-51FC-41F1-AA8D-1B2483CD663E}',
    'LightStyle2Accent2': '{72833802-FEF1-4C79-8D5D-14CF1EAF98D9}',
    'LightStyle2Accent3': '{F2DE63D5-997A-4646-A377-4702673A728D}',
    'LightStyle2Accent4': '{17292A2E-F333-43FB-9621-5CBBE7FDCDCB}',
    'LightStyle2Accent5': '{5A111915-BE36-4E01-A7E5-04B1672EAD32}',
    'LightStyle2Accent6': '{912C8C85-51F0-491E-9774-3900AFEF0FD7}',
    'LightStyle3': '{616DA210-FB5B-4158-B5E0-FEB733F419BA}',
    'LightStyle3Accent1': '{BC89EF96-8CEA-46FF-86C4-4CE0E7609802}',
    'LightStyle3Accent2': '{5DA37D80-6434-44D0-A028-1B22A696006F}',
    'LightStyle3Accent3': '{8799B23B-EC83-4686-B30A-512413B5E67A}',
    'LightStyle3Accent4': '{ED083AE6-46FA-4A59-8FB0-9F97EB10719F}',
    'LightStyle3Accent5': '{BDBED569-4797-4DF1-A0F4-6AAB3CD982D8}',
    'LightStyle3Accent6': '{E8B1032C-EA38-4F05-BA0D-38AFFFC7BED3}',
    'MediumStyle1': '{793D81CF-94F2-401A-BA57-92F5A7B2D0C5}',
    'MediumStyle1Accent1': '{B301B821-A1FF-4177-AEE7-76D212191A09}',
    'MediumStyle1Accent2': '{9DCAF9ED-07DC-4A11-8D7F-57B35C25682E}',
    'MediumStyle1Accent3': '{1FECB4D8-DB02-4DC6-A0A2-4F2EBAE1DC90}',
    'MediumStyle1Accent4': '{1E171933-4619-4E11-9A3F-F7608DF75F80}',
    'MediumStyle1Accent5': '{FABFCF23-3B69-468F-B69F-88F6DE6A72F2}',
    'MediumStyle1Accent6': '{10A1B5D5-9B99-4C35-A422-299274C87663}',
    'MediumStyle2': '{073A0DAA-6AF3-43AB-8588-CEC1D06C72B9}',
    'MediumStyle2Accent1': '{5C22544A-7EE6-4342-B048-85BDC9FD1C3A}',
    'MediumStyle2Accent2': '{21E4AEA4-8DFA-4A89-87EB-49C32662AFE0}',
    'MediumStyle2Accent3': '{F5AB1C69-6EDB-4FF4-983F-18BD219EF322}',
    'MediumStyle2Accent4': '{00A15C55-8517-42AA-B614-E9B94910E393}',
    'MediumStyle2Accent5': '{7DF18680-E054-41AD-8BC1-D1AEF772440D}',
    'MediumStyle2Accent6': '{93296810-A885-4BE3-A3E7-6D5BEEA58F35}',
    'MediumStyle3': '{8EC20E35-A176-4012-BC5E-935CFFF8708E}',
    'MediumStyle3Accent1': '{6E25E649-3F16-4E02-A733-19D2CDBF48F0}',
    'MediumStyle3Accent2': '{85BE263C-DBD7-4A20-BB59-AAB30ACAA65A}',
    'MediumStyle3Accent3': '{EB344D84-9AFB-497E-A393-DC336BA19D2E}',
    'MediumStyle3Accent4': '{EB9631B5-78F2-41C9-869B-9F39066F8104}',
    'MediumStyle3Accent5': '{74C1A8A3-306A-4EB7-A6B1-4F7E0EB9C5D6}',
    'MediumStyle3Accent6': '{2A488322-F2BA-4B5B-9748-0D474271808F}',
    'MediumStyle4': '{D7AC3CCA-C797-4891-BE02-D94E43425B78}',
    'MediumStyle4Accent1': '{69CF1AB2-1976-4502-BF36-3FF5EA218861}',
    'MediumStyle4Accent2': '{8A107856-5554-42FB-B03E-39F5DBC370BA}',
    'MediumStyle4Accent3': '{0505E3EF-67EA-436B-97B2-0124C06EBD24}',
    'MediumStyle4Accent4': '{C4B1156A-380E-4F78-BDF5-A606A8083BF9}',
    'MediumStyle4Accent5': '{22838BEF-8BB2-4498-84A7-C5851F593DF1}',
    'MediumStyle4Accent6': '{16D9F66E-5EB9-4882-86FB-DCBF35E3C3E4}',
    'DarkStyle1': '{E8034E78-7F5D-4C2E-B375-FC64B27BC917}',
    'DarkStyle1Accent1': '{125E5076-3810-47DD-B79F-674D7AD40C01}',
    'DarkStyle1Accent2': '{37CE84F3-28C3-443E-9E96-99CF82512B78}',
    'DarkStyle1Accent3': '{D03447BB-5D67-496B-8E87-E561075AD55C}',
    'DarkStyle1Accent4': '{E929F9F4-4A8F-4326-A1B4-22849713DDAB}',
    'DarkStyle1Accent5': '{8FD4443E-F989-4FC4-A0C8-D5A2AF1F390B}',
    'DarkStyle1Accent6': '{AF606853-7671-496A-8E4F-DF71F8EC918B}',
    'DarkStyle2': '{5202B0CA-FC54-4496-8BCA-5EF66A818D29}',
    'DarkStyle2Accent1Accent2': '{0660B408-B3CF-4A94-85FC-2B1E0A45F4A2}',
    'DarkStyle2Accent3Accent4': '{91EBBBCC-DAD2-459C-BE2E-F6DE35CF9A28}',
    'DarkStyle2Accent5Accent6': '{46F890A9-2807-4EBB-B81D-B2AA78EC7F39}'
}


class CreateReport:
    def __init__(self, output_slide_name: str, template_file: str = ""):
        self.slide = None
        self.slide_table = None
        self.tbl_shape = None
        self.output_file_name = output_slide_name
        if template_file != "":
            self.prs = Presentation(template_file)
        else:
            self.prs = Presentation()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.prs.save(self.output_file_name)

    def save(self):
        self.prs.save(self.output_file_name)

    def add_slide(self, master_slide_no: int):
        self.slide = self.prs.slides.add_slide(self.prs.slide_layouts[master_slide_no])

    def add_title(self, title: str = ""):
        self.slide.shapes.title.text = title

    def add_subtitle(self, subtitle: str = ""):
        self.slide.placeholders[1].text = subtitle

    def add_section(self, value: str):
        for shape in self.slide.shapes:
            if shape.has_text_frame:
                text_frame = shape.text_frame
                text_frame.text = value

    def show_shapes(self):
        for shape in self.slide.shapes:
            if shape.has_text_frame:
                text_frame = shape.text_frame
                print(dir(text_frame))
                text_frame.text = "Cookie"
                print(text_frame.text)

    def add_slide_table(
            self,
            rows: int, cols: int,
            left: float, top: float, width: float, height: float
    ):
        self.slide_table = self.slide.shapes.add_table(
            rows, cols,
            Inches(left), Inches(top), Inches(width), Inches(height)
        ).table


    def add_table_data(self, df: pd.DataFrame, column_header: list):
        # Add header
        for i, col in enumerate(column_header):
            self.slide_table.cell(0, i).text = col

        for i in range(df.shape[0]):
            for j in range(df.shape[1]):
                self.slide_table.cell(i + 1, j).text = str(df.values[i, j])
                self.slide_table.cell(i + 1, j).text_frame.auto_size = MSO_AUTO_SIZE.SHAPE_TO_FIT_TEXT

    def add_text_box_with_text(
            self, left, top, width, height, text_data: str, font_size: int
    ):
        shape = self.slide.shapes.add_shape(
            MSO_SHAPE_TYPE.AUTO_SHAPE, Inches(left), Inches(top), Inches(width), Inches(height)
        )
        shape.shadow.inherit = False
        shape.fill.background()
        shape.text = text_data
        for paragraph in shape.text_frame.paragraphs:
            paragraph.font.size = Pt(font_size)
            paragraph.font.color.rgb = RGBColor(0, 0, 0)

    def add_table_data_with_index(self, df: pd.DataFrame, column_header: list):
        # Add Header
        for i, col in enumerate(column_header):
            self.slide_table.cell(0, i).text = col

        # Add data from index column
        for i in range(df.shape[0]):
            self.slide_table.cell(i + 1, 0).text = str(df.index[i])

        # Add data from columns
        for i in range(df.shape[0]):
            for j in range(df.shape[1]):
                # textbox_shape = self.slide.shapes.add_shape(
                #     MSO_SHAPE_TYPE.TEXT_BOX, Cm(1), Cm(1), Cm(4), Cm(1)
                # )
                # textbox_shape.text = str(df.iloc[i, j])
                #
                # # Set the text box properties
                # textbox_shape.text_frame.vertical_anchor = MSO_VERTICAL_ANCHOR.MIDDLE
                self.slide_table.cell(i + 1, j + 1).text = str(df.iloc[i, j])

    def print_table_shape(self):
        print(self.tbl_shape._element.graphic.graphicData.tbl[0][-1].text)


    def custom_style_table(self, font_size: int):
        # Define the colors for the custom style
        background_color = RGBColor(255, 255, 255)
        text_color = RGBColor(0, 0, 0)
        border_color = RGBColor(128, 128, 128)

        # style_id = '{284E427A-3D55-4303-BF80-6455036E1DE7}'
        # tbl = self.tbl_shape._element.graphic.graphicData.tbl
        # tbl[0][-1].text = '{284E427A-3D55-4303-BF80-6455036E1DE7}'

        # Create the custom style
        custom_style = {
            'fill': background_color,
            'font': 'Arial',
            'font_size': Pt(11),
            'font_color': text_color,
            'align': PP_PARAGRAPH_ALIGNMENT.LEFT,
            'valign': MSO_VERTICAL_ANCHOR.MIDDLE,
            'border': {
                'color': border_color,
                'type': 'solid',
                'size': 1
            }
        }

        for row in self.slide_table.rows:
            for cell in row.cells:
                for paragraph in cell.text_frame.paragraphs:
                    paragraph.font.size = Pt(font_size)
                # cell.fill.solid()
                # cell.fill.fore_color.rgb = custom_style['fill']
                cell.text_frame.paragraphs[0].font.name = custom_style['font']
                cell.text_frame.paragraphs[0].font.size = custom_style['font_size']
                cell.text_frame.paragraphs[0].font.color.rgb = custom_style['font_color']
                cell.text_frame.paragraphs[0].alignment = custom_style['align']
                cell.vertical_anchor = custom_style['valign']


def get_data():
    df = get_rmp_portal()
    df.query("environmentName not in ['GS-FW', 'GS-UAT', 'GS-INT', 'Jazz-PERF']", inplace=True)
    df_list = []
    # Replace POD1 with IOPERF
    df.loc[df['environmentName'] == "PLAT-IOPERF", ['podName']] = 'IOPERF'

    for pods in [['POD3'], ['IOPERF'], ['POD1', 'POD2', 'POD4']]:
        df_pods = df.query("podName in @pods").copy()
        df_pods['combined'] = df_pods.apply(
            lambda x: f"App: {x['applicationVersion']}\nPF: {x['platformVersion']}",
            axis=1
        )
        df_pods = pd.pivot_table(
            df_pods, values=['combined'],
            index=['environmentName'], columns=['podName'],
            aggfunc=lambda x: x
        )
        df_pods.fillna("", inplace=True)
        df_pods = df_pods.reset_index()
        df_list.append(df_pods)
    return df_list


def main_new():
    pod_data_list = get_data()

    with CreateReport(r"e:\vishal\output.pptx",
                      template_file=r"C:\Users\<USER>\PycharmProjects\jiradashboard\data\monthly.pptx") as rpt:
        rpt.add_slide(0)
        rpt.add_title("Weekly Status Report")
        rpt.add_subtitle("Cookie & Jazz")
        print("Done with title slide")
        rpt.add_slide(master_slide_no=5)
        rpt.add_title("Application and Platform version: Jazz & IOPERF")

        rpt.add_slide_table(
            rows=pod_data_list[0].shape[0] + 1, cols=pod_data_list[0].shape[1],
            left=1.5, top=1, width=8, height=1.5
        )
        rpt.add_table_data(pod_data_list[0], ['Jazz', 'POD3'])
        rpt.custom_style_table(12)

        rpt.add_slide_table(
            rows=pod_data_list[1].shape[0] + 1, cols=pod_data_list[1].shape[1],
            left=1.5, top=5, width=8, height=1.5
        )
        rpt.add_table_data(pod_data_list[1], ['IO-PERF', 'DC'])
        rpt.custom_style_table(12)

        rpt.add_slide(master_slide_no=5)
        rpt.add_title("Application and Platform version: Cookie")
        rpt.add_slide_table(
            rows=pod_data_list[2].shape[0] + 1, cols=pod_data_list[2].shape[1],
            left=1.5, top=1, width=8, height=1.5
        )
        rpt.add_table_data(pod_data_list[2], ['Cookie', 'POD1', 'POD2', 'POD4'])
        rpt.custom_style_table(12)


def main():
    template_file = r"C:\Users\<USER>\PycharmProjects\jiradashboard\data\monthly.pptx"

    prs = Presentation(template_file)
    # print(json.dumps(dir(prs.slide_master)))
    # for count, slide in enumerate(prs.slide_master.slide_layouts):
    #     print(count, slide)

    # Add a new slide to the presentation
    slide = prs.slides.add_slide(prs.slide_layouts[0])
    title = slide.shapes.title
    subtitle = slide.placeholders[1]

    title.text = "Weekly Status Report"
    subtitle.text = "Cookie & Jazz"

    # bullet_slide_layout = prs.slide_layouts[1]
    # slide = prs.slides.add_slide(bullet_slide_layout)
    # shapes = slide.shapes
    #
    # title_shape = shapes.title
    # body_shape = shapes.placeholders[1]
    #
    # title_shape.text = 'Adding a Bullet Slide'
    #
    # tf = body_shape.text_frame
    # tf.text = 'Find the bullet slide layout'
    #
    # p = tf.add_paragraph()
    # p.text = 'Use _TextFrame.text for first bullet'
    # p.level = 1
    #
    # p = tf.add_paragraph()
    # p.text = 'Use _TextFrame.add_paragraph() for subsequent bullets'
    # p.level = 2

    df_master = get_rmp_portal()
    df_master.query("environmentName not in ['GS-FW', 'GS-UAT', 'GS-INT', 'Jazz-PERF']", inplace=True)
    df = df_master.query("podName == 'POD3'").reset_index().copy()
    df_cookie = df_master.query("podName != 'POD3'").reset_index(drop=True).copy()
    df_cookie['combined'] = df_cookie.apply(
        lambda x: f"App: {x['applicationVersion']}\nPF: {x['platformVersion']}", axis=1
    )
    # df_cookie.drop(columns=['applicationVersion', 'platformVersion'], inplace=True)

    df_pivot = pd.pivot_table(
        df_cookie, values=['combined'], index=['environmentName'], columns=['podName'],
        aggfunc=lambda x: x
    )

    df['POD3'] = df.apply(
        lambda _row: f"App: {_row['applicationVersion']}\nPF: {_row['platformVersion']}", axis=1)

    df_cookie = df_pivot.reset_index()

    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    title.text = "Application and Platform version: Jazz & IOPERF"
    font_size = Pt(11)
    table_shape = slide.shapes.add_table(rows=len(df) + 1, cols=3, left=Inches(1.5), top=Inches(1.25), width=Inches(8),
                                         height=Inches(1.5))

    # Populate the table with the data from the DataFrame
    # First, add the header row
    table_shape.table.cell(0, 0).text = "Environment"
    table_shape.table.cell(0, 1).text = "POD3"
    # table_shape.table.cell(0, 2).text = "Pod Name"

    # Then, add the data rows
    for i, row in df.iterrows():
        table_shape.table.cell(i + 1, 0).text = row['environmentName']
        table_shape.table.cell(i + 1, 1).text = row['POD3']
        # table_shape.table.cell(i + 1, 2).text = row['podName']
    for cell in table_shape.table.iter_cells():
        cell.text_frame.paragraphs[0].font.size = font_size

    # Add IO PERF

    df = df_master.query("environmentName == 'PLAT-IOPERF'").reset_index().copy()
    df['IOPERF'] = df.apply(
        lambda _row: f"App: {_row['applicationVersion']}\nPF: {_row['platformVersion']}", axis=1)

    table_shape = slide.shapes.add_table(rows=len(df) + 1, cols=3, left=Inches(1.5), top=Inches(5), width=Inches(8),
                                         height=Inches(1.5))

    # Populate the table with the data from the DataFrame
    # First, add the header row
    table_shape.table.cell(0, 0).text = "Environment"
    table_shape.table.cell(0, 1).text = "IOPERF"
    # table_shape.table.cell(0, 2).text = "Pod Name"

    # Then, add the data rows
    for i, row in df.iterrows():
        table_shape.table.cell(i + 1, 0).text = row['environmentName']
        table_shape.table.cell(i + 1, 1).text = row['IOPERF']
        # table_shape.table.cell(i + 1, 2).text = row['podName']
    for cell in table_shape.table.iter_cells():
        cell.text_frame.paragraphs[0].font.size = font_size

    # Add Cookie table
    # df_cookie.drop(columns=['applicationVersion', 'platformVersion', 'podName'], inplace=True)

    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    title.text = "Cookie Environment Status"

    rows = df_cookie.shape[0] + 1
    cols = df_cookie.shape[1]

    table_shape = slide.shapes.add_table(rows=rows, cols=cols, left=Inches(1.5), top=Inches(1), width=Inches(8),
                                         height=Inches(1.5)).table

    # Define the colors for the custom style
    background_color = RGBColor(255, 255, 255)
    text_color = RGBColor(0, 0, 0)
    border_color = RGBColor(128, 128, 128)

    # Create the custom style
    custom_style = {
        'fill': background_color,
        'font': 'Arial',
        'font_size': Pt(11),
        'font_color': text_color,
        'align': PP_PARAGRAPH_ALIGNMENT.LEFT,
        'valign': MSO_VERTICAL_ANCHOR.MIDDLE,
        'border': {
            'color': border_color,
            'type': 'solid',
            'size': 1
        }
    }

    # Populate the table with the data from the DataFrame
    # First, add the header row
    table_shape.cell(0, 0).text = "Environment"
    table_shape.cell(0, 1).text = "POD1"
    table_shape.cell(0, 2).text = "POD2"
    table_shape.cell(0, 3).text = "POD4"

    # Then, add the data rows
    df_cookie.fillna("", inplace=True)
    # Populate the table with the values from the DataFrame
    for i in range(df_cookie.shape[0]):
        for j in range(cols):
            table_shape.cell(i + 1, j).text = str(df_cookie.values[i, j])

    for row in table_shape.rows:
        for cell in row.cells:
            for paragraph in cell.text_frame.paragraphs:
                paragraph.font.size = font_size

    for row in table_shape.rows:
        for cell in row.cells:
            cell.fill.solid()
            cell.fill.fore_color.rgb = custom_style['fill']
            cell.text_frame.paragraphs[0].font.name = custom_style['font']
            cell.text_frame.paragraphs[0].font.size = custom_style['font_size']
            cell.text_frame.paragraphs[0].font.color.rgb = custom_style['font_color']
            cell.text_frame.paragraphs[0].alignment = custom_style['align']
            cell.vertical_anchor = custom_style['valign']

    prs.save("test.pptx")


def create_chart_table():
    # create a new PowerPoint presentation
    template_file = r"C:\Users\<USER>\PycharmProjects\jiradashboard\data\monthly.pptx"
    prs = c(template_file)

    # add a new slide
    slide = prs.slides.add_slide(prs.slide_layouts[5])

    # define the table dimensions
    rows = 4
    cols = 5

    # define the table size and position
    left = Inches(1.0)
    top = Inches(2.0)
    width = Inches(8.0)
    height = Inches(1.5)

    # add the table to the slide
    table = slide.shapes.add_table(rows, cols, left, top, width, height).table

    # add headers to the table
    table.cell(0, 0).text = 'project'
    table.cell(0, 1).text = 'epic'
    table.cell(0, 2).text = 'story_todo'
    table.cell(0, 3).text = 'story_wip'
    table.cell(0, 4).text = 'storydone'

    # add data to the table
    table.cell(1, 0).text = 'Cookie Enhancement'
    table.cell(1, 1).text = 'PLAT-87423'
    table.cell(1, 2).text = '2'
    table.cell(1, 3).text = '4'
    table.cell(1, 4).text = '1'

    table.cell(2, 0).text = 'Cookie Enhancement'
    table.cell(2, 1).text = 'PLAT-61573'
    table.cell(2, 2).text = '0'
    table.cell(2, 3).text = '0'
    table.cell(2, 4).text = '0'

    table.cell(3, 0).text = 'Jazz Enhancement'
    table.cell(3, 1).text = 'PLAT-79263'
    table.cell(3, 2).text = '1'
    table.cell(3, 3).text = '0'
    table.cell(3, 4).text = '0'

    # table.cell(4, 0).text = 'Jazz Enhancement'
    # table.cell(4, 1).text = 'PLAT-88951'
    # table.cell(4, 2).text = '2'
    # table.cell(4, 3).text = '2'
    # table.cell(4, 4).text = '1'

    # create presentation with 1 slide ------
    # prs = Presentation()
    shapes = slide.shapes

    left = top =  Inches(1.0)
    top = Inches(5.25)
    width = Inches(6)
    height = Inches(0.5)
    shape = shapes.add_shape(
        MSO_SHAPE_TYPE.AUTO_SHAPE, left, top, width, height
    )
    shape.text = 'In Progress|On Hold|Done'
    for paragraph in shape.text_frame.paragraphs:
        paragraph.font.size = Pt(10)
    print(dir(shape.text_frame.paragraphs))
    slide = prs.slides.add_slide(prs.slide_layouts[5])

    # define chart data ---------------------
    chart_data = CategoryChartData()
    chart_data.categories = ['East']
    chart_data.add_series('Series 1', (19.2,))
    chart_data.add_series('Series 2', (1.2,))

    # add chart to slide --------------------
    x, y, cx, cy = Inches(2), Inches(2), Inches(6), Inches(4.5)
    slide.shapes.add_chart(
        XL_CHART_TYPE.COLUMN_STACKED, x, y, cx, cy, chart_data
    )

    # save the PowerPoint presentation
    prs.save('e:/vishal/tablechart.pptx')


def test_treemap():
    # create a new PowerPoint presentation object
    prs = Presentation()

    # create a new slide object
    slide = prs.slides.add_slide(prs.slide_layouts[5])

    # define the data for the treemap
    data = CategoryChartData()
    data.categories = ['Category 1', 'Category 2', 'Category 3']
    data.add_series('Series 1', (20, 30, 50))

    # add a chart object to the slide
    print(dir(XL_CHART_TYPE))
    chart = slide.shapes.add_chart(
        XL_CHART_TYPE.TREEMAP_FULL, Inches(1), Inches(1), Inches(8), Inches(4.5), data
    ).chart

    # customize the chart as desired
    chart.has_legend = True
    chart.legend.position = XL_LEGEND_POSITION.BOTTOM
    chart.legend.include_in_layout = False
    chart.plots[0].has_data_labels = True
    chart.plots[0].data_labels.font.size = Pt(12)
    # chart.plots[0].data_labels.position = XL_LABEL_POSITION.CENTER
    chart.plots[0].data_labels.font.color.rgb = RGBColor(255, 255, 255)
    chart.plots[0].data_labels.font.bold = True
    chart.plots[0].data_labels.number_format = '0%'
    chart.plots[0].data_labels.format.auto_text_color = False
    chart.plots[0].data_labels.format.font.color.rgb = RGBColor(0, 0, 0)
    chart.plots[0].format.fill.solid()
    chart.plots[0].format.fill.fore_color.rgb = RGBColor(255, 255, 255)
    chart.plots[0].points[0].format.fill.solid()
    chart.plots[0].points[0].format.fill.fore_color.theme_color = MSO_THEME_COLOR_INDEX

    # save the PowerPoint presentation
    prs.save(r'e:\vishal\treemap.pptx')


if __name__ == '__main__':
    create_chart_table()
