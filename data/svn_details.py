import os
import sys
if os.name == "nt":
    sys.path.append('C:\Program Files\Python39\lib\site-packages')
    import pysvn
else:
    import pysvn


def main(server=None, path=None):
    if server is None:
        raise Exception("SVN Server URL is required")
    client = pysvn.Client()
    entries_list = \
        client.list(server,
                    peg_revision=pysvn.Revision(pysvn.opt_revision_kind.head),
                    revision=pysvn.Revision(pysvn.opt_revision_kind.head),
                    dirent_fields=pysvn.SVN_DIRENT_ALL,
                    depth=pysvn.depth.infinity,
                    # patterns=['*.sql']
                    )
    print(entries_list)


if __name__ == '__main__':
    svn_server = 'svn://10.205.10.31'
    path = '/Application Testing/Core_Bankcard_Base_Testing/CBC_Base_CoreIssue/Plat'
    main(server=svn_server, path=path)
