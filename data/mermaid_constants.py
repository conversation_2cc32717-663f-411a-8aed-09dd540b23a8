credit_card_system_context = """
flowchart TB     
    subgraph Credit Card System
        A[Credit Card System] -->|1. Processes Payment| B(Payment Processor)
        A[Credit Card System] -->|2. Authorizes Transactions| C(Acquirer)
        A[Credit Card System] -->|3. Provides Account Information| D(Bank)
    end
    subgraph Payment Processor
        B(Payment Processor) -->|1. Sends Payment Request| E[Payment Gateway]
        B(Payment Processor) -->|2. Verifies Payment| F[Payment Fraud Detection]
    end
    subgraph Acquirer
        C(Acquirer) -->|1. Sends Transaction Request| E[Payment Gateway]
        C(Acquirer) -->|2. Approves Transaction| G[Transaction Authorization]
    end
    subgraph Bank
        D(Bank) -->|1. Sends Account Information Request| H[Account Information Service]
        D(Bank) -->|2. Provides Account Information| H[Account Information Service]
    end
    click A call callback() "Tooltip"
    
"""

credit_card_system_containers = """
flowchart TB
    subgraph Credit Card System
        A[Credit Card System] -->|1. Processes Payment| B(Payment Processor)
        A[Credit Card System] -->|2. Authorizes Transactions| C(Acquirer)
        A[Credit Card System] -->|3. Provides Account Information| D(Bank)
    end
    subgraph Payment Processor
        B(Payment Processor) -->|1. Sends Payment Request| E[Payment Gateway]
        B(Payment Processor) -->|2. Verifies Payment| F[Payment Fraud Detection]
    end
    subgraph Acquirer
        C(Acquirer) -->|1. Sends Transaction Request| E[Payment Gateway]
        C(Acquirer) -->|2. Approves Transaction| G[Transaction Authorization]
    end
    subgraph Bank
        D(Bank) -->|1. Sends Account Information Request| H[Account Information Service]
        D(Bank) -->|2. Provides Account Information| H[Account Information Service]
    end
    
"""


credit_card_services_systems = """
        graph TD
  subgraph "Credit Card Industry"
    A[Cardholder] --> B[Merchant]
    B --> C[Payment Gateway]
    C --> D[Acquirer]
    D --> E[Processor]
    E --> F[Card Network]
    F --> G[Issuer]
    subgraph "Supporting Services"
      H[Customer Service]
      I[Fraud Detection]
      J[Credit Scoring]
    end
  end
  H --> A
  H --> B
  I --> C
  I --> D
  J --> G
        """

container_issuer_sub_functions = """
  graph LR
  A[Issuer] --> B[Authorization]
  A --> C[Card Management]
  A --> D[Transaction Processing]
  A --> E[Customer Service]
  B --> F[Rules Engine]
  B --> G[Fraud Detection]
  C --> H[Card Issuance]
  C --> I[Credit Limit Management]
  C --> J[Card Activation]
  D --> K[Transaction Settlement]
  D --> L[Chargeback Management]
  E --> M[Dispute Resolution]
  E --> N[Lost/Stolen Card Replacement]

"""

container_issuer_card_management = """
graph LR
A[Card Management] --> B[Card Issuance]
A --> C[Credit Limit Management]
A --> D[Card Activation]
B --> E[Card Personalization]
B --> F[Card Printing]
B --> G[Card Embossing]
C --> H[Credit Scoring System]
C --> I[Credit Limit Adjustment]
D --> J[Card Verification]
"""

container_issuer_authorization = """
graph LR
A[Authorization] --> B[Transaction Routing]
A --> C[Transaction Processing]
A --> D[Transaction Authorization]
B --> E[Transaction Gateway]
B --> F[Transaction Switch]
D --> G[Fraud Detection]
D --> H[Risk Management]
"""

c4_contenxt_issuer_authorization = """
graph LR
subgraph "E-commerce System"
    A[Authorization] --> B[Transaction Routing]
    A --> C[Transaction Processing]
    A --> D[Transaction Authorization]
end
subgraph "Payment Gateway"
    B --> E[Transaction Gateway]
    B --> F[Transaction Switch]
end
subgraph "Risk Management"
    D --> G[Fraud Detection]
    D --> H[Risk Management]
end

"""

container_issuer_transaction_processing = """
graph LR
A[Transaction Processing] --> B[Transaction Validation]
A --> C[Transaction Routing]
A --> D[Transaction Settlement]
B --> E[Transaction Authorization]
B --> F[Transaction Limits Check]
C --> G[Transaction Gateway]
C --> H[Transaction Switch]
D --> I[Transaction Clearing]
D --> J[Transaction Reconciliation]
"""

container_issuer_customer_service = """
graph LR
A[Customer Service] --> B[Cardholder Support]
A --> C[Dispute Management]
A --> D[Lost/Stolen Card Management]
B --> E[Customer Call Center]
B --> F[Online Support]
B --> G[Chat Support]
C --> H[Dispute Resolution]
C --> I[Chargeback Management]
D --> J[Card Replacement]
D --> K[Card Blocking/Unblocking]
"""

container_card_network = """
graph LR
A[Card Network] --> B[Transaction Processing]
A --> C[Authorization]
A --> D[Clearing and Settlement]
B --> E[Transaction Validation]
B --> F[Transaction Routing]
B --> G[Transaction Settlement]
C --> H[Card Authentication]
C --> I[Fraud Detection]
C --> J[Credit Limit Management]
D --> K[Clearing]
D --> L[Settlement]
"""

container_card_network_transaction_processing = """
graph LR
A[Transaction Processing] --> B[Transaction Validation]
A --> C[Transaction Routing]
A --> D[Transaction Settlement]
B --> E[Card Verification]
B --> F[Transaction Amount Verification]
C --> G[Issuer Network Selection]
C --> H[Payment Gateway Integration]
D --> I[Payment Processing]
D --> J[Transaction Reconciliation]
"""

container_card_network_authorization = """
graph LR
A[Authorization] --> B[Card Authentication]
A --> C[Fraud Detection]
A --> D[Credit Limit Management]
B --> E[Card Verification]
B --> F[Card Status Check]
C --> G[Transaction Risk Analysis]
D --> H[Credit Limit Check]
D --> I[Authorization Decision]
"""

container_card_network_clearing_and_settlement = """
graph LR
A[Clearing and Settlement] --> B[Clearing]
A --> C[Settlement]
B --> D[Transaction Aggregation]
B --> E[Transaction Reconciliation]
C --> F[Payment Reconciliation]
C --> G[Payment Processing]
"""

container_card_network_combined = """
flowchart LR

subgraph Card Network
A[Card Network] --> B[Transaction Processing]
A --> C[Authorization]
A --> D[Clearing and Settlement]
end

subgraph Transaction Processing
B --> E[Transaction Validation]
B --> F[Transaction Routing]
B --> G[Transaction Settlement]
E --> E1[Card Verification]
E --> E2[Transaction Amount Verification]
F --> F1[Issuer Network Selection]
F --> F2[Payment Gateway Integration]
G --> G1[Payment Processing]
G --> G2[Transaction Reconciliation]
end

subgraph Authorization
C --> H[Card Authentication]
C --> I[Fraud Detection]
C --> J[Credit Limit Management]
H --> H1[Card Verification]
H --> H2[Card Status Check]
I --> I1[Transaction Risk Analysis]
J --> J1[Credit Limit Check]
J --> J2[Authorization Decision]
end

subgraph Clearing and Settlement
D --> K[Clearing]
D --> L[Settlement]
K --> M[Transaction Aggregation]
K --> N[Transaction Reconciliation]
L --> O[Payment Reconciliation]
L --> P[Payment Processing]
end

A -.-> B
A -.-> C
A -.-> D

B -.-> E
B -.-> F
B -.-> G

C -.-> H
C -.-> I
C -.-> J

D -.-> K
D -.-> L

E -.-> E1
E -.-> E2

F -.-> F1
F -.-> F2

G -.-> G1
G -.-> G2

H -.-> H1
H -.-> H2

I -.-> I1

J -.-> J1
J -.-> J2

K -.-> M
K -.-> N

L -.-> O
L -.-> P
"""

container_processor = """
graph LR
A[Processor] --> B[Transaction Processing]
A --> C[Authorization]
A --> D[Clearing and Settlement]
B --> E[Transaction Validation]
B --> F[Transaction Routing]
B --> G[Transaction Settlement]
C --> H[Card Authentication]
C --> I[Fraud Detection]
C --> J[Credit Limit Management]
D --> K[Clearing]
D --> L[Settlement]
"""

container_acquirer = """
graph LR
A[Acquirer] --> B[Transaction Processing]
A --> C[Authorization]
A --> D[Clearing and Settlement]
B --> E[Transaction Validation]
B --> F[Transaction Routing]
B --> G[Transaction Settlement]
C --> H[Card Authentication]
C --> I[Fraud Detection]
C --> J[Credit Limit Management]
D --> K[Clearing]
D --> L[Settlement]
"""

container_gateway = """
  graph TD
    subgraph "Payment Gateway"
      A[Web Server] --> B[Load Balancer]
      B --> C[API Gateway]
      C --> D[Transaction Management]
      C --> E[Security]
      C --> F[Reporting]
    end
    subgraph "External Services"
      G[Acquirer]
      H[Card Network]
    end
    B --> G
    C --> H
"""

container_merchant = """
  graph TD
    subgraph "Merchant"
      A[Web Storefront] -->|submits order request to| B[Payment Gateway]
    end
    subgraph "Payment Gateway"
      B --> C[API Gateway]
      C --> D[Transaction Processing]
      C --> E[Security]
      C --> F[Reporting and Analytics]
    end
    subgraph "Acquirer"
      G[Transaction Acquiring]
    end
    subgraph "Card Network"
      H[Authorization and Clearing]
    end
    D --> G
    H --> D
    style A fill:#D9E5FF,stroke:#333,stroke-width:2px
    style B fill:#FFF5CC,stroke:#333,stroke-width:2px
    style C fill:#B2D8B2,stroke:#333,stroke-width:2px
    style D fill:#A2B9BC,stroke:#333,stroke-width:2px
    style E fill:#FFE5B4,stroke:#333,stroke-width:2px
    style F fill:#BFBFBF,stroke:#333,stroke-width:2px
    style G fill:#C5A5C5,stroke:#333,stroke-width:2px
    style H fill:#8EA1C5,stroke:#333,stroke-width:2px
"""

container_cardholder = """
  graph TD
    subgraph "Cardholder"
      A[Physical Card] --> B[Magnetic Stripe/EMV Chip]
      B --> C[Card Network Authorization Request]
      C --> D[Card Issuer Authorization Response]
      C --> E[Fraud Detection]
      B --> F[Tokenization]
      F --> G[Secure Element/NFC Chip]
      G --> H[Mobile Wallet]
    end
    subgraph "External Services"
      I[Merchant]
      J[Acquirer]
      K[Card Network]
    end
    C --> I
    J --> K
"""

container_cardholder_digital = """
  graph TD
    subgraph "Cardholder"
      A[Physical Card] --> B[Card Activation]
      A --> C[Card Replacement]
      A --> D[Transaction History]
      A --> E[Dispute Management]
      F[Digital & Virtual Card] --> G[Card Activation]
      F --> H[Transaction History]
      F --> I[Dispute Management]
    end
  """

container_mobile_wallet = """
  graph LR
    subgraph "Mobile Wallet"
      A[User Interface] --> B[Mobile Device API]
      B --> C[Authentication]
      B --> D[Tokenization]
      B --> E[Transaction Processing]
      B --> F[Notifications]
    end
    subgraph "External Services"
      G[Card Network]
      H[Banking Services]
      I[Payment Gateway]
      J[Merchant]
    end
    E --> G
    E --> H
    E --> I
    E --> J
"""

component_mobile_wallet = """
  graph TD
    subgraph "Mobile Wallet"
      A[Payment Card Manager] -->|manages| B[Virtual Card Manager]
      A -->|manages| C[Mobile Payment Manager]
      C -->|interacts with| D[Payment Gateway]
      C -->|interacts with| E[Acquirer]
      C -->|interacts with| F[Issuer]
      C -->|interacts with| G[Banking Network]
      C -->|interacts with| H[Secure Element]
      C -->|interacts with| I[Location Services]
      C -->|interacts with| J[Notification Service]
      B -->|provides| K[Virtual Payment Card]
      H -->|stores| L[Card Data]
      L -->|accesses| M[Tokenization Service]
      F -->|provides| N[Payment Authorization]
      G -->|provides| N
      subgraph "User Interface"
        O[Account Settings]
        P[Payment History]
        Q[Payment Card List]
        R[Add Payment Card]
        S[Edit Payment Card]
        T[Payment Amount]
        U[Payment Confirmation]
      end
      A -->|uses| O
      A -->|uses| P
      A -->|uses| Q
      A -->|uses| R
      A -->|uses| T
      A -->|uses| U
    end
"""

container_credit_scoring = """
  graph TD
    subgraph "Credit Scoring"
      A[Data Input] -->|Raw Data| B(Data Preparation)
      B -->|Prepared Data| C(Model Training)
      C -->|Scoring Model| D(Scorecard)
      D -->|Credit Score| E[Credit Decision Engine]
    end
    subgraph "External Services"
      F[Credit Bureau Data]
      G[Model Validation Data]
    end
    F --> B
    G --> C
    E --> F
    E --> G
    E --> D
"""

components_credit_scoring = """
graph TD
    A[Credit Scoring] --> B[Credit Score Calculation]
    A --> C[Credit History]
    A --> D[Public Records]
    A --> E[Credit Bureau Data]
    B --> F[Payment History]
    B --> G[Credit Utilization]
    B --> H[Length of Credit History]
    B --> I[Recent Credit Inquiries]
    C --> J[Credit Report]
    C --> K[Credit Score Factors]
    D --> L[Bankruptcies]
    D --> M[Tax Liens]
    D --> N[Judgments]
    E --> O[Credit Accounts]
    E --> P[Credit Inquiries]
    E --> Q[Personal Information]
"""

container_credit_scoring_data_sources = """
  graph TD
  subgraph "Data Sources"
    A[Bank Data]
    B[Credit Bureau Data]
    C[Alternative Data]
  end
  subgraph "Credit Scoring Engine"
    D[Data Processing]
    E[Credit Scoring Model]
    F[Credit Score]
  end
  A --> D
  B --> D
  C --> D
  D --> E
  E --> F
"""

container_credit_scoring_decision_engine = """
  graph TD
  subgraph "Decision Engine"
    A[Rules Engine]
    B[Machine Learning Models]
  end
  subgraph "Credit Scoring Engine"
    C[Credit Score]
  end
  A --> C
  B --> C
"""
