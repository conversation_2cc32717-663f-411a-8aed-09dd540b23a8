import datetime
import sys
import errno
from urllib.parse import urlparse, parse_qs

from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
# from webdrivermanager import ChromeDriverManager
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, NoSuchElementException, TimeoutException

from pykeepass import PyKeePass
import pandas as pd

import os
import time
import weakref

try:
    from helper import InvalidFileExtension
except ModuleNotFoundError as e:
    from .helper import InvalidFileExtension

from data.custom_logger import MyLogger
from .custom_exception import CtLoginFailed


class CoreTrackAutomation:
    def __init__(self, user: str, passwd: str, prod: bool = True, headless=True):
        self.driver = None
        self.df = None
        self.time_taken = None
        self.user = user
        self.passwd = passwd
        self.headless = headless
        self.url = "https://coretrack.corecard.com" if prod else "http://************:81"
        # Reference: https://docs.python.org/3.6/library/weakref.html
        self._finalizer = weakref.finalize(self, self.remove)
        self.my_logger = MyLogger()

    def remove(self):
        self.driver.close()
        self._finalizer()

    @property
    def removed(self):
        return not self._finalizer.alive

    def init(self):
        options = Options()
        options.headless = self.headless
        # options.add_argument("--log-level=3")
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    def login(self):
        try:
            self.driver.get(f"{self.url}")

        except WebDriverException as err:
            # self.my_logger.error(f"Check VPN Connection: {err.msg}")
            raise err
        username = self.driver.find_element(by=By.NAME, value='user')
        username.send_keys(self.user)
        self.driver.find_element(by=By.NAME, value='pw').send_keys(self.passwd)
        login = self.driver.find_element(By.NAME, value='ctl01')
        login.click()
        if self.driver.current_url != f"{self.url}/bugs.aspx":
            raise CtLoginFailed

    def create_ct(self):
        pass

    def search_description_ct(self, path: str, search_string: str) -> bool:
        self.driver.get(f"{self.url}/{path}")
        element = self.driver.find_element(By.ID, value='like')
        element.send_keys(search_string)
        search = self.driver.find_element(By.NAME, value='ctl01')
        search.click()
        return bool(self.driver.find_elements(by=By.ID, value="massform"))

    def __enter__(self):
        self.start_perf_counter_ns = time.perf_counter_ns()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.time_taken = str(datetime.timedelta(
            seconds=(time.perf_counter_ns() - self.start_perf_counter_ns) * pow(10, -9)))
        self.my_logger.info(self.time_taken)


def main(filename: str) -> pd.DataFrame:
    try:
        df = pd.DataFrame(None)
        if 'xlsx' in filename:
            df = pd.read_excel(filename, engine='openpyxl')
        elif 'xls' in filename:
            df = pd.read_excel(filename, engine='xlrd')
        elif 'ods' in filename:
            df = pd.read_excel(filename, engine='odf')
        else:
            raise InvalidFileExtension("File with unsupported extension uploaded")
        df['CoreTrack Id'] = ""
        return df
    except InvalidFileExtension as e:
        print("File with unsupported extension uploaded")
        sys.exit(errno.EPERM)


def createDriverInstance(head_less: bool = True) -> webdriver.Chrome:
    """
    Create the driver instance
    Args:
        env: point to production or test env
        head_less: Run the test in headless mode

    Returns:
        webdriver.Chrome driver instance

    """
    options = Options()
    options.headless = head_less
    options.add_argument("--log-level=3")

    # Installs required chromium version
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    return driver


def search_ct(user: str, passwd: str, row, env: str = "prod", head_less: bool = True):
    if env == "prod":
        url = "https://coretrack.corecard.com"
    else:
        url = "http://************:81"

    driver = createDriverInstance(head_less)
    try:
        driver.get(f"{url}/search.aspx")
    except WebDriverException as e:
        print(e)
        sys.exit(errno.ETIMEDOUT)
    username = driver.find_element(by=By.NAME, value='user')
    username.send_keys(user)
    driver.find_element(by=By.NAME, value='pw').send_keys(passwd)
    login = driver.find_element(By.NAME, value='ctl01')
    login.click()


def create_ct_single(user: str, passwd: str, row, env: str = "prod") -> str:
    my_logger = MyLogger()
    my_logger.info(f"Processing record: {row}")
    # Set Chrome Webdriver options
    options = Options()
    options.headless = True
    options.add_argument("--log-level=3")
    if env == "prod":
        url = "https://coretrack.corecard.com"
    else:
        url = "http://************:81"
        # Installs required chromium version
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    column_name_id_mapping = dict(
        Summary='short_desc', Project='project', Organization='org',
        Category='category', Priority='priority', Environment='Environment',
        AssignedTo='assigned_to', Description='cke_contents_comment', Attachment='attached_file'
    )
    # Open the URL and login with supplied credentials

    try:
        driver.get(f"{url}/edit_bug.aspx")
    except WebDriverException as e:
        my_logger.info(f'Unable to connect to: {url}; {errno.ETIMEDOUT}')
        my_logger.info(e.msg)
        sys.exit(errno.ETIMEDOUT)
    username = driver.find_element(by=By.NAME, value='user')
    username.send_keys(user)
    driver.find_element(by=By.NAME, value='pw').send_keys(passwd)
    login = driver.find_element(By.NAME, value='ctl01')
    login.click()

    for key, value in column_name_id_mapping.items():
        if hasattr(row, key):
            if key == "Description":
                # driver.find_element(By.CSS_SELECTOR, "body.cke_show_borders:nth-child(2) > p:nth-child(1)").send_keys(row[df.columns.get_loc(key)])
                try:
                    driver.switch_to.frame(driver.find_element(By.TAG_NAME, 'iframe'))
                    elem = driver.find_element(By.CSS_SELECTOR, "body")
                    elem.send_keys(getattr(row, key))
                    driver.switch_to.default_content()
                except NoSuchElementException as e:
                    my_logger.info(f"Element {key} Not Found")
                    time.sleep(5)
                    continue
            elif key in ["Attachment"]:
                continue
            else:
                element = driver.find_element(By.ID, value=value)
                if element.tag_name == "input":
                    element.send_keys(getattr(row, key))
                elif element.tag_name == "select":
                    # Select(element).select_by_visible_text(row[df.columns.get_loc(key)])
                    Select(element).select_by_visible_text(getattr(row, key))

    # Create CT
    driver.find_element(By.NAME, value='sub').click()
    ct_number = str.split(driver.current_url, '=')[1]
    driver.close()
    return ct_number


def create_ct(user: str, passwd: str, df: pd.DataFrame, mode: str = "prod"):
    my_logger = MyLogger()
    my_logger.info("Started CT creation process")
    # Set Chrome Webdriver options
    options = Options()
    options.headless = True
    options.add_argument("--log-level=3")
    if mode == "prod":
        url = "https://coretrack.corecard.com"
    else:
        url = "http://************:81"

    # Installs required chromium version
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

    column_name_id_mapping = dict(
        Summary='short_desc', Project='project', Organization='org',
        Category='category', Priority='priority', Environment='Environment',
        AssignedTo='assigned_to', Description='cke_contents_comment', Attachment='attached_file'
    )

    # if all(item in df.columns for item in ['Summary', 'Description', 'Project', 'Organization', 'Category', 'Priority', 'Assigned To',
    #     'Environment']):
    #     print("All Good")
    # else:
    #     print("Columns missing")
    #
    # if {'Summary', 'Description', 'Project', 'Organization', 'Category', 'Priority', 'Assigned To',
    #     'Environment'}.issubset(df.columns):
    #     print("All columns found")
    # else:
    #     print("Columns missing")

    for counter, row in enumerate(df.itertuples(index=True), 0):
        # print(counter, row)
        # print(row[df.columns.get_loc("Project")])
        # print(getattr(row, "Project"))

        if counter == 0:
            try:
                driver.get(f"{url}/edit_bug.aspx")
            except WebDriverException as e:
                my_logger.info(f'Unable to connect to: {url}; {errno.ETIMEDOUT}')
                my_logger.info(e.msg)
                sys.exit(errno.ETIMEDOUT)
            username = driver.find_element(by=By.NAME, value='user')
            username.send_keys(user)
            driver.find_element(by=By.NAME, value='pw').send_keys(passwd)
            login = driver.find_element(By.NAME, value='ctl01')
            login.click()
        else:
            driver.execute_script("window.open();")
            driver.switch_to.window(driver.window_handles[-1])
            try:
                driver.get(f"{url}/edit_bug.aspx")
            except WebDriverException as e:
                my_logger.info(f'Unable to connect to: {url}; {errno.ETIMEDOUT}')
                my_logger.info(e.msg)
                sys.exit(errno.ETIMEDOUT)

        for key, value in column_name_id_mapping.items():
            if hasattr(row, key):
                if key == "Description":
                    # driver.find_element(By.CSS_SELECTOR, "body.cke_show_borders:nth-child(2) > p:nth-child(1)").send_keys(row[df.columns.get_loc(key)])
                    try:
                        driver.switch_to.frame(driver.find_element(By.TAG_NAME, 'iframe'))
                        elem = driver.find_element(By.CSS_SELECTOR, "body")
                        elem.send_keys(getattr(row, key))
                        driver.switch_to.default_content()
                    except NoSuchElementException as e:
                        my_logger.info(f"Element {key} Not Found")
                        time.sleep(5)
                        continue
                elif key in ["Attachment"]:
                    continue
                else:
                    element = driver.find_element(By.ID, value=value)
                    if element.tag_name == "input":
                        element.send_keys(getattr(row, key))
                    elif element.tag_name == "select":
                        # Select(element).select_by_visible_text(row[df.columns.get_loc(key)])
                        Select(element).select_by_visible_text(getattr(row, key))

        # Create CT
        driver.find_element(By.NAME, value='sub').click()
        ct_number = str.split(driver.current_url, '=')[1]
        df.at[getattr(row, "Index"), 'CoreTrack Id'] = ct_number

    return df


def open_in_new_window(url: str) -> dict:
    my_logger = MyLogger().get_logger()
    service = ChromeService(executable_path=ChromeDriverManager().install())
    service.start()
    options = Options()
    driver = webdriver.Remote(service.service_url, options=options)
    my_logger.debug("Driver initialized")
    try:
        my_logger.debug(url)
        driver.get(url)
        wait = WebDriverWait(driver, 60).until(EC.visibility_of_element_located((By.ID, 'id-get-a-token')))
    except TimeoutException as time_exception:
        my_logger.info("Timed out")
    finally:
        redirect_url = driver.current_url
        parsed_url = urlparse(driver.current_url)
        auth_response = parse_qs(parsed_url.query)
        authorization_code = redirect_url.split("code=")[1]
        my_logger.debug(f"Authorization code: {authorization_code}")
        service.stop()
        return auth_response


def search_rt():
    pass


if __name__ == "__main__":
    search_rt()
    if os.name == "nt":
        keypass_location = os.getenv('KeyPass', "E:/KeyPass")
        kp_file_path = os.path.join(keypass_location, "Database.kdbx")
        kp_key_path = os.path.join(keypass_location, "Database.key")
        kp = PyKeePass(kp_file_path, keyfile=kp_key_path)
        entry = kp.find_entries(title='CoreTrack Test', first=True)
        _file = "e:/vishal/CoreTrack/SampleData.xlsx"
        user = entry.username
        passwd = entry.password
    else:
        _file = '/tmp/SampleData.xlsx'
        user = os.getenv("USER")
        passwd = os.getenv("PASSWD")

    ct = CoreTrackAutomation(user, passwd, False, False)
    ct.init()
    ct.login()
    ct.search_description_ct("search.aspx", "VISHAL")
    print(ct.removed)

    # with CoreTrackAutomation(user, passwd, False, False) as ct:
    #     ct.init()
    #     ct.login()
    #     ct.search_description_ct()

    hold = input("press enter to close")

    # for row in dff.itertuples():
    #     print(row)
    #     ct_no = create_ct_single(user, passwd, row, env="test")
    #     print(f'{ct_no}: {type(ct_no)}')
    # create_ct(entry.username, entry.password, dff, mode="test")
