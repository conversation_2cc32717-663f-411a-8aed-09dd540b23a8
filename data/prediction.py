import datetime

import data.get_from_db as db
from prophet import Prophet
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error
import pandas as pd
import numpy as np
from plotly.subplots import make_subplots
import plotly.graph_objs as go
import plotly.express as px
from prophet.plot import plot_plotly, plot_components_plotly, add_changepoints_to_plot


def data_prediction():
    # Source: https://stackoverflow.com/questions/25628496/getting-wider-output-in-pycharms-built-in-console
    # Source: https://intellij-support.jetbrains.com/hc/en-us/community/posts/*********-How-to-see-more-than-five-columns-of-a-data-frame-output-in-PyCharm-run-window
    desired_width = 320
    pd.set_option('display.width', desired_width)
    np.set_printoptions(linewidth=desired_width)
    pd.set_option('display.max_columns', 20)

    df_user = db.get_user_detail()
    dff = db.get_worklog_details_for_projection('plat')

    mapping = dict(df_user[['accountId', 'displayName']].values)
    dff['displayName'] = dff.author.map(mapping)
    dff = dff[dff['classification'] == 'Enhancement'].copy(deep=True)
    dff.to_csv("e:/vishal/actual.csv")
    loop = 1
    df_merge = None

    for author in dff['author'].unique():
        loop += 1
        # if loop > 10:
        #     break
        # 5fc95db9aca10c0069d9304c  Rohit Soni
        # 5fc9639efacfd600764ccb57  Vinod Prajapati
        # 5fc95c2478f01800768c7391  Ashish Saxena
        df = dff[dff['author'] == author][['ds', 'y']]

        # df_analaysis = pd.get_dummies(df_analaysis, columns=['classification'])
        # x_cols = [x for x in df_analaysis.columns if x not in ['ds', 'y']]


        # for col in x_cols:
        # Split the data into training and validation set
        print(f'Size of dataframe: {df.shape[0]}')
        if df.shape[0] < 10:
            continue
        train, test = train_test_split(df, test_size=0.2,)

        train = train.sort_values('ds')
        test = test.sort_values('ds')

        m = Prophet().fit(df)
        prediction = m.predict(train)
        y_actual = train['y']
        y_predicted = prediction['yhat']
        y_predicted = y_predicted.astype(int)
        print(f'{mean_absolute_error(y_actual, y_predicted)}')

        # Interface phase: predict future values
        future = m.make_future_dataframe(periods=90)
        future = future[(future['ds'].dt.dayofweek < 5)]

        df_collate = future[future['ds'].dt.date >= datetime.date.today()]

        forecast = m.predict(future)
        forecast = forecast[forecast['ds'].dt.date >= datetime.date.today()][['ds', 'yhat']]

        if forecast.shape[0] == 0:
            df_collate['yhat'] = None
        else:
            df_collate = pd.merge(df_collate, forecast, on='ds')

            # plot_plotly(m, forecast  ).show()
            # plot_components_plotly(m, forecast).show()
        df_collate['author'] = author
        if df_collate.shape[0] > 0:
            if df_merge is None:
                df_merge = df_collate.copy()
            else:
                df_merge = pd.concat([df_merge, df_collate], axis=0)
            print(f'shape of merge = {df_merge.shape[0]}')
    df_merge['displayName'] = df_merge.author.map(mapping)
    df_merge.to_csv("e:/vishal/collate.csv")
    exit()

    # df_analaysis.drop(columns=
    # [
    #     'classification_Bug', 'classification_Env Support', 'classification_Meetings',
    #     'classification_NBL', 'classification_PS Support'
    # ], inplace=True
    # )

    # df_analaysis = df_analaysis[df_analaysis['classification_Enhancement'] == 1].copy(deep=True)
    train, test = train_test_split(df_analaysis, test_size=0.2, random_state=10)
    train.to_csv("e:/vishal/train.csv")
    test.to_csv("e:/vishal/test.csv")
    m = Prophet()
    for i in df_analaysis.columns:
        if i in ['y', 'ds']:
            continue
        else:
            m.add_regressor(i)
    m.fit(train)

    # Model evaluation


    # fig.show()

    future = m.make_future_dataframe(periods=60)
    future = future[(future['ds'].dt.dayofweek < 5)]

    for i in test.columns:
        if i in ['y', 'ds']:
            continue
        else:
            future[i] = 1

    forecast = m.predict(future)
    forecast.to_csv("e:/vishal/forecast.csv")


if __name__ == '__main__':
    data_prediction()
