"""
Module to define custom loggers used in the project
"""
from __future__ import annotations

import errno
import json
import logging
import os
import sys

from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
import re
import pytz
from functools import WRAPPER_ASSIGNMENTS, wraps
from dependency_injector import containers, providers


class ConfigContainer(containers.DeclarativeContainer):
    config = providers.Configuration()


config_container = ConfigContainer()
# config_container.config.from_yaml('config.yml')
config_container.config.logname.from_value('dash')
config_container.config.use_formatter.from_value('Yes')
config_container.config.log_level.from_value(logging.DEBUG)
config_container.config.propagate.from_value(True)
config_container.config.log_directory.from_value(r"c:\vishal\log\dashboard" if os.name == 'nt' else '/var/log/dashboard')


def pop_handlers(func):
    """
    This will be used a decorator
    # to remove existing FileHandlers
    # Add another Filehandler
    Args:
        func:
    Returns:

    """

    def wrapper(*args, **kwargs):
        logger = args[0]

        handlers = logger._logger.handlers.copy()
        for handler in handlers:
            if isinstance(handler, logging.FileHandler) and handler.baseFilename.endswith('.json'):
                logger._logger.removeHandler(handler)
        result = func(*args, **kwargs)
        for handler in handlers:
            logger._logger.addHandler(handler)
        return result

    return wrapper


def replace_handlers(custom_handler):
    def decorator(func):
        attributes = WRAPPER_ASSIGNMENTS

        @wraps(func, assigned=attributes)
        def wrapper(*args, **kwargs):
            logger = args[0]
            handlers = logger._logger.handlers.copy()
            logger._logger.handlers = [custom_handler]
            # logger._logger.addHandler(json_handler)
            result = func(*args, **kwargs)
            logger._logger.handlers = handlers
            return result

        return wrapper

    return decorator


def mask_string(s, mask_char='*', num_masked_chars=6):
    if len(s) > num_masked_chars:
        return s[:len(s) - num_masked_chars // 2] + mask_char * num_masked_chars + s[num_masked_chars // 2:]
    else:
        return mask_char * len(s)


class LogUserLogin(logging.Formatter):
    def format(self, record) -> str:
        message_dict = {}
        extra_dict = record.__dict__.get('extra', None)
        if extra_dict:
            message_dict.update(extra_dict)

        return json.dumps(message_dict)


class LogQueryExplainPlan(logging.Formatter):
    def format(self, record):
        message_dict = {
            # "time": self.formatTime(record, self.datefmt),
            # "name": record.name,
            # "level": record.levelname,
            # "message": record.getMessage()
        }
        # if record.exc_info:
        #     message_dict["exc_info"] = self.formatException(record.exc_info)
        # if record.stack_info:
        #     message_dict["stack_info"] = self.formatStack(record.stack_info)

        extra_dict = record.__dict__.get('extra', None)
        if extra_dict:
            message_dict.update(extra_dict)

        return json.dumps(message_dict)


class MaskMasterKeyFilter(logging.Filter):
    def filter(self, record):
        if record.msg and 'MASTER_KEYFILE' in record.msg:
            # Replace the value of MASTER_KEYFILE with '********'
            record.msg = record.msg.replace(os.environ.get('MASTER_KEYFILE'), '********')
        return True


class MaskingFilter(logging.Filter):
    """
    A filter to mask sensitive information in log messages.
    """
    MASK = "********"

    def filter(self, record):
        try:
            if record.args:
                message = record.msg.format(record.args)
            else:
                message = record.getMessage()
        except TypeError as e:
            raise e
        if isinstance(message, str):
            message = re.sub(r"\b\d{3}-\d{2}-\d{4}\b", self.MASK, message)  # mask SSN
            message = re.sub(r"\b\d{16}\b", self.MASK, message)  # mask credit card numbers
            # add more regex patterns to mask other sensitive information as needed

            record.msg = message

        return True


def mask_sensitive_info(record):
    sensitive_fields = ['password', 'api_key']
    for field in sensitive_fields:
        if hasattr(record, 'msg'):
            record.msg = re.sub(rf"{field}=[\w.-]+", f"{field}=<masked>", record.msg)
    return True


json_handler = logging.FileHandler(
    os.path.join(
        r'c:\vishal\log\dashboard' if os.name == 'nt' else '/var/log/dashboard', f'explain_plan.json'
    )
)
json_handler.setLevel(logging.DEBUG)
json_handler.setFormatter(LogQueryExplainPlan())

login_handler = logging.FileHandler(
    os.path.join(
        r'c:\vishal\log\dashboard' if os.name == 'nt' else '/var/log/dashboard', f'login.json'
    )
)
login_handler.setLevel(logging.DEBUG)
login_handler.setFormatter(LogUserLogin())


class MyLogger:
    """
    A logging utility class that provides a logger object with customizable log levels, formatters, and handlers.

    Attributes:
        _logger (logging.Logger): The logger object used for logging messages.

    Methods:
        log(level, msg, *args, **kwargs): Logs a message with the specified log level.
        debug(msg, *args, **kwargs): Logs a message with log level DEBUG.
        info(msg, *args, **kwargs): Logs a message with log level INFO.
        warning(msg, *args, **kwargs): Logs a message with log level WARNING.
        error(msg, *args, **kwargs): Logs a message with log level ERROR.
        critical(msg, *args, **kwargs): Logs a message with log level CRITICAL.
        exception(msg, *args, **kwargs): Logs an exception message with log level ERROR.
        loglevel: A property that gets the current log level of the logger.
        loglevel.setter: A property setter that sets the log level of the logger.
    """
    _logger = None

    # Reading link
    # Read below link for interesting ideas to log data in json format
    # https://www.datadoghq.com/blog/python-logging-best-practices/#digging-deeper-into-pythons-logging-library

    # def __new__(cls, logname=__name__, use_formatter=True, *args, **kwargs):
    #     if cls._logger is None:
    #         cls._logger = super(MyLogger, cls).__new__(cls, *args, **kwargs)
    #         cls._logger = logging.getLogger(logname)
    #         cls._logger.setLevel(logging.DEBUG)
    #         cls._logger.propagate = True
    #         # dirname = "./log"
    #         dirname = 'e:/vishal/log/dashboard' if os.name == 'nt' else '/var/log/dashboard'
    #
    #         if not os.path.isdir(dirname):
    #             os.mkdir(dirname)
    #
    #         cls._logger.addHandler(get_file_handler(f'{dirname}/{logname}.log', use_formatter))
    #         # cls._logger.addHandler(get_json_handler(f'{dirname}/{logname}.json'))
    #
    #         cls._logger.addHandler(get_console_handler(use_formatter))
    #         # cls._logger.addHandler(set_json_formatter(f'{dirname}/{logname}.json', use_formatter))
    #
    #     return cls._logger

    def __init__(self, config=config_container.config()):
        # config = types.MappingProxyType(
        #     dict(
        #         logname='dash', use_formatter=True, level=logging.DEBUG, propagate=True,
        #         dirname=r"E:\vishal\log\dashboard" if os.name == 'nt' else '/var/log/dashboard',
        #     )
        if not MyLogger._logger:
            logname = config.get('logname', __name__)
            use_formatter = config.get('use_formatter', True)
            level = config.get('level', logging.DEBUG)
            propagate = config.get('propagate', True)
            dirname = config.get('log_directory', os.path.abspath('./log'))

            if not os.path.isdir(dirname):
                os.makedirs(dirname)

            utc_name = datetime.now(tz=pytz.timezone('UTC')).strftime('%Z')
            MyLogger._logger = logging.getLogger(logname)
            MyLogger._logger.setLevel(logging.DEBUG)
            MyLogger._logger.propagate = True

            file_handler = logging.FileHandler(os.path.join(dirname, f'{logname}.log'))
            file_handler.setLevel(level)
            file_handler.addFilter(MaskingFilter())
            file_handler.addFilter(MaskMasterKeyFilter())
            file_handler.addFilter(mask_sensitive_info)
            if use_formatter:
                formatter = logging.Formatter(
                    '%(asctime)s.%(msecs)03d ' + utc_name + ' %(relativeCreated)d|%(filename)s, %(lineno)d| %(levelname)s|'
                                                            'pid: %(process)d | THREAD ID: %(thread)d | %(message)s',
                    "%Y-%m-%d %I:%M",
                )
                file_handler.setFormatter(formatter)

            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)
            console_handler.addFilter(MaskingFilter())
            console_handler.addFilter(mask_sensitive_info)
            if use_formatter:
                console_handler.setFormatter(
                    logging.Formatter(
                        '%(asctime)s - %(filename)s, %(lineno)d - %(name)s - %(levelname)s - %(message)s'))

            self._logger.addHandler(file_handler)
            self._logger.addHandler(json_handler)
            self._logger.addHandler(console_handler)

    def get_logger(self):
        return self._logger

    def custom(self, level, msg, *args, **kwargs):
        extra_params = {}
        if 'user_id' in kwargs:
            extra_params['user_id'] = kwargs['user_id']
        if 'request_id' in kwargs:
            extra_params['request_id'] = kwargs['request_id']
        if extra_params:
            kwargs['extra'] = extra_params

        self._logger.log(level, msg, *args, **kwargs)

    @pop_handlers
    def info(self, msg, *args, **kwargs):
        self._logger.info(msg, *args, **kwargs)

    @replace_handlers(json_handler)
    def log_explain_plan(self, message="", **kwargs):
        self._logger.info(message, extra=kwargs)

    @replace_handlers(login_handler)
    def login_activity(self, message=None, **kwargs):
        kwargs['extra']['login_time'] = datetime.now().isoformat()
        self._logger.info(message, extra=kwargs)

    @property
    def loglevel(self):
        return MyLogger._logger.getEffectiveLevel()

    @loglevel.setter
    def loglevel(self, level):
        MyLogger._logger.setLevel(level)


def get_console_handler(formatter_param: bool = False) -> logging.StreamHandler:
    """
    Function to define parameters for console handle.
    References: https://docs.python.org/2/library/logging.html Section 15.7.7. LogRecord attributes for details
    Args:
        formatter_param: parameter to turn on or off the formatter

    Returns:
        StreamHandler for logging object
    """
    console_handler = logging.StreamHandler(sys.stdout)
    if formatter_param:
        formatter = logging.Formatter(
            "%(asctime)s.%(msecs)03d UTC %(relativeCreated)d| %(filename)s, %(lineno)d: %(levelname)s| %(message)s",
            "%m/%d %I:%M:%S")
        logging.Formatter.converter = custom_time
        console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)
    return console_handler


def custom_time(*args):
    utc_now = datetime.now(tz=pytz.timezone('UTC'))
    ist_now = utc_now.astimezone(pytz.timezone('Asia/Kolkata'))
    return utc_now.timetuple()


def get_file_handler(log_file: str, formatter_param: bool = False, backup_count: int = 3) -> TimedRotatingFileHandler:
    # Check https://stackoverflow.com/questions/17558552/how-do-i-add-custom-field-to-python-log-format-string
    # has interesting ideas on customizing log message
    file_handler = None

    try:
        file_handler = TimedRotatingFileHandler(log_file, when='midnight', backupCount=backup_count)
    except PermissionError as e:
        print("Got Exception")
        print(e.with_traceback(e.__traceback__))
        sys.exit(errno.EPERM)

    if formatter_param:
        utc_name = datetime.now(tz=pytz.timezone('UTC')).strftime('%Z')
        logging.Formatter.converter = custom_time
        # formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s", "%Y-%b-%d %I:%M %p %Z")
        # %(filename)s, %(lineno)d
        formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d ' + utc_name + ' %(relativeCreated)d|%(filename)s, %(lineno)d| %(levelname)s|'
                                                    'pid: %(process)d | THREAD ID: %(thread)d | %(message)s',
            "%Y-%m-%d %I:%M",
        )
        file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    return file_handler


def get_json_handler(log_file: str, backup_count: int = 5) -> TimedRotatingFileHandler:
    file_handler = TimedRotatingFileHandler(log_file, when='W0', backupCount=backup_count, utc=True)
    json_formatter = JsonFormatter(
        {
            "level": "levelname",
            "message": "message",
            "loggerName": "name",
            "processName": "processName",
            "processID": "process",
            "threadName": "threadName",
            "threadID": "thread",
            "timestamp": "asctime",
            "filepath": "pathname",
            "functionname": "funcName",
            "lineno": "lineno"
        }
    )
    # file_handler.setFormatter(json_formatter)
    file_handler.setFormatter(LogQueryExplainPlan)
    return file_handler


class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the LogRecord.

    @param dict fmt_dict: Key: logging format attribute pairs. Defaults to {"message": "message"}.
    @param str time_format: time.strftime() format value. Default: "%Y-%m-%dT%H:%M:%S"
    @param str msec_format: Microsecond formatting. Appended at the end. Default: "%s.%03dZ"
    """

    # Source: https://stackoverflow.com/a/70223539/16073830
    def __init__(self, fmt_dict: dict = {}, time_format: str = "%Y-%m-%dT%H:%M:%S", msec_format: str = "%s.%03dZ"):
        super().__init__()
        self.fmt_dict = fmt_dict if fmt_dict is not None else {"message": "message"}
        self.default_time_format = time_format
        self.default_msec_format = msec_format
        self.datefmt = None

    def usesTime(self) -> bool:
        """
        Overwritten to look for the attribute in the format dict values instead of the fmt value.
        """
        return "asctime" in self.fmt_dict.values()

    def formatMessage(self, record) -> dict:
        """
        Overwritten to return a dictionary of the relevant LogRecord attributes instead of a value.
        KeyError is raised if an unknown attribute is provided in the fmt_dict.
        """
        dict_message = {fmt_key: record.__dict__[fmt_val] for fmt_key, fmt_val in self.fmt_dict.items()}
        for fmt_key, fmt_val in self.fmt_dict.items():
            if fmt_key == "message":
                # print(record.__dict__[fmt_val], type(record.__dict__[fmt_val]))
                if "duration" in record.__dict__[fmt_val]:
                    record_list = eval(record.__dict__[fmt_val])
                    for data in record_list:
                        if isinstance(data, dict):
                            for key, value in data.items():
                                dict_message[key] = value
                            dict_message.pop("message")
                    # record.__dict__[fmt_val] = eval(record.__dict__[fmt_val])
                    # return {fmt_key: list(eval(record.__dict__[fmt_val]))}
        return dict_message

    def format(self, record) -> str:
        """
        Mostly the same as the parent's class method, the difference being that a dict is manipulated and dumped as JSON
        instead of a value.
        """
        record.message = record.getMessage()

        if self.usesTime():
            record.asctime = self.formatTime(record, self.datefmt)

        message_dict = self.formatMessage(record)

        if record.exc_info:
            # Cache the traceback text to avoid converting it multiple times
            # (it's constant anyway)
            if not record.exc_text:
                record.exc_text = self.formatException(record.exc_info)

        if record.exc_text:
            message_dict["exc_info"] = record.exc_text

        if record.stack_info:
            message_dict["stack_info"] = self.formatStack(record.stack_info)

        return json.dumps(message_dict, default=str)


class DashLogger(logging.StreamHandler):
    def __init__(self, stream=None):
        super().__init__(stream=stream)
        self.logs = list()

    def emit(self, record):
        try:
            msg = self.format(record)
            self.logs.append(msg)
            self.logs = self.logs[-1000:]
            self.flush()
        except Exception:
            self.handleError(record)
