from cachetools import cached
from cachetools.keys import hashkey
from flask_caching import Cache

cache = Cache()


def conditional_cache_decorator(func_type, cache, use_memoize=True, timeout=600):
    def decorator(func):
        if func_type == "__main__":
            decorated_func = cached(cache=cache, key=hashkey)(func)
        else:
            if use_memoize:
                decorated_func = cache.memoize(timeout=timeout)(func)
            else:
                decorated_func = cache.cached(timeout=timeout,)(func)

        return decorated_func

    return decorator

