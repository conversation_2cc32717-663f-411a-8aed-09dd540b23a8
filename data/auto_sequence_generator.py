import sqlite3
import threading


class AutoSequenceGeneratorThreaded:
    """
    Thread-safe class for generating auto-incrementing sequence numbers.

    Instances of this class can be used to generate unique sequence numbers at the instance level,
    while the class itself can be used to generate unique sequence numbers at the class level.

    Usage:
        # Create an instance of the class
        generator = AutoSequenceGeneratorThreaded()

        # Generate the next instance number
        instance_number = generator.get_next_instance_number()

        # Generate the next class number
        class_number = AutoSequenceGeneratorThreaded.get_next_class_number()

    Attributes:
        None.

    Methods:
        __init__():
            Initializes a new instance of the generator. Creates a new class counter if this is the first instance, and initializes the instance counter to zero.

        get_next_instance_number() -> int:
            Returns the next unique instance number generated by this instance of the generator.

        get_next_class_number() -> int:
            Returns the next unique class number generated by this class of the generator.

    Thread safety:
        This class is thread-safe and can be used in multi-threaded applications without race conditions.
    """

    _lock = threading.Lock()
    _class_counter = 0

    def __init__(self):
        with AutoSequenceGeneratorThreaded._lock:
            # increment the class-level counter only when a new instance is created

            if not hasattr(self.__class__, '_instance_counter'):
                self.__class__._instance_counter = 0
                AutoSequenceGeneratorThreaded._class_counter += 1

            # set the instance-level counter
            self.__class__._instance_counter += 1
            # self.instance_counter = self.__class__._instance_counter
            self.instance_counter = 1

            # set the class-level counter
            self.class_counter = AutoSequenceGeneratorThreaded._class_counter
        if hasattr(self.__class__, "_instance_counter"):
            print(f'Value = {self.__class__._instance_counter}')
        print(f'Value = {self.__class__._class_counter}')
        print(f'Value instance = {self.instance_counter}')

    def nextval(self):
        with AutoSequenceGeneratorThreaded._lock:
            self.instance_counter += 1
            return self.instance_counter

    def currval(self):
        with AutoSequenceGeneratorThreaded._lock:
            return self.instance_counter

    @classmethod
    def nextval_class(cls):
        with AutoSequenceGeneratorThreaded._lock:
            cls._class_counter += 1

            # return the updated counter value
            return cls._class_counter

    @classmethod
    def currval_class(cls):
        with AutoSequenceGeneratorThreaded._lock:
            return cls._class_counter


class AutoSequenceGeneratorThreadedDb:
    """
    An auto sequence generator that uses a database as a shared resource to ensure unique sequence numbers
    across multiple threads.
    """

    def __init__(self, db_conn):
        """
        Initializes the auto sequence generator with a database connection.

        Args:
            db_conn: A database connection object.
        """
        self._lock = threading.Lock()
        self._db_conn = db_conn

        self._cursor = self._db_conn.cursor()
        self._cursor.execute("CREATE TABLE IF NOT EXISTS sequence (id INTEGER PRIMARY KEY)")
        self._cursor.execute("INSERT OR IGNORE INTO sequence (id) VALUES (0)")
        self._db_conn.commit()

    def get_next_sequence_number(self):
        """
        Gets the next sequence number by incrementing the sequence counter in the database.

        Returns:
            The next sequence number.
        """
        with self._lock:
            self._cursor.execute("UPDATE sequence SET id = id + 1")
            self._db_conn.commit()
            # return self._cursor.lastrowid
            self._cursor.execute("SELECT id FROM sequence")
            next_id = self._cursor.fetchone()[0]
            return next_id


class AutoSequenceGenerator:
    """
    An auto sequence generator that uses a class-level variable and a lock to ensure thread safety.
    """
    _lock = threading.Lock()
    _counter = 0

    @classmethod
    def get_next_sequence_number(cls):
        """
        Gets the next sequence number by incrementing the class-level counter.

        Returns:
            The next sequence number.
        """
        with cls._lock:
            cls._counter += 1
            return cls._counter


class AutoSequenceGeneratorSingleton:
    """
    An auto sequence generator that uses a singleton pattern to ensure only one instance of the class is created.
    """
    _instance = None
    _lock = threading.Lock()

    _class_counter = 0
    _instance_counter = 0

    @classmethod
    def get_instance(cls):
        """
        Gets the singleton instance of the class.

        Returns:
            The singleton instance.
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        """
        Initializes the auto sequence generator singleton instance.
        """
        AutoSequenceGeneratorSingleton._class_counter += 1
        self.class_counter = AutoSequenceGeneratorSingleton._class_counter
        self.instance_counter = 0

    def get_next_instance_number(self):
        """
        Gets the next sequence number at the instance level by incrementing the instance-level counter.

        Returns:
            The next sequence number at the instance level.
        """
        with AutoSequenceGeneratorSingleton._lock:
            self.instance_counter += 1
            return self.instance_counter

    @classmethod
    def get_next_class_number(cls):
        """
        Gets the next sequence number at the class level by incrementing the class-level counter.

        Returns:
            The next sequence number at the class level.
        """
        with cls._lock:
            cls._class_counter += 1
            return cls._class_counter


if __name__ == '__main__':
    gen1 = AutoSequenceGeneratorThreaded()
    print(gen1.currval())
    print(gen1.nextval())
    print(gen1.nextval())
    #
    gen2 = AutoSequenceGeneratorThreaded()
    print(gen2.nextval())
    print(gen2.nextval())
    print(gen2.nextval())
    print(gen2.nextval_class())
    gen3 = AutoSequenceGeneratorSingleton()
    print(gen3.get_next_instance_number())

    conn = sqlite3.connect(":memory:")
    gen_4 = AutoSequenceGeneratorThreadedDb(conn)
    print(gen_4.get_next_sequence_number())
