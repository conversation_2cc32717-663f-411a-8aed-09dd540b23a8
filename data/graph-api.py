import os
import sys
import json
import webbrowser
from urllib.parse import urlparse, parse_qs

import msal
import requests

from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException, NoSuchElementException, TimeoutException


def main():
    CLIENT_ID = os.getenv("CLIENT_ID")
    TENANT_ID = os.getenv("TENANT_ID")
    AUTHORITY = "https://login.microsoftonline.com/66c22d86-1594-465a-9171-fd722d7bb1e4"
    CLIENT_SECRET = os.getenv("CLIENT_SECRET")

    app = msal.ConfidentialClientApplication(
        client_id=CLIENT_ID,
        client_credential=CLIENT_SECRET,
        authority=AUTHORITY, token_cache=None)

    scopes = ['Mail.Send']
    auth_code_flow_dict = app.initiate_auth_code_flow(scopes=scopes, redirect_uri="http://localhost:1951/getAToken")
    print(auth_code_flow_dict.keys())

    # At this stage I have the redirect uri
    # Open that URI in a browser to get the authentication code
    # driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
    with open(os.devnull, 'w') as devnull:
        original_stdout = sys.stdout
        sys.stdout = devnull
        service = ChromeService(executable_path=ChromeDriverManager().install())
        # Start the ChromeDriver service
        service.start()
        options = Options()

        # Create a Chrome web driver using the Service object
        driver = webdriver.Remote(service.service_url, options=options)
        sys.stdout = original_stdout

    try:
        driver.get(auth_code_flow_dict["auth_uri"])
        wait = WebDriverWait(driver, 30)
        redirect_url = "http://localhost:1951/getAToken"
        wait.until(EC.url_to_be(redirect_url))

        # element_id = "some-unique-element-id"
        # wait.until(EC.visibility_of_element_located((By.ID, element_id)))

    except TimeoutException as e:
        print("Timed out")
        print(driver.current_url)
    finally:
        redirect_url = driver.current_url
        parsed_url = urlparse(driver.current_url)
        auth_response = parse_qs(parsed_url.query)
        authorization_code = redirect_url.split("code=")[1]
        print(f"Authorization code: {authorization_code}")
        service.stop()

    # Now get access token from the authorization code
    # auth_response = dict of query value received from auth server
    print(f'state: {auth_code_flow_dict["state"]}')
    state = auth_code_flow_dict["state"]
    auth_code_flow_dict["state"] = [state]

    access_token = app.acquire_token_by_auth_code_flow(
        auth_code_flow=auth_code_flow_dict,
        auth_response=auth_response
    )
    print(f'Access Token: {access_token}')

    token_url = f"https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/token"
    resource = 'https://graph.microsoft.com'

    response = requests.post(
        token_url,
        data={
            "client_id": CLIENT_ID,
            "client_secret": CLIENT_SECRET,
            "grant_type": "client_credentials",
            "scope": resource + "/.default"
        }
    )
    print("Token response")
    print(response.status_code)
    print(response.json())

    # Extract the access token from the response
    access_token_delegated = response.json().get("access_token")

    # Use the access token to send an email
    headers = {
        "Authorization": "Bearer " + access_token['access_token'],
        "Content-Type": "application/json"
    }

    # Define the email message
    userId = "<EMAIL>"
    message = {
        "subject": "Test email from Microsoft Graph API",
        "body": {
            "contentType": "Text",
            "content": "This is a test email sent from Microsoft Graph API."
        },
        "toRecipients": [
            {
                "emailAddress": {
                    "address": userId
                }
            }
        ]
    }

    message_test = {
        "message": {
            "subject": "Meet for lunch?",
            "body": {
                "contentType": "HTML",
                "content": "The new cafeteria is open."
            },
            "toRecipients": [
                {
                    "emailAddress": {
                        "address": "<EMAIL>"
                    }
                }
            ],
            "ccRecipients": [
                {
                    "emailAddress": {
                        "address": "<EMAIL>"
                    }
                }
            ]
        },
        "saveToSentItems": "false"
    }

    # Send the email

    response = requests.post(
        f"https://graph.microsoft.com/v1.0/me/sendMail",
        headers=headers,
        data=json.dumps(message_test)
    )

    # Check the response status code
    if response.status_code == 202:
        print("Email sent successfully.")
    else:
        print(f"{userId}")
        print("Failed to send email. Response status code: " + str(response.status_code))
        print(response.json())

    result = None
    result = app.acquire_token_silent(scopes, account=None)

    if not result:
        # print("No suitable token exists in cache. Let's get a new one from Azure Active Directory.")
        result = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
        if "access_token" in result:
            userId = "<EMAIL>"
            endpoint = f'https://graph.microsoft.com/v1.0/users/{userId}/sendMail'
            toUserEmail = "***"
            email_msg = {'Message': {'Subject': "Test Sending Email from Python",
                                     'Body': {'ContentType': 'Text', 'Content': "This is a test email."},
                                     'ToRecipients': [{'EmailAddress': {'Address': toUserEmail}}]
                                     },
                         'SaveToSentItems': 'true'}
            r = requests.post(endpoint,
                              headers={'Authorization': 'Bearer ' + result['access_token']}, json=email_msg)
            if r.ok:
                print('Sent email successfully')
            else:
                print(r.json())
        else:
            print(result.get("error"))
            print(result.get("error_description"))
            print(result.get("correlation_id"))


if __name__ == '__main__':
    main()
