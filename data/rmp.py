import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from urllib.parse import urlparse, parse_qs

def main():
    service = ChromeService(executable_path=ChromeDriverManager().install())
    service.start()
    options = Options()
    options.headless = False

    driver = webdriver.Remote(service.service_url, options=options)

    driver.get("https://rmp.corecard.in")
    driver.find_element(by=By.NAME, value="UserName").send_keys('<EMAIL>')
    driver.find_element(by=By.NAME, value="Password").send_keys('password')
    driver.find_element(by=By.XPATH, value='//*[@id="root"]/div/section/div/form/div/div/div/div/div/div/div[4]/button').click()
    # key = input("Press enter to close the page")
    driver.get("https://rmp.corecard.in/app/all-version-management")
    driver.implicitly_wait(10)
    wait = WebDriverWait(driver, 20)
    element = wait.until(EC.presence_of_element_located((By.ID, "root")))
    print(element.text)
    html = driver.page_source
    # print(html)
    element = driver.find_element(by=By.XPATH, value='//*[@id="root"]')

    print(element)

    service.stop()



headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/109.0',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    # 'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'same-origin',
}

response = requests.get('https://rmp.corecard.in/app/all-version-management', headers=headers)
soup = BeautifulSoup(response.content, 'html.parser')


if __name__ == '__main__':
    main()