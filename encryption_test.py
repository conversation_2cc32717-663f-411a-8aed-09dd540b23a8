import base64
import binascii
import json
import os
import platform
import time

import dash
import pkcs11
from dash import dcc, html, Input, Output, clientside_callback, callback, State
import dash_bootstrap_components as dbc
import flask
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from dash.exceptions import PreventUpdate
from flask import Flask

import aiohttp
import asyncio
from aiohttp import BasicAuth
from pkcs11 import Attribute
from pkcs11.util.rsa import encode_rsa_public_key


async def fetch(session, url, auth):
    headers = {"Accept": "application/json"}
    async with session.get(url, headers=headers, auth=auth) as response:
        response_json = await response.json()
        return [response.status, response_json]


async def post(session, url, auth, payload):
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    async with session.post(url, headers=headers, auth=auth, data=payload) as response:
        response_json = await response.json()
        return [response.status, response_json]


async def get_details():
    auth = BasicAuth('<EMAIL>', 'Uhfct2OSLLS2HyNwf7yY4EC5')

    urls = [
        'https://corecard.atlassian.net/rest/api/3/myself',
        'https://corecard.atlassian.net/rest/api/3/permissions/project'
    ]

    payload = json.dumps({"permissions": ["BROWSE_PROJECTS"]})

    async with aiohttp.ClientSession() as session:
        tasks = [
            fetch(session, urls[0], auth),
            post(session, urls[1], auth, payload)
        ]
        responses = await asyncio.gather(*tasks)
        return responses


external_stylesheets = [
    {
        'href': "https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css",
        # SRI hash to ensure the integrity of the fetched resource
        'integrity': "sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH",
        'crossorigin': "anonymous",
        'rel': 'stylesheet',
    },
    {
        'src': 'https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap',
        'rel': 'stylesheet',
        'crossorigin': 'anonymous'
    },
    {
        'href': 'https://fonts.googleapis.com/css2?family=Roboto&display=swap',
        'rel': 'stylesheet',
        'crossorigin': 'anonymous'
    },
    {
        'href': 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://unpkg.com/aos@2.3.1/dist/aos.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://unpkg.com/tablefilter@latest/dist/tablefilter/style/tablefilter.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css',
        'rel': 'stylesheet',
    },
    {
        'href': 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css',
        'rel': 'stylesheet',
    },
    # This is used by dash mantime component
    "https://unpkg.com/@mantine/dates@7/styles.css",
    "https://unpkg.com/@mantine/code-highlight@7/styles.css",
    "https://unpkg.com/@mantine/charts@7/styles.css",
    "https://unpkg.com/@mantine/carousel@7/styles.css",
    "https://unpkg.com/@mantine/notifications@7/styles.css",
    "https://unpkg.com/@mantine/nprogress@7/styles.css",

]

# Add all the external javascripts here
external_scripts = [
    {
        'src': 'https://kit.fontawesome.com/4e20f97c7e.js',
        'crossorigin': 'anonymous'
    },
    {
        'src': 'https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js',
        'crossorigin': 'anonymous'
    },
    {
        'src': 'https://unpkg.com/aos@2.3.1/dist/aos.js',
        'crossorigin': 'anonymous'
    },
    # {
    #     'src': "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js",
    #     'crossorigin': 'anonymous'
    # },
    # {
    #     'src': "https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js",
    #     'crossorigin': 'anonymous'
    # },
    {
        'src': "https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js",
        'crossorigin': 'anonymous'
    },
    {
        'src': "https://code.angularjs.org/1.7.9/angular.min.js"
    },
    {
        'src': "https://unpkg.com/tablefilter@latest/dist/tablefilter/tablefilter.js"
    },
    {
        'src': "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"
    },
    {
        'src': "https://cdn.jsdelivr.net/npm/marked/marked.min.js"
    },
    {
        'src': "https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"
    },
    {
        'src': "https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js",
        'crossorigin': 'anonymous'

    },
    {
        'src': "https://cdn.rawgit.com/kjur/jsrsasign/8.0.12/jsrsasign-all-min.js",
        'crossorigin': 'anonymous'

    },
]

server = Flask(__name__)

app = dash.Dash(
    __name__,
    external_stylesheets=external_stylesheets,
    external_scripts=external_scripts,
    server=server
)
lib = pkcs11.lib(os.environ['PKCS11_MODULE'])
token = lib.get_token(token_label='My token 1')

with token.open(user_pin="123456", rw=True) as hsm_session:
    keys = list(hsm_session.get_objects({
        Attribute.LABEL: 'holalu',
    }))

    public_key = keys[0]

    modulus = base64.b64encode(public_key[Attribute.MODULUS]).decode('utf-8')
    modulus_raw = public_key[Attribute.MODULUS]
    public_exponent = base64.b64encode(public_key[Attribute.PUBLIC_EXPONENT]).decode('utf-8')
    public_exponent_raw = public_key[Attribute.PUBLIC_EXPONENT]
    private_key = keys[1]
    print("modulus")
    print(public_key[Attribute.MODULUS])
    print(f"{modulus}")
    print(base64.b64encode(public_key[Attribute.MODULUS]))

    public_key_der = encode_rsa_public_key(public_key)
    print(f"DER formattted key")
    print(public_key_der)
    print(base64.b64encode(public_key_der).decode('latin-1'))


app.layout = html.Div(
    html.Ul(
        children=[
            html.Li(children=[
                dcc.Input(type='password', id="id-login-passwd-main", placeholder="Password",
                          className="input-creds", debounce=True, persistence=True),
                html.Span(className='focus-input-creds'),
                html.Span(children=[html.I(className="fa fa-lock"), ], className="symbol-input-creds"),
                html.Span(
                    children=[
                        html.I(className="fa fa-eye", id="toggle-password")
                    ],
                    className="toggle-password-icon"
                ),
            ],
                className='wrap-input-creds'
            ),
            html.Button('Submit', id='submit-btn', n_clicks=0),
            dcc.Input(id='output', value=""),
            dcc.Input(id='output-softhsm', value=""),
            dcc.Input(
                id="modulus",
                value=modulus,
                disabled=True
            ),
            dcc.Input(id="public_exponent", value=public_exponent, disabled=True),
            html.Div(id='decrypt'),
            html.Div(id='decrypt-softhsm'),
            html.Button('Submit', id='show-btn', n_clicks=0),
        ],
        className="login-container-ul"
    ),
)


@callback(
    Output("decrypt", "children"),
    Output("decrypt-softhsm", "children"),
    Input("show-btn", "n_clicks"),
    State("output", "value"),
    State("output-softhsm", "value"),
    State("id-login-passwd-main", "value"),
    prevent_initial_callbacks=True
)
def show_plaintext(
        n_clicks, encrypted_text, encrypted_text_hsm,
        original_text
):
    if n_clicks > 0:
        print(f"js passed: {encrypted_text_hsm}")
        private_key_pem = b"""-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFLTBXBgkqhkiG9w0BBQ0wSjApBgkqhkiG9w0BBQwwHAQIpRhMRXvVuScCAggA
MAwGCCqGSIb3DQIJBQAwHQYJYIZIAWUDBAEqBBBzKKFvXLYs4FM0GZPXc1XOBIIE
0Eaycp4I3J5z9v3L8m0g7cAgz1dPlUfIUwFHrlX1oSHqkKqeAwd39Oz/+iaAIarf
E9uJqNlG+cq01oBykiB4OsLbZBsN4CcIj3IYgO8bjU/fCMfPQ88U+PyY952ONqdo
9v0LmAEWHmMhVQEHNKe6be5RtOT30sZOzHo6BoPv58gNTJnjeLHJRA+YM4kKgXpc
4y6gtkWFCqEyT4rXTVIuXbfQVtS8/AGH6DBo5IgpzA1KbzHI8Bwdu2RtQ/u6eJMZ
XYtvL4Qkn8fl/v9meTgML75gG9NWYPLXUAuIVIo590iKgkOavFJd2KsdheS+W5so
DQjCbRw7HRAnT2Qq22LtDOibD22oK1Wp603XY5Xtm/Ghx9ug85pqa0oUmQ37CK7B
jcAAdyBb8Uzf9bDc6qGoZV+PPKBHwYQ1op8MHTZ00PQIdz2hchhO7HpUWJHMqE+K
i7LcOgPO+eRvZN5sjPMEai3+2vhDyMaiH/FBscDQ4Cgtwp5WpuSUgwttT3NeZaF7
nsnarL2FdakhT46/4l0nhNtnJrbjvqcglV3DTvd85WHm9bv6d750MlUl44NlKisc
R8I7an75v8o6YJvrxmOYG5f6o/nwmSLOl9kPpo06F9bJAfEnTFeMfP7WkBE244wm
TnUiMNJkV9C3nHlSVbTVEfwn4FtQfVvnnNm5Ht5zZAUMLfgsHyqUxlSrNUyecYQn
15+E26m+Qybd44ePKlFc2+KcIgSGo9vSWE3uNq2OiGdfruuBsfPU/USWW0SV/24s
dEO0jm5jJSLMXVVJXKwzmMamTypvri+qRfoXDQNyfemQOjRX4bu6Bd4TAHfkv3+x
k4tVQQ2VFCHvUA7eLVJHlLSuTskaaIDOTlrv9SNcDKew0GC7FANr/bf5hZldbU+c
7Ah+2pZPtMU/j6+Sbk8zL5QzPbtLtMgavhyVqdcD5VLR+OUX83Ay5A8ybAm/1AJu
t+CukSZj8yaKJppvMRndonWzOuiAUFYYJccmr6ffcuhFgNjo6Wkwgjpm3zuwJlRY
30dyZLQIhfJ3TqnyhvbKVgFZ3yvyF/LhcS0H5xTbEhqw1W/c5r/LfDQNtoldaQhr
pMXcOvxQHDhVy6dNcIxvM3evoC0nDqNK+FaeBx05UsyjChyClYMtSE79SfPWFxvo
4uOOecKc9EmNwYH1oxAIWiWc4l/NLGSlKigSyLjAuHtNsmFbS9z0jDn/cJxtZJie
XFS8mueifU7Rq7dJX2pCCmP+hj2O/afh0DuX4VOjCZKxYIBav5IlucUXnHtC81+U
HNgGBah1HzDgsWLFaoTk6mb7ShdYQPFe9txazu5oGxNCPr+Rkr2qOXP3W3ZQ7a8T
a6hn/D0DfX51WSJVf3i2JlOkWByCj7nomn5YgoFECxnWaT07SNuDBz9nm+DH4I2A
EOUO/MS/p4OJoTY4TJmT7AUd54nPq7nBk3VKHxpA5Gj6417UHqYZshdfXvFYSGjl
qFMK2rRShLkiBvKhq9cKDlkvKlEZTDTcMcueAdgecXnd/Q9sch6Q5NVP8UkdLcxI
GY06UzK+YekYXXYTqv+jhNWaFTflieYdvk6GJNviU8tEsDMuskfo4CYWXEGdi80Y
kZDgRNYcJLTJmUMZypLqJKTaHr+roNF6oZ8+pOujhTO9
-----END ENCRYPTED PRIVATE KEY-----"""
        private_key = serialization.load_pem_private_key(
            private_key_pem,
            password=b'password',
            backend=default_backend()
        )

        decrypted_password = private_key.decrypt(
            bytes.fromhex(encrypted_text),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )


        start_time = time.time()
        if platform.system() == 'Windows':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        responses = asyncio.run(get_details())
        print(f'Time taken: {time.time() - start_time}')
        for response in responses:
            print(response)

        print(f"value to be encrypted/decrypted: {original_text}")
        with token.open(user_pin="123456", rw=True) as hsm_session:
            key_private_gen = hsm_session.get_objects({
                Attribute.LABEL: 'holalu',
                Attribute.PRIVATE: True
            })
            key_private = list(key_private_gen)

            key_public = list(hsm_session.get_objects({
                Attribute.LABEL: 'holalu',
                Attribute.PRIVATE: False
            }))

            encr = key_public[0].encrypt(original_text)
            print(f"Encrypt text from hsm: {encr}")
            print(f"base64: {base64.b64encode(encr)}")
            print(f"decoded text: {key_private[0].decrypt(encr)}")

            # encrypted_text_hsm_encode = base64.b64encode(encrypted_text_hsm.encode('latin-1'))
            # print(encrypted_text_hsm_encode)
            print(f"Encrypted test from javascript: {encrypted_text_hsm}")
            print(f"base64 decoded")
            print(base64.b64decode(encrypted_text_hsm))
            x = key_private[0].decrypt(encrypted_text_hsm)
            print(x)

            print("binascii")
            h = binascii.hexlify(b"Hello world !!")
            print(h)
            h = binascii.hexlify("Hello world !!".encode('latin-1'))
            print(h)

            k = key_private[0].decrypt(encrypted_text_hsm)

        return decrypted_password.decode('utf-8 '), "check"
    else:
        raise PreventUpdate


@app.server.route('/decrypt_password', methods=['POST'])
def decrypt_password():
    print(flask.request.data)
    return {"test": "test"}
    data = flask.request.get_json()
    encrypted_password = data['encrypted_password']
    print(encrypted_password)
    private_key_pem = b"""-----BEGIN ENCRYPTED PRIVATE KEY-----MIIFLTBXBgkqhkiG9w0BBQ0wSjApBgkqhkiG9w0BBQwwHAQIpRhMRXvVuScCAggAMAwGCCqGSIb3DQIJBQAwHQYJYIZIAWUDBAEqBBBzKKFvXLYs4FM0GZPXc1XOBIIE0Eaycp4I3J5z9v3L8m0g7cAgz1dPlUfIUwFHrlX1oSHqkKqeAwd39Oz/+iaAIarfE9uJqNlG+cq01oBykiB4OsLbZBsN4CcIj3IYgO8bjU/fCMfPQ88U+PyY952ONqdo9v0LmAEWHmMhVQEHNKe6be5RtOT30sZOzHo6BoPv58gNTJnjeLHJRA+YM4kKgXpc4y6gtkWFCqEyT4rXTVIuXbfQVtS8/AGH6DBo5IgpzA1KbzHI8Bwdu2RtQ/u6eJMZXYtvL4Qkn8fl/v9meTgML75gG9NWYPLXUAuIVIo590iKgkOavFJd2KsdheS+W5soDQjCbRw7HRAnT2Qq22LtDOibD22oK1Wp603XY5Xtm/Ghx9ug85pqa0oUmQ37CK7BjcAAdyBb8Uzf9bDc6qGoZV+PPKBHwYQ1op8MHTZ00PQIdz2hchhO7HpUWJHMqE+Ki7LcOgPO+eRvZN5sjPMEai3+2vhDyMaiH/FBscDQ4Cgtwp5WpuSUgwttT3NeZaF7nsnarL2FdakhT46/4l0nhNtnJrbjvqcglV3DTvd85WHm9bv6d750MlUl44NlKiscR8I7an75v8o6YJvrxmOYG5f6o/nwmSLOl9kPpo06F9bJAfEnTFeMfP7WkBE244wmTnUiMNJkV9C3nHlSVbTVEfwn4FtQfVvnnNm5Ht5zZAUMLfgsHyqUxlSrNUyecYQn15+E26m+Qybd44ePKlFc2+KcIgSGo9vSWE3uNq2OiGdfruuBsfPU/USWW0SV/24sdEO0jm5jJSLMXVVJXKwzmMamTypvri+qRfoXDQNyfemQOjRX4bu6Bd4TAHfkv3+xk4tVQQ2VFCHvUA7eLVJHlLSuTskaaIDOTlrv9SNcDKew0GC7FANr/bf5hZldbU+c7Ah+2pZPtMU/j6+Sbk8zL5QzPbtLtMgavhyVqdcD5VLR+OUX83Ay5A8ybAm/1AJut+CukSZj8yaKJppvMRndonWzOuiAUFYYJccmr6ffcuhFgNjo6Wkwgjpm3zuwJlRY30dyZLQIhfJ3TqnyhvbKVgFZ3yvyF/LhcS0H5xTbEhqw1W/c5r/LfDQNtoldaQhrpMXcOvxQHDhVy6dNcIxvM3evoC0nDqNK+FaeBx05UsyjChyClYMtSE79SfPWFxvo4uOOecKc9EmNwYH1oxAIWiWc4l/NLGSlKigSyLjAuHtNsmFbS9z0jDn/cJxtZJieXFS8mueifU7Rq7dJX2pCCmP+hj2O/afh0DuX4VOjCZKxYIBav5IlucUXnHtC81+UHNgGBah1HzDgsWLFaoTk6mb7ShdYQPFe9txazu5oGxNCPr+Rkr2qOXP3W3ZQ7a8Ta6hn/D0DfX51WSJVf3i2JlOkWByCj7nomn5YgoFECxnWaT07SNuDBz9nm+DH4I2AEOUO/MS/p4OJoTY4TJmT7AUd54nPq7nBk3VKHxpA5Gj6417UHqYZshdfXvFYSGjlqFMK2rRShLkiBvKhq9cKDlkvKlEZTDTcMcueAdgecXnd/Q9sch6Q5NVP8UkdLcxIGY06UzK+YekYXXYTqv+jhNWaFTflieYdvk6GJNviU8tEsDMuskfo4CYWXEGdi80YkZDgRNYcJLTJmUMZypLqJKTaHr+roNF6oZ8+pOujhTO9-----END ENCRYPTED PRIVATE KEY-----"""
    private_key = serialization.load_pem_private_key(
        private_key_pem,
        password=b'password',
        backend=default_backend()
    )
    print(private_key)
    decrypted_password = private_key.decrypt(
        bytes.fromhex(encrypted_password),
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )
    print(decrypted_password)
    return {'decrypted_password': decrypted_password.decode('utf-8')}, 200


clientside_callback(
    """
    function(n_clicks, password) {        
        if (n_clicks > 0) {
            async function handlePasswordEncryption(password) {                
                if (!password) return 'Operation Failed';
                
                try {
                    const encryptedPassword = await encryptPassword(password);                    
                    return encryptedPassword;                    
                } catch (err) {
                    console.error('Encryption failed:', err);
                    return '';
                }
            }
            return handlePasswordEncryption(password);
        }
    }
    """,
    Output('output', 'value'),
    Input('submit-btn', 'n_clicks'),
    Input('id-login-passwd-main', 'value'),
    prevent_initial_call=True
)

clientside_callback(
    """
    function(input_value) {
        var pwd = document.getElementById('id-login-passwd-main');        
        var toggleIcon = document.getElementById('toggle-password');
        console.log("This is working");
        if (pwd.type === 'password') {
            pwd.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            pwd.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    """,
    Output('toggle-password', 'n_clicks'),
    Input('toggle-password', 'n_clicks'),
    prevent_initial_call=True
)

clientside_callback(
    """
    function(n_clicks, password, modulus, public_exponent) { 
        console.log(password);
        console.log(modulus);     
        console.log(public_exponent);
        console.log("number of clicks:" + n_clicks);
        
        if (n_clicks > 0) {
            async function handlePasswordEncryption(modulus, public_exponent, password) {                
                if (!password) return 'Operation Failed';

                try {
                    const encryptedPassword = await encryptWithPublicKey(modulus, public_exponent, password);
                    console.log("encrypted = " +  encryptedPassword);                   
                    return encryptedPassword;                    
                } catch (err) {
                    console.error('Encryption failed:', err);
                    return '';
                }
            }
            return handlePasswordEncryption(modulus, public_exponent, password);
        }
        
    }
    """,
    Output('output-softhsm', 'value'),
    Input('submit-btn', 'n_clicks'),
    State('id-login-passwd-main', 'value'),
    State(component_id="modulus", component_property="value"),
    State(component_id="public_exponent", component_property="value"),
    prevent_initial_call=True
)

if __name__ == '__main__':
    app.run_server(debug=True)
