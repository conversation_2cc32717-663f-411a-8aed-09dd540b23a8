<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Timesheet Design Demo</title>
    <link rel="stylesheet" href="assets/css/modern_timesheet.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@iconify/iconify@3.1.1/dist/iconify.min.css">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        /* Demo header */
        .demo-header {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .demo-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .demo-header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        /* Mock AG Grid */
        .mock-ag-grid {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            height: 400px;
        }
        
        .mock-grid-header {
            background: #fafafa;
            border-bottom: 1px solid #d9d9d9;
            padding: 12px 16px;
            font-weight: 600;
            color: #262626;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 16px;
        }
        
        .mock-grid-row {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 16px;
            transition: background 0.2s;
        }
        
        .mock-grid-row:hover {
            background: #f5f5f5;
        }
        
        .mock-grid-row:nth-child(even) {
            background: #fafafa;
        }
        
        /* Mock Chart */
        .mock-chart {
            background: white;
            border-radius: 8px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            color: #8c8c8c;
            font-size: 18px;
        }
        
        /* Demo controls */
        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            z-index: 1000;
        }
        
        .demo-controls h3 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #262626;
        }
        
        .demo-controls button {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .demo-controls button:hover {
            background: #f5f5f5;
            border-color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>🎨 Modern Timesheet Design</h1>
        <p>Redesigned WD Timesheet with modern UI/UX patterns</p>
    </div>

    <div class="demo-controls">
        <h3>Demo Controls</h3>
        <button onclick="toggleSidebar()">Toggle Sidebar</button>
        <button onclick="switchTab('data')">Data Table</button>
        <button onclick="switchTab('chart')">Effort Chart</button>
        <button onclick="switchTab('analysis')">Analysis</button>
        <button onclick="simulateMobile()">Mobile View</button>
    </div>

    <div class="modern-timesheet-layout" id="timesheet-layout">
        <!-- Modern Sidebar -->
        <div class="modern-sidebar" id="sidebar">
            <div class="sidebar-header">
                <iconify-icon icon="mdi:filter-variant" class="sidebar-icon"></iconify-icon>
                <h3 class="sidebar-title">Filters & Controls</h3>
            </div>
            
            <div class="sidebar-section">
                <div class="section-header">
                    <iconify-icon icon="mdi:calendar-range" class="section-icon"></iconify-icon>
                    <h4 class="section-title">Date Range</h4>
                </div>
                <div class="form-group">
                    <div class="modern-checkbox">
                        <div class="checkbox-label">
                            <iconify-icon icon="mdi:calendar-check" class="checkbox-icon"></iconify-icon>
                            <span>Set End Date = Start Date</span>
                        </div>
                    </div>
                    <div class="modern-date-picker" style="padding: 12px; background: white; border: 1px solid #d9d9d9; border-radius: 4px;">
                        📅 Date Range Picker
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="section-header">
                    <iconify-icon icon="mdi:folder-multiple" class="section-icon"></iconify-icon>
                    <h4 class="section-title">Project Selection</h4>
                </div>
                <div class="form-group">
                    <label class="form-label">Projects</label>
                    <div class="modern-dropdown" style="padding: 12px; background: white; border: 1px solid #d9d9d9; border-radius: 4px;">
                        📁 Project Dropdown
                    </div>
                    <label class="form-label">Issue Type</label>
                    <div class="modern-dropdown" style="padding: 12px; background: white; border: 1px solid #d9d9d9; border-radius: 4px;">
                        🏷️ Issue Type Dropdown
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="modern-button primary">
                        <iconify-icon icon="mdi:magnify" class="button-icon"></iconify-icon>
                        Get Details
                    </button>
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="section-header">
                    <iconify-icon icon="mdi:cloud-upload" class="section-icon"></iconify-icon>
                    <h4 class="section-title">File Upload</h4>
                </div>
                <div class="form-group">
                    <div class="modern-upload">
                        <iconify-icon icon="mdi:cloud-upload-outline" class="upload-icon"></iconify-icon>
                        <div class="upload-text">Drag & Drop or Click</div>
                        <div class="upload-subtext">CSV, XLS, XLSX files only</div>
                    </div>
                    <div class="upload-actions">
                        <button class="modern-button success">
                            <iconify-icon icon="mdi:play" class="button-icon"></iconify-icon>
                            Process
                        </button>
                        <button class="modern-button secondary">
                            <iconify-icon icon="mdi:close" class="button-icon"></iconify-icon>
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Main Content -->
        <div class="modern-main-content" id="main-content">
            <div class="content-header">
                <button class="modern-toggle-button" id="toggle-btn" onclick="toggleSidebar()">
                    <iconify-icon icon="mdi:menu" class="toggle-icon"></iconify-icon>
                </button>
                
                <div class="page-title-section">
                    <iconify-icon icon="mdi:clock-time-four" class="title-icon"></iconify-icon>
                    <h1 class="page-title">WD Timesheet</h1>
                </div>
                
                <div class="header-actions">
                    <button class="modern-button secondary">
                        <iconify-icon icon="mdi:download" class="button-icon"></iconify-icon>
                        Export Data
                    </button>
                </div>
            </div>
            
            <div class="content-body">
                <div class="tab-navigation">
                    <button class="tab-button active" onclick="switchTab('data')">
                        <iconify-icon icon="mdi:table" class="tab-icon"></iconify-icon>
                        Data Table
                    </button>
                    <button class="tab-button" onclick="switchTab('chart')">
                        <iconify-icon icon="mdi:chart-line" class="tab-icon"></iconify-icon>
                        Effort Chart
                    </button>
                    <button class="tab-button" onclick="switchTab('analysis')">
                        <iconify-icon icon="mdi:chart-pie" class="tab-icon"></iconify-icon>
                        Subtask Analysis
                    </button>
                </div>
                
                <div class="tab-content">
                    <div class="tab-panel active" id="data-panel">
                        <div class="panel-header">
                            <div>
                                <h3 class="panel-title">Timesheet Data</h3>
                                <p class="panel-description">View and manage your timesheet entries</p>
                            </div>
                            <button class="modern-button primary">
                                <iconify-icon icon="mdi:download" class="button-icon"></iconify-icon>
                                Download CSV
                            </button>
                        </div>
                        <div class="data-grid-container">
                            <div class="mock-ag-grid">
                                <div class="mock-grid-header">
                                    <div>Project</div>
                                    <div>Issue Key</div>
                                    <div>Hours</div>
                                    <div>Date</div>
                                </div>
                                <div class="mock-grid-row">
                                    <div>Project Alpha</div>
                                    <div>PROJ-123</div>
                                    <div>8.0</div>
                                    <div>2024-01-15</div>
                                </div>
                                <div class="mock-grid-row">
                                    <div>Project Beta</div>
                                    <div>PROJ-124</div>
                                    <div>6.5</div>
                                    <div>2024-01-15</div>
                                </div>
                                <div class="mock-grid-row">
                                    <div>Project Gamma</div>
                                    <div>PROJ-125</div>
                                    <div>4.0</div>
                                    <div>2024-01-16</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" id="chart-panel">
                        <div class="panel-header">
                            <div>
                                <h3 class="panel-title">Effort Spent Analysis</h3>
                                <p class="panel-description">Visual representation of time spent across projects</p>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div class="mock-chart">
                                📊 Interactive Chart Would Appear Here
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-panel" id="analysis-panel">
                        <div class="panel-header">
                            <div>
                                <h3 class="panel-title">Subtask Effort Distribution</h3>
                                <p class="panel-description">Breakdown of effort by issue type and subtasks</p>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div class="mock-chart">
                                🥧 Pie Chart Analysis Would Appear Here
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
    <script>
        let sidebarOpen = true;
        let isMobile = window.innerWidth <= 768;

        function toggleSidebar() {
            sidebarOpen = !sidebarOpen;
            updateLayout();
        }

        function updateLayout() {
            const layout = document.getElementById('timesheet-layout');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const toggleBtn = document.getElementById('toggle-btn');

            if (isMobile) {
                if (sidebarOpen) {
                    sidebar.classList.add('mobile-open');
                    sidebar.classList.remove('hidden');
                } else {
                    sidebar.classList.remove('mobile-open');
                    sidebar.classList.add('hidden');
                }
            } else {
                if (sidebarOpen) {
                    layout.classList.remove('sidebar-collapsed');
                    sidebar.classList.remove('hidden');
                    mainContent.classList.remove('expanded');
                    toggleBtn.classList.remove('active');
                } else {
                    layout.classList.add('sidebar-collapsed');
                    sidebar.classList.add('hidden');
                    mainContent.classList.add('expanded');
                    toggleBtn.classList.add('active');
                }
            }
        }

        function switchTab(tabName) {
            // Remove active from all tabs and panels
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // Add active to selected tab and panel
            const tabMap = {
                'data': { button: 0, panel: 'data-panel' },
                'chart': { button: 1, panel: 'chart-panel' },
                'analysis': { button: 2, panel: 'analysis-panel' }
            };

            if (tabMap[tabName]) {
                document.querySelectorAll('.tab-button')[tabMap[tabName].button].classList.add('active');
                document.getElementById(tabMap[tabName].panel).classList.add('active');
            }
        }

        function simulateMobile() {
            isMobile = !isMobile;
            document.body.style.maxWidth = isMobile ? '768px' : 'none';
            updateLayout();
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            isMobile = window.innerWidth <= 768;
            updateLayout();
        });

        // Initialize
        updateLayout();
    </script>
</body>
</html>
